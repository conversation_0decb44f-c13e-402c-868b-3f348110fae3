<?php
/**
 * Security Management System for Redco Optimizer
 * 
 * Enhanced security features and production hardening
 * 
 * @package RedcoOptimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Security_Manager {
    
    /**
     * Security levels
     */
    const LEVEL_LOW = 'low';
    const LEVEL_MEDIUM = 'medium';
    const LEVEL_HIGH = 'high';
    const LEVEL_STRICT = 'strict';
    
    /**
     * Failed login attempts tracking
     */
    private static $failed_attempts = array();
    
    /**
     * Security configuration
     */
    private static $config = array(
        'security_level' => self::LEVEL_LOW,
        'max_failed_attempts' => 10,
        'lockout_duration' => 900, // 15 minutes
        'enable_file_monitoring' => false,
        'enable_request_filtering' => false,
        'enable_admin_protection' => false
    );
    
    /**
     * Initialize security manager
     */
    public static function init() {
        // Safety check - only initialize if WordPress is fully loaded
        if (!function_exists('wp_verify_nonce') || !function_exists('current_user_can')) {
            return;
        }

        // Load configuration
        self::load_config();
        
        // Add security hooks
        add_action('wp_login_failed', array(__CLASS__, 'handle_failed_login'));
        add_action('wp_login', array(__CLASS__, 'handle_successful_login'), 10, 2);
        add_filter('authenticate', array(__CLASS__, 'check_login_attempts'), 30, 3);
        
        // Admin security check
        if (self::$config['enable_admin_protection']) {
            add_action('admin_init', array(__CLASS__, 'admin_security_check'));
            add_action('wp_login', array(__CLASS__, 'track_admin_login'), 10, 2);
            add_filter('login_redirect', array(__CLASS__, 'secure_admin_redirect'), 10, 3);
        }

        // PERFORMANCE FIX: Only enable request filtering when explicitly needed
        if (self::$config['enable_request_filtering'] &&
            (is_admin() || is_user_logged_in() || self::is_high_risk_request())) {
            add_action('init', array(__CLASS__, 'filter_malicious_requests'), 1);
            add_action('wp_loaded', array(__CLASS__, 'advanced_request_filtering'));
        }
        
        // Add file monitoring
        if (self::$config['enable_file_monitoring']) {
            add_action('redco_security_scan', array(__CLASS__, 'scan_core_files'));
            add_action('redco_file_integrity_check', array(__CLASS__, 'check_file_integrity'));

            // Schedule security scan
            if (!wp_next_scheduled('redco_security_scan')) {
                wp_schedule_event(time(), 'daily', 'redco_security_scan');
            }

            // Schedule file integrity check
            if (!wp_next_scheduled('redco_file_integrity_check')) {
                wp_schedule_event(time(), 'hourly', 'redco_file_integrity_check');
            }
        }
        
        // Add security headers
        add_action('send_headers', array(__CLASS__, 'add_security_headers'));
        
        // Add nonce validation for AJAX (temporarily disabled for testing)
        // add_action('wp_ajax_redco_*', array(__CLASS__, 'validate_ajax_nonce'), 1);
        // add_action('wp_ajax_nopriv_redco_*', array(__CLASS__, 'validate_ajax_nonce'), 1);
    }
    
    /**
     * Handle failed login attempt
     * 
     * @param string $username Username that failed
     */
    public static function handle_failed_login($username) {
        $ip = self::get_client_ip();
        $key = 'failed_login_' . md5($ip . $username);
        
        $attempts = get_transient($key) ?: 0;
        $attempts++;
        
        set_transient($key, $attempts, self::$config['lockout_duration']);
        
        if ($attempts >= self::$config['max_failed_attempts']) {
            // Lock out the IP/username combination
            $lockout_key = 'lockout_' . md5($ip . $username);
            set_transient($lockout_key, time(), self::$config['lockout_duration']);
            
            Redco_Error_Handler::warning(
                "Login lockout triggered for IP: {$ip}, Username: {$username}",
                Redco_Error_Handler::CONTEXT_SECURITY,
                array(
                    'ip' => $ip,
                    'username' => $username,
                    'attempts' => $attempts
                )
            );
        }
        
        Redco_Error_Handler::notice(
            "Failed login attempt #{$attempts} for {$username} from {$ip}",
            Redco_Error_Handler::CONTEXT_SECURITY,
            array(
                'ip' => $ip,
                'username' => $username,
                'attempts' => $attempts
            )
        );
    }
    
    /**
     * Handle successful login
     * 
     * @param string $user_login Username
     * @param WP_User $user User object
     */
    public static function handle_successful_login($user_login, $user) {
        $ip = self::get_client_ip();
        
        // Clear failed attempts on successful login
        $key = 'failed_login_' . md5($ip . $user_login);
        delete_transient($key);
        
        $lockout_key = 'lockout_' . md5($ip . $user_login);
        delete_transient($lockout_key);
        
        Redco_Error_Handler::info(
            "Successful login for {$user_login} from {$ip}",
            Redco_Error_Handler::CONTEXT_SECURITY,
            array(
                'ip' => $ip,
                'username' => $user_login,
                'user_id' => $user->ID
            )
        );
    }
    
    /**
     * Check login attempts before authentication
     * 
     * @param WP_User|WP_Error|null $user User object or error
     * @param string $username Username
     * @param string $password Password
     * @return WP_User|WP_Error User object or error
     */
    public static function check_login_attempts($user, $username, $password) {
        if (empty($username) || empty($password)) {
            return $user;
        }
        
        $ip = self::get_client_ip();
        $lockout_key = 'lockout_' . md5($ip . $username);
        
        if (get_transient($lockout_key)) {
            $error = new WP_Error(
                'login_locked',
                'Too many failed login attempts. Please try again later.'
            );
            
            Redco_Error_Handler::warning(
                "Blocked login attempt during lockout for {$username} from {$ip}",
                Redco_Error_Handler::CONTEXT_SECURITY,
                array(
                    'ip' => $ip,
                    'username' => $username
                )
            );
            
            return $error;
        }
        
        return $user;
    }
    
    /**
     * Admin security check
     */
    public static function admin_security_check() {
        // Check for suspicious admin activity
        if (current_user_can('manage_options')) {
            $suspicious_actions = array(
                'delete_plugins',
                'delete_themes',
                'edit_plugins',
                'edit_themes',
                'update-core',
                'do-core-upgrade'
            );

            $current_action = $_GET['action'] ?? $_POST['action'] ?? '';

            if (in_array($current_action, $suspicious_actions)) {
                // Log the activity
                Redco_Error_Handler::warning(
                    "Suspicious admin action: {$current_action}",
                    Redco_Error_Handler::CONTEXT_SECURITY,
                    array(
                        'action' => $current_action,
                        'user_id' => get_current_user_id(),
                        'ip' => self::get_client_ip(),
                        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                        'referer' => $_SERVER['HTTP_REFERER'] ?? ''
                    )
                );

                // Check if this is a high-risk action
                $high_risk_actions = array('delete_plugins', 'delete_themes');
                if (in_array($current_action, $high_risk_actions)) {
                    // Additional verification for high-risk actions
                    self::verify_admin_action($current_action);
                }
            }
        }

        // Check for admin session security
        self::check_admin_session_security();
    }

    /**
     * Track admin login for enhanced protection
     */
    public static function track_admin_login($user_login, $user) {
        if (user_can($user, 'manage_options')) {
            $login_data = array(
                'user_id' => $user->ID,
                'user_login' => $user_login,
                'ip' => self::get_client_ip(),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'timestamp' => current_time('timestamp'),
                'session_id' => session_id()
            );

            // Store admin login data
            $admin_logins = get_option('redco_admin_logins', array());
            $admin_logins[] = $login_data;

            // Keep only last 50 logins
            if (count($admin_logins) > 50) {
                $admin_logins = array_slice($admin_logins, -50);
            }

            update_option('redco_admin_logins', $admin_logins);

            // Log admin login
            Redco_Error_Handler::info(
                "Admin user logged in: {$user_login}",
                Redco_Error_Handler::CONTEXT_SECURITY,
                $login_data
            );
        }
    }

    /**
     * Secure admin redirect
     */
    public static function secure_admin_redirect($redirect_to, $request, $user) {
        if (is_wp_error($user)) {
            return $redirect_to;
        }

        if (user_can($user, 'manage_options')) {
            // Ensure admin users are redirected to secure admin area
            if (strpos($redirect_to, admin_url()) !== 0) {
                $redirect_to = admin_url();
            }

            // Add security token to admin URLs
            $redirect_to = add_query_arg('redco_admin_token', wp_create_nonce('redco_admin_access'), $redirect_to);
        }

        return $redirect_to;
    }

    /**
     * Verify admin action
     */
    private static function verify_admin_action($action) {
        $nonce_action = 'redco_verify_' . $action;

        if (!wp_verify_nonce($_REQUEST['_wpnonce'] ?? '', $nonce_action)) {
            Redco_Error_Handler::critical(
                "Admin action blocked - invalid nonce: {$action}",
                Redco_Error_Handler::CONTEXT_SECURITY,
                array(
                    'action' => $action,
                    'user_id' => get_current_user_id(),
                    'ip' => self::get_client_ip()
                )
            );

            wp_die(
                __('Security verification failed. This action has been blocked for security reasons.', 'redco-optimizer'),
                __('Security Error', 'redco-optimizer'),
                array('response' => 403)
            );
        }
    }

    /**
     * Check admin session security
     */
    private static function check_admin_session_security() {
        if (!current_user_can('manage_options')) {
            return;
        }

        $current_ip = self::get_client_ip();
        $session_ip = get_user_meta(get_current_user_id(), 'redco_session_ip', true);

        if ($session_ip && $session_ip !== $current_ip) {
            // IP changed during admin session
            Redco_Error_Handler::warning(
                "Admin session IP changed",
                Redco_Error_Handler::CONTEXT_SECURITY,
                array(
                    'user_id' => get_current_user_id(),
                    'old_ip' => $session_ip,
                    'new_ip' => $current_ip
                )
            );
        }

        // Update session IP
        update_user_meta(get_current_user_id(), 'redco_session_ip', $current_ip);
    }
    
    /**
     * Filter malicious requests
     */
    public static function filter_malicious_requests() {
        // Skip filtering for legitimate WordPress requests
        if (self::is_legitimate_request()) {
            return;
        }

        $request_uri = $_SERVER['REQUEST_URI'] ?? '';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $query_string = $_SERVER['QUERY_STRING'] ?? '';
        $post_data = http_build_query($_POST);

        // Enhanced attack patterns (more specific to avoid false positives)
        $malicious_patterns = array(
            // SQL injection (more specific patterns)
            '/(\bUNION\s+SELECT\b|\bINSERT\s+INTO\b|\bDELETE\s+FROM\b|\bDROP\s+TABLE\b)/i',
            '/\b(or|and)\s+\d+\s*=\s*\d+\s*--/i',
            '/\'\s*(or|and)\s*\'[^\']*\'\s*=\s*\'/i',

            // XSS patterns (more specific)
            '/<script[^>]*>.*?alert\s*\(/is',
            '/<iframe[^>]*src\s*=\s*["\']javascript:/i',
            '/javascript\s*:\s*alert\s*\(/i',
            '/on(load|error|click)\s*=\s*["\'].*?alert\s*\(/i',

            // Path traversal (more specific)
            '/\.\.\/(.*\/){3,}/i',
            '/%2e%2e%2f.*%2e%2e%2f/i',

            // File inclusion (more specific)
            '/php:\/\/filter\/read=/i',
            '/data:\/\/text\/plain/i',

            // Command injection (more specific)
            '/;\s*(cat|ls|pwd|id|whoami|uname)\s+[\/\w]/i',
            '/\b(system|exec|shell_exec|passthru)\s*\(\s*["\'].*[;&|]/i',

            // Common attack tools
            '/\b(sqlmap|nikto|dirb|gobuster|wpscan)\b/i'
        );

        $suspicious_content = $request_uri . ' ' . $query_string . ' ' . $user_agent . ' ' . $post_data;

        foreach ($malicious_patterns as $pattern) {
            if (preg_match($pattern, $suspicious_content)) {
                Redco_Error_Handler::critical(
                    "Malicious request detected and blocked",
                    Redco_Error_Handler::CONTEXT_SECURITY,
                    array(
                        'pattern' => $pattern,
                        'request_uri' => $request_uri,
                        'query_string' => $query_string,
                        'user_agent' => $user_agent,
                        'ip' => self::get_client_ip(),
                        'method' => $_SERVER['REQUEST_METHOD'] ?? ''
                    )
                );

                // Increment blocked requests counter
                self::increment_blocked_requests();

                // Block the request
                status_header(403);
                wp_die(
                    __('Request blocked for security reasons.', 'redco-optimizer'),
                    __('Security Error', 'redco-optimizer'),
                    array('response' => 403)
                );
            }
        }
    }

    /**
     * Check if this is a legitimate WordPress request
     */
    private static function is_legitimate_request() {
        // Debug mode - temporarily disable all filtering for troubleshooting
        if (defined('REDCO_SECURITY_DEBUG') && REDCO_SECURITY_DEBUG) {
            return true;
        }

        $request_uri = $_SERVER['REQUEST_URI'] ?? '';

        // Allow WordPress admin AJAX requests
        if (strpos($request_uri, '/wp-admin/admin-ajax.php') !== false) {
            return true;
        }

        // Allow WordPress admin requests
        if (strpos($request_uri, '/wp-admin/') !== false && is_user_logged_in()) {
            return true;
        }

        // Allow WordPress API requests
        if (strpos($request_uri, '/wp-json/') !== false) {
            return true;
        }

        // Allow WordPress core files
        if (preg_match('/\/(wp-includes|wp-content)\//', $request_uri)) {
            return true;
        }

        // Allow legitimate POST actions
        $action = $_POST['action'] ?? '';
        if (!empty($action) && strpos($action, 'redco_') === 0) {
            return true;
        }

        // Allow if user is logged in and accessing admin area
        if (is_admin() && is_user_logged_in()) {
            return true;
        }

        // Allow all requests from localhost during development
        $ip = self::get_client_ip();
        if (in_array($ip, array('127.0.0.1', '::1', 'localhost'))) {
            return true;
        }

        return false;
    }

    /**
     * Advanced request filtering
     */
    public static function advanced_request_filtering() {
        // Skip filtering for legitimate WordPress requests
        if (self::is_legitimate_request()) {
            return;
        }

        // Check for suspicious user agents (only for non-admin requests)
        if (!is_admin() && !is_user_logged_in()) {
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            $suspicious_agents = array(
                'sqlmap', 'nikto', 'dirb', 'gobuster', 'wpscan',
                'masscan', 'zmap', 'nessus', 'openvas', 'acunetix'
            );

            foreach ($suspicious_agents as $agent) {
                if (stripos($user_agent, $agent) !== false) {
                    Redco_Error_Handler::critical(
                        "Suspicious user agent detected: {$agent}",
                        Redco_Error_Handler::CONTEXT_SECURITY,
                        array(
                            'user_agent' => $user_agent,
                            'ip' => self::get_client_ip()
                        )
                    );

                    self::increment_blocked_requests();
                    status_header(403);
                    wp_die(
                        __('Access denied.', 'redco-optimizer'),
                        __('Security Error', 'redco-optimizer'),
                        array('response' => 403)
                    );
                }
            }
        }

        // Check for rapid requests (basic rate limiting) - only for non-admin
        if (!is_admin() && !is_user_logged_in()) {
            self::check_rate_limiting();
        }
    }

    /**
     * Check rate limiting
     */
    private static function check_rate_limiting() {
        // Skip rate limiting for admin users
        if (is_user_logged_in() && current_user_can('manage_options')) {
            return;
        }

        $ip = self::get_client_ip();
        $rate_limit_key = 'redco_rate_limit_' . md5($ip);

        $requests = get_transient($rate_limit_key) ?: array();
        $current_time = time();

        // Remove requests older than 1 minute
        $requests = array_filter($requests, function($timestamp) use ($current_time) {
            return ($current_time - $timestamp) < 60;
        });

        // Add current request
        $requests[] = $current_time;

        // Higher limit for logged-in users, lower for anonymous
        $max_requests = is_user_logged_in() ? 120 : 60;

        // Check if rate limit exceeded
        if (count($requests) > $max_requests) {
            Redco_Error_Handler::warning(
                "Rate limit exceeded",
                Redco_Error_Handler::CONTEXT_SECURITY,
                array(
                    'ip' => $ip,
                    'requests_count' => count($requests),
                    'max_allowed' => $max_requests,
                    'user_logged_in' => is_user_logged_in()
                )
            );

            self::increment_blocked_requests();
            status_header(429);
            wp_die(
                __('Too many requests. Please slow down.', 'redco-optimizer'),
                __('Rate Limit Exceeded', 'redco-optimizer'),
                array('response' => 429)
            );
        }

        // Store updated requests
        set_transient($rate_limit_key, $requests, 300); // 5 minutes
    }
    
    /**
     * Add security headers
     */
    public static function add_security_headers() {
        if (!headers_sent()) {
            // CRITICAL FIX: Only add security headers in admin or for logged-in users
            // The nosniff header was blocking resources and causing MIME type errors
            if (!is_admin() && !is_user_logged_in()) {
                return;
            }

            // Prevent clickjacking
            header('X-Frame-Options: SAMEORIGIN');

            // DISABLED: Prevent MIME type sniffing (was causing resource blocking)
            // header('X-Content-Type-Options: nosniff');

            // XSS protection
            header('X-XSS-Protection: 1; mode=block');

            // Referrer policy
            header('Referrer-Policy: strict-origin-when-cross-origin');

            // Content Security Policy (basic) - only for high security levels
            if (self::$config['security_level'] === self::LEVEL_HIGH || self::$config['security_level'] === self::LEVEL_STRICT) {
                header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';");
            }
        }
    }
    
    /**
     * Validate AJAX nonce
     */
    public static function validate_ajax_nonce() {
        $action = $_POST['action'] ?? '';
        
        if (strpos($action, 'redco_') === 0) {
            $nonce = $_POST['nonce'] ?? '';
            
            if (!wp_verify_nonce($nonce, $action)) {
                Redco_Error_Handler::warning(
                    "Invalid nonce for AJAX action: {$action}",
                    Redco_Error_Handler::CONTEXT_SECURITY,
                    array(
                        'action' => $action,
                        'ip' => self::get_client_ip()
                    )
                );
                
                wp_die('Security check failed.', 'Security Error', array('response' => 403));
            }
        }
    }
    
    /**
     * Scan core files for modifications
     */
    public static function scan_core_files() {
        $core_files = array(
            ABSPATH . 'wp-config.php',
            ABSPATH . 'wp-load.php',
            ABSPATH . 'wp-blog-header.php',
            ABSPATH . 'index.php',
            ABSPATH . 'wp-settings.php'
        );

        $modified_files = array();

        foreach ($core_files as $file) {
            if (file_exists($file)) {
                $current_hash = md5_file($file);
                $stored_hash = get_option('redco_file_hash_' . md5($file));

                if ($stored_hash && $stored_hash !== $current_hash) {
                    $modified_files[] = $file;
                } elseif (!$stored_hash) {
                    // Store initial hash
                    update_option('redco_file_hash_' . md5($file), $current_hash);
                }
            }
        }

        if (!empty($modified_files)) {
            Redco_Error_Handler::critical(
                "Core files modified: " . implode(', ', $modified_files),
                Redco_Error_Handler::CONTEXT_SECURITY,
                array('modified_files' => $modified_files)
            );
        }
    }

    /**
     * Check file integrity
     */
    public static function check_file_integrity() {
        // Check WordPress core files
        $wp_includes_files = array(
            ABSPATH . 'wp-includes/wp-db.php',
            ABSPATH . 'wp-includes/functions.php',
            ABSPATH . 'wp-includes/pluggable.php',
            ABSPATH . 'wp-includes/user.php'
        );

        $wp_admin_files = array(
            ABSPATH . 'wp-admin/admin.php',
            ABSPATH . 'wp-admin/admin-ajax.php',
            ABSPATH . 'wp-admin/admin-post.php'
        );

        $all_files = array_merge($wp_includes_files, $wp_admin_files);
        $suspicious_files = array();

        foreach ($all_files as $file) {
            if (file_exists($file)) {
                // Check for suspicious modifications
                $file_content = file_get_contents($file, false, null, 0, 2048); // Read first 2KB

                // Look for suspicious patterns
                $suspicious_patterns = array(
                    '/eval\s*\(/i',
                    '/base64_decode\s*\(/i',
                    '/gzinflate\s*\(/i',
                    '/str_rot13\s*\(/i',
                    '/system\s*\(/i',
                    '/shell_exec\s*\(/i',
                    '/exec\s*\(/i',
                    '/passthru\s*\(/i',
                    '/file_get_contents\s*\(\s*["\']https?:\/\//i'
                );

                foreach ($suspicious_patterns as $pattern) {
                    if (preg_match($pattern, $file_content)) {
                        $suspicious_files[] = array(
                            'file' => $file,
                            'pattern' => $pattern,
                            'size' => filesize($file),
                            'modified' => filemtime($file)
                        );
                        break;
                    }
                }
            }
        }

        if (!empty($suspicious_files)) {
            Redco_Error_Handler::critical(
                "Suspicious code detected in core files",
                Redco_Error_Handler::CONTEXT_SECURITY,
                array('suspicious_files' => $suspicious_files)
            );
        }

        // Check for new unknown files in wp-admin and wp-includes
        self::check_unknown_files();
    }

    /**
     * Check for unknown files in critical directories
     */
    private static function check_unknown_files() {
        $critical_dirs = array(
            ABSPATH . 'wp-admin/',
            ABSPATH . 'wp-includes/'
        );

        $unknown_files = array();

        foreach ($critical_dirs as $dir) {
            if (is_dir($dir)) {
                $files = glob($dir . '*.php');

                foreach ($files as $file) {
                    $filename = basename($file);

                    // Check against known WordPress core files
                    if (!self::is_known_wp_file($filename, $dir)) {
                        $unknown_files[] = array(
                            'file' => $file,
                            'size' => filesize($file),
                            'created' => filectime($file),
                            'modified' => filemtime($file)
                        );
                    }
                }
            }
        }

        if (!empty($unknown_files)) {
            Redco_Error_Handler::warning(
                "Unknown files detected in critical directories",
                Redco_Error_Handler::CONTEXT_SECURITY,
                array('unknown_files' => $unknown_files)
            );
        }
    }

    /**
     * Check if file is a known WordPress core file
     */
    private static function is_known_wp_file($filename, $dir) {
        $known_admin_files = array(
            'admin.php', 'admin-ajax.php', 'admin-post.php', 'admin-header.php',
            'admin-footer.php', 'index.php', 'menu.php', 'options.php',
            'plugins.php', 'themes.php', 'users.php', 'tools.php'
        );

        $known_includes_files = array(
            'wp-db.php', 'functions.php', 'pluggable.php', 'user.php',
            'post.php', 'comment.php', 'taxonomy.php', 'option.php',
            'formatting.php', 'capabilities.php', 'class-wp.php'
        );

        if (strpos($dir, 'wp-admin') !== false) {
            return in_array($filename, $known_admin_files);
        } elseif (strpos($dir, 'wp-includes') !== false) {
            return in_array($filename, $known_includes_files);
        }

        return false;
    }

    /**
     * Increment blocked requests counter
     */
    private static function increment_blocked_requests() {
        $count = get_option('redco_blocked_requests_count', 0);
        update_option('redco_blocked_requests_count', $count + 1);
    }
    
    /**
     * Get client IP address
     * 
     * @return string Client IP address
     */
    private static function get_client_ip() {
        $ip_headers = array(
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',            // Proxy
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'REMOTE_ADDR'                // Standard
        );
        
        foreach ($ip_headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ip = $_SERVER[$header];
                
                // Handle comma-separated IPs
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                // Validate IP
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * Secure wp_die handler
     *
     * @param callable $handler Current handler
     * @return callable Secure handler
     */
    public static function secure_wp_die_handler($handler) {
        return function($message, $title = '', $args = array()) use ($handler) {
            // Sanitize error messages in production
            if (!Redco_Config::is_development_environment()) {
                if (is_string($message) && (
                    strpos($message, 'Fatal error') !== false ||
                    strpos($message, 'Warning') !== false ||
                    strpos($message, 'Notice') !== false
                )) {
                    $message = 'An error occurred. Please contact the administrator.';
                }
            }

            // Ensure handler is callable before using it
            if (is_callable($handler)) {
                return call_user_func($handler, $message, $title, $args);
            }

            // Fallback to default wp_die behavior
            wp_die($message, $title, $args);
        };
    }
    
    /**
     * Load security configuration
     */
    private static function load_config() {
        $config = get_option('redco_optimizer_security_config', array());

        $defaults = array(
            'security_level' => self::LEVEL_LOW,
            'max_failed_attempts' => 10,
            'lockout_duration' => 900,
            'enable_file_monitoring' => false,
            'enable_request_filtering' => false,
            'enable_admin_protection' => false
        );

        self::$config = wp_parse_args($config, $defaults);
    }
    
    /**
     * Get security statistics
     *
     * @return array Security statistics
     */
    public static function get_security_stats() {
        global $wpdb;

        // Get recent security events from logs
        $recent_events = Redco_Error_Handler::get_recent_logs(Redco_Error_Handler::CONTEXT_SECURITY, 50);

        // Get total blocked requests count
        $total_blocked = get_option('redco_blocked_requests_count', 0);

        $stats = array(
            'failed_logins_24h' => 0,
            'blocked_requests_24h' => 0,
            'total_blocked_requests' => $total_blocked,
            'security_level' => ucfirst(self::$config['security_level']),
            'recent_events' => array_slice($recent_events, 0, 10),
            'features_enabled' => array(
                'file_monitoring' => self::$config['enable_file_monitoring'],
                'request_filtering' => self::$config['enable_request_filtering'],
                'admin_protection' => self::$config['enable_admin_protection']
            )
        );

        // Count events in last 24 hours
        $yesterday = date('Y-m-d H:i:s', strtotime('-24 hours'));

        foreach ($recent_events as $event) {
            if ($event['timestamp'] >= $yesterday) {
                if (strpos($event['message'], 'Failed login') !== false ||
                    strpos($event['message'], 'login attempt') !== false) {
                    $stats['failed_logins_24h']++;
                } elseif (strpos($event['message'], 'blocked') !== false ||
                         strpos($event['message'], 'Malicious request') !== false ||
                         strpos($event['message'], 'Rate limit') !== false) {
                    $stats['blocked_requests_24h']++;
                }
            }
        }

        return $stats;
    }
    
    /**
     * Check if this is a high-risk request that needs security filtering
     */
    private static function is_high_risk_request() {
        $request_uri = $_SERVER['REQUEST_URI'] ?? '';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

        // Check for suspicious patterns that indicate potential attacks
        $high_risk_patterns = array(
            '/wp-admin/',
            '/wp-login.php',
            'wp-config',
            'eval(',
            'base64_decode',
            'union.*select',
            'script.*alert'
        );

        foreach ($high_risk_patterns as $pattern) {
            if (stripos($request_uri . $user_agent, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Update security configuration
     *
     * @param array $new_config New configuration
     * @return bool Success
     */
    public static function update_config($new_config) {
        $validated_config = Redco_Settings_Validator::validate_module_settings('security', $new_config);
        self::$config = wp_parse_args($validated_config, self::$config);

        return update_option('redco_optimizer_security_config', self::$config);
    }
}
