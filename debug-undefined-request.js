/**
 * Debug script to identify the source of "/undefined" requests
 * 
 * This script monitors network requests and logs any that contain "undefined"
 * to help identify the source of the issue.
 */

(function() {
    'use strict';
    
    console.log('🔍 Debug script loaded - monitoring for undefined requests');
    
    // Override fetch to monitor requests
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        const url = args[0];
        if (typeof url === 'string' && url.includes('undefined')) {
            console.error('🚨 UNDEFINED REQUEST DETECTED via fetch():', url);
            console.trace('Stack trace:');
        }
        return originalFetch.apply(this, args);
    };
    
    // Override XMLHttpRequest to monitor requests
    const originalXHROpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(method, url, ...args) {
        if (typeof url === 'string' && url.includes('undefined')) {
            console.error('🚨 UNDEFINED REQUEST DETECTED via XMLHttpRequest:', url);
            console.trace('Stack trace:');
        }
        return originalXHROpen.apply(this, [method, url, ...args]);
    };
    
    // Monitor image loading
    const originalImageSrc = Object.getOwnPropertyDescriptor(HTMLImageElement.prototype, 'src');
    Object.defineProperty(HTMLImageElement.prototype, 'src', {
        get: function() {
            return originalImageSrc.get.call(this);
        },
        set: function(value) {
            if (typeof value === 'string' && value.includes('undefined')) {
                console.error('🚨 UNDEFINED IMAGE SRC DETECTED:', value);
                console.trace('Stack trace:');
                console.log('Image element:', this);
            }
            return originalImageSrc.set.call(this, value);
        }
    });
    
    // Monitor all network requests via Performance Observer
    if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
                if (entry.name.includes('undefined')) {
                    console.error('🚨 UNDEFINED REQUEST DETECTED via Performance Observer:', entry.name);
                    console.log('Entry details:', entry);
                }
            }
        });
        observer.observe({ entryTypes: ['resource'] });
    }
    
    // Check for existing images with undefined src
    function checkExistingImages() {
        const images = document.querySelectorAll('img');
        images.forEach((img, index) => {
            if (img.src && img.src.includes('undefined')) {
                console.error('🚨 EXISTING IMAGE WITH UNDEFINED SRC:', img.src);
                console.log('Image element:', img);
            }
            if (img.dataset.src && img.dataset.src.includes('undefined')) {
                console.error('🚨 EXISTING IMAGE WITH UNDEFINED DATA-SRC:', img.dataset.src);
                console.log('Image element:', img);
            }
        });
    }
    
    // Check immediately and after DOM changes
    checkExistingImages();
    
    // Monitor DOM changes for new images
    const mutationObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            mutation.addedNodes.forEach((node) => {
                if (node.nodeType === 1) { // Element node
                    if (node.tagName === 'IMG') {
                        if (node.src && node.src.includes('undefined')) {
                            console.error('🚨 NEW IMAGE WITH UNDEFINED SRC:', node.src);
                            console.log('Image element:', node);
                        }
                        if (node.dataset.src && node.dataset.src.includes('undefined')) {
                            console.error('🚨 NEW IMAGE WITH UNDEFINED DATA-SRC:', node.dataset.src);
                            console.log('Image element:', node);
                        }
                    } else if (node.querySelector) {
                        const imgs = node.querySelectorAll('img');
                        imgs.forEach((img) => {
                            if (img.src && img.src.includes('undefined')) {
                                console.error('🚨 NEW IMAGE WITH UNDEFINED SRC:', img.src);
                                console.log('Image element:', img);
                            }
                            if (img.dataset.src && img.dataset.src.includes('undefined')) {
                                console.error('🚨 NEW IMAGE WITH UNDEFINED DATA-SRC:', img.dataset.src);
                                console.log('Image element:', img);
                            }
                        });
                    }
                }
            });
        });
    });
    
    mutationObserver.observe(document.body, {
        childList: true,
        subtree: true
    });
    
    console.log('✅ Debug monitoring active - will log any undefined requests');
    
})();
