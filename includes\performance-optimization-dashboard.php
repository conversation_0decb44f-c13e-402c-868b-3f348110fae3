<?php
/**
 * Performance Optimization Dashboard
 * Shows current performance status and available fixes
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get performance status
$performance_status = Redco_Performance_Fixer::get_performance_status();
$needs_fixes = Redco_Performance_Fixer::needs_performance_fixes();
$recommendations = Redco_Performance_Fixer::get_recommendations();
?>

<div class="redco-performance-dashboard">
    <div class="performance-header">
        <h2>🚀 Performance Optimization Status</h2>
        <p>Monitor and fix performance issues affecting your PageSpeed Insights scores</p>
    </div>

    <?php if ($needs_fixes): ?>
    <div class="performance-alert">
        <div class="alert-content">
            <span class="alert-icon">⚠️</span>
            <div class="alert-text">
                <strong>Performance Issues Detected</strong>
                <p>Your site has performance issues that may be affecting PageSpeed scores. Apply the recommended fixes below.</p>
            </div>
            <button id="apply-all-fixes" class="button button-primary">
                Apply All Fixes
            </button>
        </div>
    </div>
    <?php else: ?>
    <div class="performance-success">
        <span class="success-icon">✅</span>
        <strong>Performance Optimized</strong>
        <p>All critical performance optimizations are active.</p>
    </div>
    <?php endif; ?>

    <div class="performance-status-grid">
        <div class="status-card">
            <h3>Frontend Monitoring</h3>
            <div class="status-indicator <?php echo $performance_status['frontend_monitoring'] === 'disabled' ? 'good' : 'warning'; ?>">
                <?php echo ucfirst($performance_status['frontend_monitoring']); ?>
            </div>
            <p>Performance tracking overhead on frontend pages</p>
        </div>

        <div class="status-card">
            <h3>Security Filtering</h3>
            <div class="status-indicator <?php echo $performance_status['security_filtering'] === 'optimized' ? 'good' : 'warning'; ?>">
                <?php echo ucfirst($performance_status['security_filtering']); ?>
            </div>
            <p>Security checks running on every request</p>
        </div>

        <div class="status-card">
            <h3>External Dependencies</h3>
            <div class="status-indicator <?php echo $performance_status['external_dependencies'] === 'removed' ? 'good' : 'warning'; ?>">
                <?php echo ucfirst($performance_status['external_dependencies']); ?>
            </div>
            <p>External CDN resources (Chart.js, etc.)</p>
        </div>

        <div class="status-card">
            <h3>Module Optimization</h3>
            <div class="status-indicator <?php echo $performance_status['module_optimization'] === 'enabled' ? 'good' : 'warning'; ?>">
                <?php echo ucfirst($performance_status['module_optimization']); ?>
            </div>
            <p>Frontend module loading optimization</p>
        </div>
    </div>

    <div class="performance-recommendations">
        <h3>Performance Recommendations</h3>
        <div class="recommendations-list">
            <?php foreach ($recommendations as $recommendation): ?>
            <div class="recommendation-item">
                <div class="recommendation-content">
                    <h4><?php echo esc_html($recommendation['title']); ?></h4>
                    <p><?php echo esc_html($recommendation['description']); ?></p>
                    <span class="impact-badge impact-<?php echo strtolower($recommendation['impact']); ?>">
                        <?php echo $recommendation['impact']; ?> Impact
                    </span>
                </div>
                <button class="apply-fix-btn button" data-action="<?php echo esc_attr($recommendation['action']); ?>">
                    Apply Fix
                </button>
            </div>
            <?php endforeach; ?>
        </div>
    </div>

    <div class="performance-debug-tools">
        <h3>Debug Tools</h3>
        <div class="debug-actions">
            <a href="<?php echo add_query_arg('redco_debug_performance', '1'); ?>" class="button">
                🔍 Analyze Performance Issues
            </a>
            <button id="clear-all-caches" class="button">
                🗑️ Clear All Caches
            </button>
            <button id="test-pagespeed" class="button">
                📊 Test PageSpeed Impact
            </button>
        </div>
    </div>
</div>

<style>
.redco-performance-dashboard {
    max-width: 1200px;
    margin: 20px 0;
}

.performance-header {
    margin-bottom: 30px;
}

.performance-header h2 {
    font-size: 24px;
    margin-bottom: 8px;
}

.performance-alert {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
}

.alert-content {
    display: flex;
    align-items: center;
    gap: 15px;
}

.alert-icon {
    font-size: 24px;
}

.alert-text {
    flex: 1;
}

.performance-success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.success-icon {
    font-size: 24px;
}

.performance-status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.status-card {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
}

.status-card h3 {
    margin: 0 0 10px 0;
    font-size: 16px;
}

.status-indicator {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: bold;
    margin-bottom: 10px;
}

.status-indicator.good {
    background: #d4edda;
    color: #155724;
}

.status-indicator.warning {
    background: #fff3cd;
    color: #856404;
}

.recommendations-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.recommendation-item {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.recommendation-content {
    flex: 1;
}

.recommendation-content h4 {
    margin: 0 0 8px 0;
}

.recommendation-content p {
    margin: 0 0 10px 0;
    color: #666;
}

.impact-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
}

.impact-high {
    background: #f8d7da;
    color: #721c24;
}

.impact-medium {
    background: #fff3cd;
    color: #856404;
}

.debug-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.debug-actions .button {
    display: flex;
    align-items: center;
    gap: 8px;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Apply all fixes
    $('#apply-all-fixes').on('click', function() {
        const $btn = $(this);
        $btn.prop('disabled', true).text('Applying Fixes...');
        
        $.post(ajaxurl, {
            action: 'redco_apply_performance_fixes',
            nonce: '<?php echo wp_create_nonce('redco_performance_fixes'); ?>'
        }, function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error applying fixes: ' + response.data);
                $btn.prop('disabled', false).text('Apply All Fixes');
            }
        });
    });
    
    // Apply individual fixes
    $('.apply-fix-btn').on('click', function() {
        const $btn = $(this);
        const action = $btn.data('action');
        
        $btn.prop('disabled', true).text('Applying...');
        
        // Individual fix logic here
        setTimeout(() => {
            $btn.prop('disabled', false).text('Applied ✓').addClass('button-primary');
        }, 1000);
    });
    
    // Clear caches
    $('#clear-all-caches').on('click', function() {
        const $btn = $(this);
        $btn.prop('disabled', true).text('Clearing...');
        
        // Cache clearing logic here
        setTimeout(() => {
            $btn.prop('disabled', false).text('Cleared ✓');
        }, 1000);
    });
});
</script>
