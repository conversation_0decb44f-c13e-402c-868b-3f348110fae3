<?php
/**
 * Page Cache Module Settings
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get module instance
$page_cache = new Redco_Page_Cache();
$is_enabled = redco_is_module_enabled('page-cache');

// Get current settings using centralized configuration
$defaults = Redco_Config::get_module_defaults('page-cache');
$cache_expiration = redco_get_module_option('page-cache', 'expiration', $defaults['expiration']);
$excluded_pages = redco_get_module_option('page-cache', 'excluded_pages', $defaults['excluded_pages']);

// Get all pages for exclusion list
$pages = redco_get_pages();

// Get cache statistics - only if module is enabled
$cache_stats = array(
    'cached_pages' => 0,
    'cache_size' => '0 B',
    'cache_hits' => 0,
    'cache_misses' => 0,
    'hit_ratio' => 0
);
$trending_pages = array();
$optimization_tips = array();
$historical_trends = array();

if ($is_enabled && class_exists('Redco_Page_Cache')) {
    $cache_stats = $page_cache->get_cache_stats();
    $trending_pages = $page_cache->get_trending_pages(5);
    $optimization_tips = $page_cache->get_optimization_tips();
    $historical_trends = $page_cache->get_historical_trends(7);
}
?>

<!-- Enqueue standardized module CSS -->
<link rel="stylesheet" href="<?php echo REDCO_OPTIMIZER_PLUGIN_URL; ?>assets/css/module-layout-standard.css">

<div class="redco-module-tab" data-module="page-cache">
    <!-- Enhanced Professional Header Section -->
    <div class="module-header-section">
        <!-- Breadcrumb Navigation -->
        <div class="header-breadcrumb">
            <div class="breadcrumb-nav">
                <a href="<?php echo admin_url('admin.php?page=redco-optimizer'); ?>">
                    <span class="dashicons dashicons-performance"></span>
                    <?php _e('Redco Optimizer', 'redco-optimizer'); ?>
                </a>
                <span class="breadcrumb-separator">›</span>
                <a href="<?php echo admin_url('admin.php?page=redco-optimizer-modules'); ?>">
                    <?php _e('Modules', 'redco-optimizer'); ?>
                </a>
                <span class="breadcrumb-separator">›</span>
                <span class="breadcrumb-current"><?php _e('Page Cache', 'redco-optimizer'); ?></span>
            </div>
        </div>

        <!-- Main Header Content -->
        <div class="header-content">
            <div class="header-main">
                <div class="header-icon">
                    <span class="dashicons dashicons-performance"></span>
                </div>
                <div class="header-text">
                    <h1><?php _e('Page Cache', 'redco-optimizer'); ?></h1>
                    <p><?php _e('High-performance page caching for faster loading times and better user experience', 'redco-optimizer'); ?></p>

                    <!-- Status Indicators -->
                    <div class="header-status">
                        <?php if ($is_enabled): ?>
                            <div class="status-badge enabled">
                                <span class="dashicons dashicons-yes-alt"></span>
                                <?php _e('Active', 'redco-optimizer'); ?>
                            </div>
                            <?php if ($cache_stats['cached_pages'] > 0): ?>
                                <div class="status-badge performance">
                                    <span class="dashicons dashicons-database"></span>
                                    <?php _e('Cached Pages', 'redco-optimizer'); ?>
                                </div>
                            <?php endif; ?>
                            <?php if ($cache_stats['hit_ratio'] > 80): ?>
                                <div class="status-badge performance">
                                    <span class="dashicons dashicons-performance"></span>
                                    <?php _e('High Hit Rate', 'redco-optimizer'); ?>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="status-badge disabled">
                                <span class="dashicons dashicons-warning"></span>
                                <?php _e('Inactive', 'redco-optimizer'); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Header Actions -->
            <div class="header-actions">
                <div class="header-action-group">
                    <!-- Quick Actions -->
                    <div class="header-quick-actions">
                        <?php if ($is_enabled): ?>
                            <button type="button" class="header-action-btn" id="clear-all-cache-header" data-redco-action="clear_all_cache">
                                <span class="dashicons dashicons-trash"></span>
                                <?php _e('Clear All Cache', 'redco-optimizer'); ?>
                            </button>
                            <button type="button" class="header-action-btn" id="preload-cache" data-redco-action="preload_cache">
                                <span class="dashicons dashicons-update"></span>
                                <?php _e('Preload', 'redco-optimizer'); ?>
                            </button>
                            <a href="<?php echo admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix'); ?>" class="header-action-btn">
                                <span class="dashicons dashicons-admin-tools"></span>
                                <?php _e('Diagnose', 'redco-optimizer'); ?>
                            </a>
                        <?php endif; ?>
                        <a href="<?php echo admin_url('admin.php?page=redco-optimizer-modules'); ?>" class="header-action-btn">
                            <span class="dashicons dashicons-admin-generic"></span>
                            <?php _e('All Modules', 'redco-optimizer'); ?>
                        </a>
                    </div>
                </div>

                <!-- Performance Metrics -->
                <?php if ($is_enabled): ?>
                    <div class="header-metrics">
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo number_format($cache_stats['cached_pages']); ?>
                            </div>
                            <div class="header-metric-label"><?php _e('Pages', 'redco-optimizer'); ?></div>
                        </div>
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo $cache_stats['hit_ratio']; ?>%
                            </div>
                            <div class="header-metric-label"><?php _e('Hit Rate', 'redco-optimizer'); ?></div>
                        </div>
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo $cache_stats['cache_size']; ?>
                            </div>
                            <div class="header-metric-label"><?php _e('Size', 'redco-optimizer'); ?></div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <?php if ($is_enabled): ?>
    <!-- Module Content -->
    <div class="redco-module-content">
        <div class="module-layout">
            <div class="redco-content-main">
                <form class="redco-module-form" data-module="page-cache">
                    <!-- Cache Settings Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-admin-settings"></span>
                                <?php _e('Page Cache Configuration', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="settings-intro">
                                <p class="description">
                                    <?php _e('Configure how long pages are cached and optimize for your website\'s update frequency. Longer cache times improve performance but may delay content updates.', 'redco-optimizer'); ?>
                                </p>
                                <div class="performance-indicator">
                                    <span class="indicator-label"><?php _e('Current Performance Impact:', 'redco-optimizer'); ?></span>
                                    <span class="impact-level high" id="cache-impact"><?php _e('High Performance', 'redco-optimizer'); ?></span>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h4 class="section-title">
                                    <span class="dashicons dashicons-clock"></span>
                                    <?php _e('Cache Duration Settings', 'redco-optimizer'); ?>
                                </h4>

                                <div class="setting-item enhanced">
                                    <label for="cache_expiration" class="setting-label">
                                        <strong><?php _e('Cache Expiration Time', 'redco-optimizer'); ?></strong>
                                        <span class="recommended-badge"><?php _e('Recommended: 6 hours', 'redco-optimizer'); ?></span>
                                    </label>
                                    <div class="setting-control">
                                        <select name="settings[expiration]" id="cache_expiration" class="enhanced-select">
                                            <option value="1800" <?php selected($cache_expiration, 1800); ?> data-impact="low"><?php _e('30 minutes - Frequent Updates', 'redco-optimizer'); ?></option>
                                            <option value="3600" <?php selected($cache_expiration, 3600); ?> data-impact="medium"><?php _e('1 hour - Regular Updates', 'redco-optimizer'); ?></option>
                                            <option value="7200" <?php selected($cache_expiration, 7200); ?> data-impact="medium"><?php _e('2 hours - Moderate Updates', 'redco-optimizer'); ?></option>
                                            <option value="21600" <?php selected($cache_expiration, 21600); ?> data-impact="high"><?php _e('6 hours - Optimal Performance', 'redco-optimizer'); ?></option>
                                            <option value="43200" <?php selected($cache_expiration, 43200); ?> data-impact="high"><?php _e('12 hours - Static Content', 'redco-optimizer'); ?></option>
                                            <option value="86400" <?php selected($cache_expiration, 86400); ?> data-impact="high"><?php _e('24 hours - Rarely Updated', 'redco-optimizer'); ?></option>
                                        </select>
                                        <div class="setting-help">
                                            <span class="help-icon" title="<?php _e('Choose based on how frequently your content updates', 'redco-optimizer'); ?>">?</span>
                                        </div>
                                    </div>
                                    <div class="setting-description">
                                        <p><?php _e('How long to keep cached pages before regenerating them. Longer times = better performance, shorter times = fresher content.', 'redco-optimizer'); ?></p>
                                        <div class="setting-impact">
                                            <span class="impact-info">
                                                <span class="impact-label"><?php _e('Performance Impact:', 'redco-optimizer'); ?></span>
                                                <span class="impact-value" id="expiration-impact"><?php _e('High', 'redco-optimizer'); ?></span>
                                            </span>
                                            <span class="freshness-info">
                                                <span class="freshness-label"><?php _e('Content Freshness:', 'redco-optimizer'); ?></span>
                                                <span class="freshness-value" id="expiration-freshness"><?php _e('6 hours delay', 'redco-optimizer'); ?></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php if (!empty($pages)): ?>
                    <!-- Excluded Pages Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-dismiss"></span>
                                <?php _e('Cache Exclusion Rules', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="exclusion-intro">
                                <p class="description">
                                    <?php _e('Exclude specific pages from caching to prevent issues with dynamic content, forms, or user-specific data. Excluded pages will always be served fresh.', 'redco-optimizer'); ?>
                                </p>
                                <div class="exclusion-summary">
                                    <span class="total-pages"><?php printf(__('Total pages: %s', 'redco-optimizer'), '<strong>' . count($pages) . '</strong>'); ?></span>
                                    <span class="excluded-count"><?php printf(__('Excluded: %s', 'redco-optimizer'), '<strong id="excluded-counter">' . count($excluded_pages) . '</strong>'); ?></span>
                                </div>
                            </div>

                            <div class="exclusion-section">
                                <h4 class="section-title">
                                    <span class="dashicons dashicons-admin-page"></span>
                                    <?php _e('Page Exclusion List', 'redco-optimizer'); ?>
                                </h4>

                                <div class="exclusion-controls">
                                    <div class="search-box">
                                        <input type="text" id="page-search" placeholder="<?php _e('Search pages...', 'redco-optimizer'); ?>" class="search-input">
                                        <span class="dashicons dashicons-search search-icon"></span>
                                    </div>
                                    <div class="filter-controls">
                                        <label class="filter-label">
                                            <input type="checkbox" id="show-only-excluded"> <?php _e('Show only excluded', 'redco-optimizer'); ?>
                                        </label>
                                    </div>
                                </div>

                                <div class="pages-list-container">
                                    <div class="pages-list" id="pages-exclusion-list">
                                        <?php foreach ($pages as $page_id => $page_title):
                                            $is_excluded = in_array($page_id, $excluded_pages);
                                            $page_type = get_post_type($page_id);
                                            $is_recommended = in_array(strtolower($page_title), ['contact', 'login', 'register', 'checkout', 'cart', 'account', 'profile']);
                                        ?>
                                            <div class="page-exclusion-item <?php echo $is_excluded ? 'excluded' : ''; ?>" data-page-title="<?php echo esc_attr(strtolower($page_title)); ?>">
                                                <label class="page-checkbox-item">
                                                    <input type="checkbox"
                                                           name="settings[excluded_pages][]"
                                                           value="<?php echo esc_attr($page_id); ?>"
                                                           <?php checked($is_excluded); ?>
                                                           class="page-exclusion-checkbox">
                                                    <span class="page-info">
                                                        <span class="page-title">
                                                            <?php echo esc_html($page_title); ?>
                                                            <?php if ($is_recommended): ?>
                                                                <span class="recommended-badge small"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                                            <?php endif; ?>
                                                        </span>
                                                        <span class="page-meta">
                                                            <span class="page-type"><?php echo esc_html(ucfirst($page_type)); ?></span>
                                                            <span class="page-id">ID: <?php echo $page_id; ?></span>
                                                        </span>
                                                    </span>
                                                    <span class="exclusion-status">
                                                        <span class="status-indicator <?php echo $is_excluded ? 'excluded' : 'cached'; ?>">
                                                            <?php echo $is_excluded ? __('Excluded', 'redco-optimizer') : __('Cached', 'redco-optimizer'); ?>
                                                        </span>
                                                    </span>
                                                </label>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>

                                <div class="exclusion-summary-footer">
                                    <div class="summary-info">
                                        <span class="pages-shown"><?php printf(__('%d pages shown', 'redco-optimizer'), count($pages)); ?></span>
                                        <span class="cache-efficiency"><?php _e('Cache efficiency: High', 'redco-optimizer'); ?></span>
                                    </div>
                                    <div class="exclusion-tips">
                                        <span class="dashicons dashicons-info"></span>
                                        <?php _e('Tip: Exclude pages with forms, user-specific content, or frequently changing data.', 'redco-optimizer'); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Advanced Caching Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-performance"></span>
                                <?php _e('Advanced Caching Options', 'redco-optimizer'); ?>
                                <span class="feature-badge"><?php _e('Enhanced', 'redco-optimizer'); ?></span>
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="settings-intro">
                                <p class="description">
                                    <?php _e('Advanced multi-layer caching system for maximum performance. These settings control the intelligent caching system that works alongside page caching.', 'redco-optimizer'); ?>
                                </p>
                            </div>

                            <div class="settings-section">
                                <h4 class="section-title">
                                    <span class="dashicons dashicons-database"></span>
                                    <?php _e('Multi-Layer Cache Configuration', 'redco-optimizer'); ?>
                                </h4>

                                <?php
                                // Get advanced cache settings
                                $cache_config = get_option('redco_optimizer_cache_config', array());
                                $defaults = array(
                                    'default_expiration' => 3600,
                                    'max_memory_items' => 100,
                                    'file_cache_enabled' => true,
                                    'object_cache_enabled' => true
                                );
                                $cache_config = wp_parse_args($cache_config, $defaults);
                                ?>

                                <div class="setting-item enhanced">
                                    <label for="cache_default_expiration" class="setting-label">
                                        <strong><?php _e('Default Cache Expiration', 'redco-optimizer'); ?></strong>
                                        <span class="help-tooltip" title="<?php _e('Default duration for caching database queries and API responses', 'redco-optimizer'); ?>">?</span>
                                    </label>
                                    <div class="setting-control">
                                        <select name="cache_config[default_expiration]" id="cache_default_expiration" class="enhanced-select">
                                            <option value="300" <?php selected($cache_config['default_expiration'], 300); ?>><?php _e('5 minutes - Frequent updates', 'redco-optimizer'); ?></option>
                                            <option value="900" <?php selected($cache_config['default_expiration'], 900); ?>><?php _e('15 minutes - Regular updates', 'redco-optimizer'); ?></option>
                                            <option value="1800" <?php selected($cache_config['default_expiration'], 1800); ?>><?php _e('30 minutes - Moderate updates', 'redco-optimizer'); ?></option>
                                            <option value="3600" <?php selected($cache_config['default_expiration'], 3600); ?>><?php _e('1 hour - Optimal balance', 'redco-optimizer'); ?></option>
                                            <option value="7200" <?php selected($cache_config['default_expiration'], 7200); ?>><?php _e('2 hours - Static content', 'redco-optimizer'); ?></option>
                                        </select>
                                    </div>
                                    <div class="setting-description">
                                        <p><?php _e('Controls how long database queries, API responses, and other data are cached in the advanced caching system.', 'redco-optimizer'); ?></p>
                                    </div>
                                </div>

                                <div class="setting-item enhanced">
                                    <label for="cache_max_memory_items" class="setting-label">
                                        <strong><?php _e('Memory Cache Limit', 'redco-optimizer'); ?></strong>
                                        <span class="help-tooltip" title="<?php _e('Maximum number of items to keep in fast memory cache', 'redco-optimizer'); ?>">?</span>
                                    </label>
                                    <div class="setting-control">
                                        <select name="cache_config[max_memory_items]" id="cache_max_memory_items" class="enhanced-select">
                                            <option value="50" <?php selected($cache_config['max_memory_items'], 50); ?>><?php _e('50 items - Low memory', 'redco-optimizer'); ?></option>
                                            <option value="100" <?php selected($cache_config['max_memory_items'], 100); ?>><?php _e('100 items - Balanced', 'redco-optimizer'); ?></option>
                                            <option value="200" <?php selected($cache_config['max_memory_items'], 200); ?>><?php _e('200 items - High performance', 'redco-optimizer'); ?></option>
                                            <option value="500" <?php selected($cache_config['max_memory_items'], 500); ?>><?php _e('500 items - Maximum performance', 'redco-optimizer'); ?></option>
                                        </select>
                                    </div>
                                    <div class="setting-description">
                                        <p><?php _e('Higher limits provide better performance but use more server memory. Adjust based on your server capacity.', 'redco-optimizer'); ?></p>
                                    </div>
                                </div>

                                <div class="setting-item enhanced">
                                    <label class="setting-label checkbox-label">
                                        <input type="checkbox" name="cache_config[file_cache_enabled]" value="1" <?php checked($cache_config['file_cache_enabled']); ?> class="enhanced-checkbox">
                                        <strong><?php _e('Enable File Cache Layer', 'redco-optimizer'); ?></strong>
                                        <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                    </label>
                                    <div class="setting-description">
                                        <p><?php _e('Store cache data in files for persistence across server restarts. Provides excellent performance with minimal server requirements.', 'redco-optimizer'); ?></p>
                                        <div class="setting-benefits">
                                            <span class="benefit-item">💾 Persistent storage</span>
                                            <span class="benefit-item">⚡ Fast access</span>
                                            <span class="benefit-item">🔄 Survives restarts</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="setting-item enhanced">
                                    <label class="setting-label checkbox-label">
                                        <input type="checkbox" name="cache_config[object_cache_enabled]" value="1" <?php checked($cache_config['object_cache_enabled']); ?> class="enhanced-checkbox">
                                        <strong><?php _e('Enable Object Cache Integration', 'redco-optimizer'); ?></strong>
                                        <?php if (function_exists('wp_cache_get')): ?>
                                            <span class="status-badge enabled"><?php _e('Available', 'redco-optimizer'); ?></span>
                                        <?php else: ?>
                                            <span class="status-badge disabled"><?php _e('Not Available', 'redco-optimizer'); ?></span>
                                        <?php endif; ?>
                                    </label>
                                    <div class="setting-description">
                                        <p><?php _e('Integrate with WordPress object cache systems like Redis or Memcached for enterprise-level performance.', 'redco-optimizer'); ?></p>
                                        <?php if (!function_exists('wp_cache_get')): ?>
                                            <div class="setting-note">
                                                <span class="dashicons dashicons-info"></span>
                                                <?php _e('Object cache is not available on your server. Consider installing Redis or Memcached for enhanced performance.', 'redco-optimizer'); ?>
                                            </div>
                                        <?php else: ?>
                                            <div class="setting-benefits">
                                                <span class="benefit-item">🚀 Enterprise performance</span>
                                                <span class="benefit-item">📈 Scalable</span>
                                                <span class="benefit-item">⚡ Ultra-fast</span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>


                        </div>
                    </div>

                </form>
            </div>

            <!-- Sidebar -->
            <div class="redco-content-sidebar">
                <!-- Comprehensive Cache Statistics -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-chart-bar"></span>
                            <?php _e('Cache Performance', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <?php
                        // Get comprehensive cache statistics from both page cache and advanced cache
                        $page_cache_stats = $cache_stats; // Existing page cache stats
                        $advanced_cache_stats = array();
                        if (class_exists('Redco_Advanced_Cache')) {
                            $advanced_cache_stats = Redco_Advanced_Cache::get_stats();
                        }

                        // Combine statistics for comprehensive view
                        $total_hits = ($page_cache_stats['cache_hits'] ?? 0) + ($advanced_cache_stats['hits'] ?? 0);
                        $total_misses = ($page_cache_stats['cache_misses'] ?? 0) + ($advanced_cache_stats['misses'] ?? 0);
                        $total_requests = $total_hits + $total_misses;
                        $combined_hit_ratio = $total_requests > 0 ? ($total_hits / $total_requests) * 100 : 0;
                        ?>

                        <div class="stats-grid">
                            <div class="stat-item stat-hits">
                                <span class="stat-value"><?php echo number_format($total_hits); ?></span>
                                <span class="stat-label"><?php _e('Total Cache Hits', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-misses">
                                <span class="stat-value"><?php echo number_format($total_misses); ?></span>
                                <span class="stat-label"><?php _e('Total Cache Misses', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-ratio">
                                <span class="stat-value"><?php echo round($combined_hit_ratio, 1); ?>%</span>
                                <span class="stat-label"><?php _e('Overall Hit Ratio', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-size">
                                <span class="stat-value"><?php echo esc_html($page_cache_stats['cache_size'] ?? '0 MB'); ?></span>
                                <span class="stat-label"><?php _e('Page Cache Size', 'redco-optimizer'); ?></span>
                            </div>
                        </div>

                        <?php if (!empty($advanced_cache_stats)): ?>
                        <div class="advanced-stats-section">
                            <h4 class="stats-subtitle"><?php _e('Advanced Cache Layers', 'redco-optimizer'); ?></h4>
                            <div class="advanced-stats-grid">
                                <div class="advanced-stat-item">
                                    <span class="stat-value"><?php echo number_format($advanced_cache_stats['memory_items'] ?? 0); ?></span>
                                    <span class="stat-label"><?php _e('Memory Items', 'redco-optimizer'); ?></span>
                                </div>
                                <div class="advanced-stat-item">
                                    <span class="stat-value"><?php echo number_format($advanced_cache_stats['file_items'] ?? 0); ?></span>
                                    <span class="stat-label"><?php _e('File Cache Items', 'redco-optimizer'); ?></span>
                                </div>
                                <?php if (function_exists('wp_cache_get')): ?>
                                <div class="advanced-stat-item">
                                    <span class="stat-value"><?php echo number_format($advanced_cache_stats['object_items'] ?? 0); ?></span>
                                    <span class="stat-label"><?php _e('Object Cache Items', 'redco-optimizer'); ?></span>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <div class="cache-performance-summary">
                            <div class="performance-indicator performance-<?php echo $combined_hit_ratio >= 80 ? 'excellent' : ($combined_hit_ratio >= 60 ? 'good' : ($combined_hit_ratio >= 40 ? 'fair' : 'poor')); ?>">
                                <span class="indicator-icon dashicons dashicons-<?php echo $combined_hit_ratio >= 80 ? 'yes-alt' : ($combined_hit_ratio >= 60 ? 'thumbs-up' : ($combined_hit_ratio >= 40 ? 'warning' : 'dismiss')); ?>"></span>
                                <span class="indicator-text">
                                    <?php
                                    if ($combined_hit_ratio >= 80) {
                                        _e('Excellent Performance', 'redco-optimizer');
                                    } elseif ($combined_hit_ratio >= 60) {
                                        _e('Good Performance', 'redco-optimizer');
                                    } elseif ($combined_hit_ratio >= 40) {
                                        _e('Fair Performance', 'redco-optimizer');
                                    } else {
                                        _e('Needs Improvement', 'redco-optimizer');
                                    }
                                    ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cache Actions -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-admin-tools"></span>
                            <?php _e('Cache Management', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <button type="button" id="clear-all-cache" class="button button-secondary" data-redco-action="clear_all_cache" style="width: 100%; margin-bottom: 10px;">
                            <span class="dashicons dashicons-trash"></span>
                            <?php _e('Clear All Cache', 'redco-optimizer'); ?>
                        </button>
                        <p class="description">
                            <?php _e('Clear all cache layers including page cache, advanced cache, memory cache, file cache, and object cache. New cache will be generated automatically.', 'redco-optimizer'); ?>
                        </p>

                        <div class="cache-layers-info">
                            <h4 class="cache-info-title"><?php _e('Cache Layers Cleared:', 'redco-optimizer'); ?></h4>
                            <ul class="cache-layers-list">
                                <li><span class="dashicons dashicons-yes-alt"></span> <?php _e('Page Cache (HTML files)', 'redco-optimizer'); ?></li>
                                <li><span class="dashicons dashicons-yes-alt"></span> <?php _e('Advanced Cache (Database queries)', 'redco-optimizer'); ?></li>
                                <li><span class="dashicons dashicons-yes-alt"></span> <?php _e('Memory Cache (Runtime data)', 'redco-optimizer'); ?></li>
                                <li><span class="dashicons dashicons-yes-alt"></span> <?php _e('File Cache (Persistent storage)', 'redco-optimizer'); ?></li>
                                <?php if (function_exists('wp_cache_get')): ?>
                                <li><span class="dashicons dashicons-yes-alt"></span> <?php _e('Object Cache (Redis/Memcached)', 'redco-optimizer'); ?></li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Trending Pages -->
                <?php if (!empty($trending_pages)): ?>
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-chart-line"></span>
                            <?php _e('Trending Pages', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="trending-pages-list">
                            <?php foreach ($trending_pages as $page): ?>
                            <div class="trending-page-item">
                                <div class="page-info">
                                    <div class="page-title"><?php echo esc_html($page['title'] ?? __('Untitled', 'redco-optimizer')); ?></div>
                                    <div class="page-stats">
                                        <span class="cache-count"><?php echo number_format($page['cache_count'] ?? 0); ?> <?php _e('cached', 'redco-optimizer'); ?></span>
                                        <span class="hit-count"><?php echo number_format($page['total_hits'] ?? 0); ?> <?php _e('hits', 'redco-optimizer'); ?></span>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <p class="description">
                            <?php _e('Most frequently cached pages on your website.', 'redco-optimizer'); ?>
                        </p>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Optimization Tips -->
                <?php if (!empty($optimization_tips)): ?>
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-lightbulb"></span>
                            <?php _e('Optimization Tips', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="optimization-tips-list">
                            <?php foreach ($optimization_tips as $tip): ?>
                            <div class="optimization-tip tip-<?php echo esc_attr($tip['type']); ?>">
                                <div class="tip-header">
                                    <span class="tip-icon dashicons dashicons-<?php echo $tip['type'] === 'warning' ? 'warning' : ($tip['type'] === 'success' ? 'yes-alt' : 'info'); ?>"></span>
                                    <strong><?php echo esc_html($tip['title']); ?></strong>
                                </div>
                                <div class="tip-message">
                                    <?php echo esc_html($tip['message']); ?>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Historical Trends -->
                <?php if (!empty($historical_trends['data'])): ?>
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-chart-area"></span>
                            <?php _e('Performance Trends', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="trends-summary">
                            <div class="trend-stat">
                                <span class="trend-value"><?php echo esc_html($historical_trends['avg_pages_cached'] ?? 0); ?></span>
                                <span class="trend-label"><?php _e('Avg Pages/Day', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="trend-direction trend-<?php echo esc_attr($historical_trends['trend_direction'] ?? 'stable'); ?>">
                                <span class="dashicons dashicons-<?php echo ($historical_trends['trend_direction'] ?? 'stable') === 'increasing' ? 'arrow-up-alt' : (($historical_trends['trend_direction'] ?? 'stable') === 'decreasing' ? 'arrow-down-alt' : 'minus'); ?>"></span>
                                <span><?php echo ucfirst($historical_trends['trend_direction'] ?? 'stable'); ?></span>
                            </div>
                        </div>
                        <div class="trends-chart">
                            <div class="chart-bars">
                                <?php
                                $data = $historical_trends['data'] ?? array();
                                if (!empty($data)) {
                                    $max_pages = max(array_column($data, 'pages_cached'));
                                    $max_pages = max($max_pages, 1); // Avoid division by zero
                                    foreach ($data as $day_data):
                                        $height = (($day_data['pages_cached'] ?? 0) / $max_pages) * 100;
                                ?>
                                <div class="chart-bar" style="height: <?php echo $height; ?>%;" title="<?php echo esc_attr(($day_data['date'] ?? '') . ': ' . ($day_data['pages_cached'] ?? 0) . ' pages'); ?>"></div>
                                <?php
                                    endforeach;
                                }
                                ?>
                            </div>
                        </div>
                        <p class="description">
                            <?php printf(__('Cache performance over the last %d days.', 'redco-optimizer'), $historical_trends['total_days'] ?? 7); ?>
                        </p>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php else: ?>
    <!-- Module Disabled State -->
    <div class="redco-module-disabled">
        <div class="disabled-message">
            <span class="dashicons dashicons-performance"></span>
            <h3><?php _e('Page Cache Module Disabled', 'redco-optimizer'); ?></h3>
            <p><?php _e('This module is currently disabled. Enable it from the Modules page to access high-performance page caching features.', 'redco-optimizer'); ?></p>
        </div>
    </div>
    <?php endif; ?>
</div>


