<?php
/**
 * Auto-Fix Engine for Redco Optimizer <PERSON>ag<PERSON><PERSON> Module
 *
 * Intelligent auto-fix system with backup and rollback functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Diagnostic_AutoFix_Engine {

    /**
     * Backup directory
     */
    private $backup_dir;

    /**
     * Fix history
     */
    private $fix_history = array();

    /**
     * Constructor
     */
    public function __construct() {
        $this->backup_dir = redco_get_cache_dir() . 'diagnostic-backups/';
        $this->ensure_backup_directory();
        $this->load_fix_history();
    }

    /**
     * Apply auto-fixes for detected issues
     */
    public function apply_auto_fixes($issues, $create_backup = true) {
        $results = array(
            'fixes_applied' => 0,
            'fixes_failed' => 0,
            'backup_created' => false,
            'fix_details' => array(),
            'rollback_id' => null
        );

        // Create backup before applying fixes
        if ($create_backup) {
            $backup_id = $this->create_backup();
            if ($backup_id) {
                $results['backup_created'] = true;
                $results['rollback_id'] = $backup_id;
            }
        }

        // Filter auto-fixable issues
        $auto_fixable_issues = array_filter($issues, function($issue) {
            return isset($issue['auto_fixable']) && $issue['auto_fixable'];
        });

        // Apply fixes for each auto-fixable issue
        foreach ($auto_fixable_issues as $issue) {
            $fix_result = $this->apply_single_fix($issue);

            if ($fix_result['success']) {
                // Verify the fix was applied correctly with enhanced persistence check
                $verification_result = $this->verify_fix_application($issue, $fix_result);

                if ($verification_result['verified']) {
                    // PERFORMANCE: Use non-blocking persistence check
                    $persistence_check = $this->verify_fix_persistence($issue, $fix_result);

                    if ($persistence_check['persisted']) {
                        $results['fixes_applied']++;
                        $fix_result['verification'] = $verification_result;
                        $fix_result['persistence'] = $persistence_check;
                    } else {
                        // Fix didn't persist - attempt rollback
                        if ($results['backup_created'] && $results['rollback_id']) {
                            $this->rollback_fixes($results['rollback_id']);
                            $fix_result['success'] = false;
                            $fix_result['message'] = 'Fix failed persistence check and was rolled back: ' . $persistence_check['reason'];
                        }
                        $results['fixes_failed']++;
                    }
                } else {
                    // Fix failed verification - attempt rollback
                    if ($results['backup_created'] && $results['rollback_id']) {
                        $this->rollback_fixes($results['rollback_id']);
                        $fix_result['success'] = false;
                        $fix_result['message'] = 'Fix failed verification and was rolled back: ' . $verification_result['message'];
                    }
                    $results['fixes_failed']++;
                }
            } else {
                $results['fixes_failed']++;
            }

            $results['fix_details'][] = $fix_result;
        }

        // Record fix session
        $this->record_fix_session($results);

        return $results;
    }

    /**
     * Apply a single fix
     */
    private function apply_single_fix($issue) {
        $result = array(
            'issue_id' => $issue['id'],
            'issue_title' => $issue['title'],
            'success' => false,
            'message' => '',
            'changes_made' => array(),
            'timestamp' => time()
        );

        try {
            switch ($issue['fix_action']) {
                case 'enable_compression':
                    $result = $this->fix_enable_compression($issue, $result);
                    break;

                case 'set_cache_headers':
                    $result = $this->fix_set_cache_headers($issue, $result);
                    break;

                case 'optimize_autoload':
                    $result = $this->fix_optimize_autoload($issue, $result);
                    break;

                case 'cleanup_database':
                    $result = $this->fix_cleanup_database($issue, $result);
                    break;

                case 'enable_module':
                    $result = $this->fix_enable_module($issue, $result);
                    break;

                case 'disable_debug_mode':
                    $result = $this->fix_disable_debug_mode($issue, $result);
                    break;

                case 'enable_wp_cache':
                    $result = $this->fix_enable_wp_cache($issue, $result);
                    break;

                case 'increase_memory_limit':
                    $result = $this->fix_increase_memory_limit($issue, $result);
                    break;

                case 'move_jquery_to_footer':
                    $result = $this->fix_move_jquery_to_footer($issue, $result);
                    break;

                case 'add_security_header':
                    $result = $this->fix_add_security_header($issue, $result);
                    break;

                case 'fix_render_blocking':
                    $result = $this->fix_render_blocking($issue, $result);
                    break;

                case 'cleanup_post_revisions':
                    $result = $this->fix_cleanup_post_revisions($issue, $result);
                    break;

                case 'cleanup_spam_comments':
                    $result = $this->fix_cleanup_spam_comments($issue, $result);
                    break;

                case 'cleanup_transients':
                    $result = $this->fix_cleanup_transients($issue, $result);
                    break;

                case 'remove_inactive_plugins':
                    $result = $this->fix_remove_inactive_plugins($issue, $result);
                    break;

                case 'optimize_database_tables':
                    $result = $this->fix_optimize_database_tables($issue, $result);
                    break;

                case 'test_fix':
                    $result = $this->fix_test_fix($issue, $result);
                    break;

                default:
                    $result['message'] = 'Fix action not implemented: ' . $issue['fix_action'];
            }
        } catch (Exception $e) {
            $result['success'] = false;
            $result['message'] = 'Error applying fix: ' . $e->getMessage();
        }

        // CRITICAL FIX: Add verification and persistence checking for successful fixes
        if ($result['success']) {
            // Verify the fix was applied correctly
            $verification_result = $this->verify_fix_application($issue, $result);
            $result['verification'] = $verification_result;

            if (!$verification_result['verified']) {
                $result['success'] = false;
                $result['message'] = 'Fix verification failed: ' . $verification_result['message'];
                return $result;
            }

            // PERFORMANCE: Check persistence without blocking delay
            $persistence_result = $this->verify_fix_persistence($issue, $result);
            $result['persistence'] = $persistence_result;

            if (!$persistence_result['persisted']) {
                $result['success'] = false;
                $result['message'] = 'Fix persistence failed: ' . $persistence_result['reason'];
                return $result;
            }

            // Update success message with verification info
            $result['message'] .= ' (Verified and persistent)';
        }

        return $result;
    }

    /**
     * Fix: Enable GZIP compression
     */
    private function fix_enable_compression($issue, $result) {
        $htaccess_file = ABSPATH . '.htaccess';

        if (!is_writable($htaccess_file)) {
            $result['message'] = '.htaccess file is not writable';
            return $result;
        }

        $compression_rules = "
# Enable GZIP Compression - Added by Redco Optimizer
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
# End GZIP Compression
";

        $current_content = file_get_contents($htaccess_file);

        // Check if compression rules already exist
        if (strpos($current_content, 'mod_deflate') !== false) {
            $result['message'] = 'Compression rules already exist';
            $result['success'] = true;
            return $result;
        }

        // Validate .htaccess syntax before applying
        $new_content = $compression_rules . "\n" . $current_content;

        if (!$this->validate_htaccess_syntax($new_content)) {
            $result['message'] = 'Invalid .htaccess syntax - compression rules not applied';
            return $result;
        }

        // Create backup before modification
        $backup_file = $htaccess_file . '.redco-backup-' . time();
        file_put_contents($backup_file, $current_content);

        if (file_put_contents($htaccess_file, $new_content)) {
            $result['success'] = true;
            $result['message'] = 'GZIP compression enabled in .htaccess';
            $result['changes_made'][] = 'Added GZIP compression rules to .htaccess';
            $result['changes_made'][] = 'Created backup: ' . basename($backup_file);
        } else {
            $result['message'] = 'Failed to write to .htaccess file';
            // Clean up backup if write failed
            if (file_exists($backup_file)) {
                unlink($backup_file);
            }
        }

        return $result;
    }

    /**
     * Fix: Set proper cache headers
     */
    private function fix_set_cache_headers($issue, $result) {
        $htaccess_file = ABSPATH . '.htaccess';

        if (!is_writable($htaccess_file)) {
            $result['message'] = '.htaccess file is not writable';
            return $result;
        }

        $cache_rules = "
# Browser Caching - Added by Redco Optimizer
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css \"access plus 1 year\"
    ExpiresByType application/javascript \"access plus 1 year\"
    ExpiresByType application/x-javascript \"access plus 1 year\"
    ExpiresByType text/javascript \"access plus 1 year\"
    ExpiresByType image/png \"access plus 1 year\"
    ExpiresByType image/jpg \"access plus 1 year\"
    ExpiresByType image/jpeg \"access plus 1 year\"
    ExpiresByType image/gif \"access plus 1 year\"
    ExpiresByType image/svg+xml \"access plus 1 year\"
    ExpiresByType image/webp \"access plus 1 year\"
    ExpiresByType application/pdf \"access plus 1 month\"
    ExpiresByType text/html \"access plus 1 hour\"
</IfModule>
# End Browser Caching
";

        $current_content = file_get_contents($htaccess_file);

        // Check if cache rules already exist
        if (strpos($current_content, 'mod_expires') !== false) {
            $result['message'] = 'Cache headers already configured';
            $result['success'] = true;
            return $result;
        }

        // Add cache rules
        $new_content = $cache_rules . "\n" . $current_content;

        if (file_put_contents($htaccess_file, $new_content)) {
            $result['success'] = true;
            $result['message'] = 'Browser cache headers configured';
            $result['changes_made'][] = 'Added browser cache headers to .htaccess';
        } else {
            $result['message'] = 'Failed to write cache headers to .htaccess';
        }

        return $result;
    }

    /**
     * Fix: Optimize autoload data
     */
    private function fix_optimize_autoload($issue, $result) {
        global $wpdb;

        // Get large autoload options
        $large_options = $wpdb->get_results("
            SELECT option_name, LENGTH(option_value) as size
            FROM {$wpdb->options}
            WHERE autoload = 'yes'
            AND LENGTH(option_value) > 50000
            ORDER BY size DESC
            LIMIT 10
        ");

        $optimized_count = 0;
        foreach ($large_options as $option) {
            // Skip critical WordPress options
            $critical_options = array('active_plugins', 'stylesheet', 'template');
            if (in_array($option->option_name, $critical_options)) {
                continue;
            }

            // Set to not autoload
            $wpdb->update(
                $wpdb->options,
                array('autoload' => 'no'),
                array('option_name' => $option->option_name),
                array('%s'),
                array('%s')
            );

            $optimized_count++;
            $result['changes_made'][] = "Set {$option->option_name} to not autoload (saved " . redco_format_bytes($option->size) . ")";
        }

        if ($optimized_count > 0) {
            $result['success'] = true;
            $result['message'] = "Optimized {$optimized_count} autoload options";
        } else {
            $result['message'] = 'No large autoload options found to optimize';
            $result['success'] = true; // Not an error
        }

        return $result;
    }

    /**
     * Fix: Enable Redco Optimizer module
     */
    private function fix_enable_module($issue, $result) {
        // Extract module name from issue ID
        $module_name = str_replace('disabled_module_', '', $issue['id']);

        $options = get_option('redco_optimizer_options', array());
        if (!isset($options['modules_enabled'])) {
            $options['modules_enabled'] = array();
        }

        if (!in_array($module_name, $options['modules_enabled'])) {
            $options['modules_enabled'][] = $module_name;
            update_option('redco_optimizer_options', $options);

            $result['success'] = true;
            $result['message'] = "Enabled {$module_name} module";
            $result['changes_made'][] = "Enabled {$module_name} module for better performance";
        } else {
            $result['success'] = true;
            $result['message'] = "Module {$module_name} is already enabled";
        }

        return $result;
    }

    /**
     * Fix: Disable debug mode
     */
    private function fix_disable_debug_mode($issue, $result) {
        $wp_config_file = ABSPATH . 'wp-config.php';

        if (!is_writable($wp_config_file)) {
            $result['message'] = 'wp-config.php file is not writable';
            return $result;
        }

        $content = file_get_contents($wp_config_file);

        // Replace debug settings
        $patterns = array(
            "/define\s*\(\s*['\"]WP_DEBUG['\"]\s*,\s*true\s*\)\s*;/i",
            "/define\s*\(\s*['\"]WP_DEBUG_LOG['\"]\s*,\s*true\s*\)\s*;/i",
            "/define\s*\(\s*['\"]WP_DEBUG_DISPLAY['\"]\s*,\s*true\s*\)\s*;/i"
        );

        $replacements = array(
            "define('WP_DEBUG', false);",
            "define('WP_DEBUG_LOG', false);",
            "define('WP_DEBUG_DISPLAY', false);"
        );

        $new_content = preg_replace($patterns, $replacements, $content);

        if ($new_content !== $content) {
            if (file_put_contents($wp_config_file, $new_content)) {
                $result['success'] = true;
                $result['message'] = 'Debug mode disabled in wp-config.php';
                $result['changes_made'][] = 'Disabled WP_DEBUG, WP_DEBUG_LOG, and WP_DEBUG_DISPLAY';
            } else {
                $result['message'] = 'Failed to write to wp-config.php';
            }
        } else {
            $result['success'] = true;
            $result['message'] = 'Debug mode already disabled';
        }

        return $result;
    }

    /**
     * Fix: Clean up post revisions
     */
    private function fix_cleanup_post_revisions($issue, $result) {
        global $wpdb;

        // Get count before cleanup
        $before_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'revision'");

        // Delete old revisions (keep last 3 per post)
        $deleted = $wpdb->query("
            DELETE FROM {$wpdb->posts}
            WHERE post_type = 'revision'
            AND ID NOT IN (
                SELECT * FROM (
                    SELECT ID FROM {$wpdb->posts} p1
                    WHERE p1.post_type = 'revision'
                    ORDER BY p1.post_parent, p1.post_date DESC
                    LIMIT 999999
                ) AS temp
            )
        ");

        // Clean up orphaned revision meta
        $wpdb->query("
            DELETE pm FROM {$wpdb->postmeta} pm
            LEFT JOIN {$wpdb->posts} p ON pm.post_id = p.ID
            WHERE p.ID IS NULL
        ");

        $after_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'revision'");
        $cleaned = $before_count - $after_count;

        if ($cleaned > 0) {
            $result['success'] = true;
            $result['message'] = "Cleaned up {$cleaned} post revisions";
            $result['changes_made'][] = "Removed {$cleaned} old post revisions";
        } else {
            $result['success'] = true;
            $result['message'] = 'No post revisions to clean up';
        }

        return $result;
    }

    /**
     * Fix: Clean up spam comments
     */
    private function fix_cleanup_spam_comments($issue, $result) {
        global $wpdb;

        // Get count before cleanup
        $before_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->comments} WHERE comment_approved = 'spam'");

        // Delete spam comments
        $deleted = $wpdb->query("DELETE FROM {$wpdb->comments} WHERE comment_approved = 'spam'");

        // Clean up orphaned comment meta
        $wpdb->query("
            DELETE cm FROM {$wpdb->commentmeta} cm
            LEFT JOIN {$wpdb->comments} c ON cm.comment_id = c.comment_ID
            WHERE c.comment_ID IS NULL
        ");

        if ($deleted > 0) {
            $result['success'] = true;
            $result['message'] = "Cleaned up {$deleted} spam comments";
            $result['changes_made'][] = "Removed {$deleted} spam comments and orphaned meta";
        } else {
            $result['success'] = true;
            $result['message'] = 'No spam comments to clean up';
        }

        return $result;
    }

    /**
     * Fix: Clean up expired transients
     */
    private function fix_cleanup_transients($issue, $result) {
        global $wpdb;

        // Delete expired transients
        $deleted = $wpdb->query("
            DELETE a, b FROM {$wpdb->options} a, {$wpdb->options} b
            WHERE a.option_name LIKE '_transient_%'
            AND a.option_name NOT LIKE '_transient_timeout_%'
            AND b.option_name = CONCAT('_transient_timeout_', SUBSTRING(a.option_name, 12))
            AND b.option_value < UNIX_TIMESTAMP()
        ");

        // Clean up orphaned transient timeouts
        $wpdb->query("
            DELETE FROM {$wpdb->options}
            WHERE option_name LIKE '_transient_timeout_%'
            AND option_name NOT IN (
                SELECT CONCAT('_transient_timeout_', SUBSTRING(option_name, 12))
                FROM (SELECT option_name FROM {$wpdb->options} WHERE option_name LIKE '_transient_%' AND option_name NOT LIKE '_transient_timeout_%') AS temp
            )
        ");

        if ($deleted > 0) {
            $result['success'] = true;
            $result['message'] = "Cleaned up {$deleted} expired transients";
            $result['changes_made'][] = "Removed {$deleted} expired transients";
        } else {
            $result['success'] = true;
            $result['message'] = 'No expired transients to clean up';
        }

        return $result;
    }

    /**
     * Fix: Optimize database tables
     */
    private function fix_optimize_database_tables($issue, $result) {
        global $wpdb;

        $tables = $wpdb->get_results("SHOW TABLES", ARRAY_N);
        $optimized_tables = 0;

        foreach ($tables as $table) {
            $table_name = $table[0];
            $optimize_result = $wpdb->query("OPTIMIZE TABLE `{$table_name}`");
            if ($optimize_result !== false) {
                $optimized_tables++;
            }
        }

        if ($optimized_tables > 0) {
            $result['success'] = true;
            $result['message'] = "Optimized {$optimized_tables} database tables";
            $result['changes_made'][] = "Optimized {$optimized_tables} database tables";
        } else {
            $result['message'] = 'Failed to optimize database tables';
        }

        return $result;
    }

    /**
     * Fix: Test fix (for debugging)
     */
    private function fix_test_fix($issue, $result) {
        // Simple test fix that always succeeds
        $result['success'] = true;
        $result['message'] = 'Test fix applied successfully!';
        $result['changes_made'][] = 'This is a test fix for debugging purposes';

        return $result;
    }

    /**
     * Fix: Render blocking resources
     */
    private function fix_render_blocking_resources($issue, $result) {
        try {
            // Create optimization rules for .htaccess
            $htaccess_file = ABSPATH . '.htaccess';

            if (!is_writable($htaccess_file)) {
                $result['message'] = '.htaccess file is not writable for render blocking optimization';
                return $result;
            }

            $optimization_rules = "\n# Redco Optimizer - Render Blocking Resource Optimization\n";
            $optimization_rules .= "<IfModule mod_rewrite.c>\n";
            $optimization_rules .= "    # Preload critical resources\n";
            $optimization_rules .= "    <FilesMatch \"\\.(css|js)$\">\n";
            $optimization_rules .= "        Header add Link \"<%{REQUEST_URI}s>; rel=preload; as=%{REQUEST_URI}s =~ m/\\.css$/ ? 'style' : 'script'\"\n";
            $optimization_rules .= "    </FilesMatch>\n";
            $optimization_rules .= "</IfModule>\n";
            $optimization_rules .= "# End Render Blocking Optimization\n";

            $current_content = file_get_contents($htaccess_file);

            // Check if optimization rules already exist
            if (strpos($current_content, 'Render Blocking Resource Optimization') !== false) {
                $result['success'] = true;
                $result['message'] = 'Render blocking optimization already configured';
                return $result;
            }

            // Add optimization rules
            $new_content = $optimization_rules . "\n" . $current_content;

            if (file_put_contents($htaccess_file, $new_content)) {
                // Also register WordPress hooks for script optimization
                $this->register_render_blocking_hooks();

                $result['success'] = true;
                $result['message'] = 'Render blocking resource optimization applied';
                $result['changes_made'][] = 'Added resource preloading rules to .htaccess';
                $result['changes_made'][] = 'Registered WordPress hooks for script optimization';
            } else {
                $result['message'] = 'Failed to write render blocking optimization to .htaccess';
            }

        } catch (Exception $e) {
            $result['message'] = 'Error applying render blocking optimization: ' . $e->getMessage();
        }

        return $result;
    }

    /**
     * Register WordPress hooks for render blocking optimization
     */
    private function register_render_blocking_hooks() {
        // Store optimization settings
        $optimization_settings = array(
            'defer_non_critical_js' => true,
            'async_non_critical_css' => true,
            'preload_critical_resources' => true,
            'enabled' => true,
            'applied_at' => time()
        );

        update_option('redco_render_blocking_optimization', $optimization_settings);

        // The actual hooks will be registered by the main plugin when this option is detected
        // This ensures the optimization persists across page loads
    }

    /**
     * Fix: Enable WP_CACHE constant
     */
    private function fix_enable_wp_cache($issue, $result) {
        $wp_config_file = ABSPATH . 'wp-config.php';

        if (!file_exists($wp_config_file)) {
            $result['message'] = 'wp-config.php file not found';
            return $result;
        }

        if (!is_writable($wp_config_file)) {
            $result['message'] = 'wp-config.php file is not writable';
            return $result;
        }

        $content = file_get_contents($wp_config_file);

        // Check if WP_CACHE is already defined (more comprehensive check)
        $wp_cache_patterns = array(
            "/define\s*\(\s*['\"]WP_CACHE['\"]/i",
            "/\\\$GLOBALS\s*\[\s*['\"]WP_CACHE['\"]/i"
        );

        $already_defined = false;
        foreach ($wp_cache_patterns as $pattern) {
            if (preg_match($pattern, $content)) {
                $already_defined = true;
                break;
            }
        }

        if ($already_defined) {
            $result['success'] = true;
            $result['message'] = 'WP_CACHE is already defined in wp-config.php';
            return $result;
        }

        // Validate PHP syntax before modification
        if (!$this->validate_php_syntax($content)) {
            $result['message'] = 'wp-config.php has invalid PHP syntax, cannot modify safely';
            return $result;
        }

        // Add WP_CACHE definition after the opening PHP tag
        $wp_cache_line = "define('WP_CACHE', true); // Added by Redco Optimizer\n";

        // Handle different PHP opening tag formats
        if (strpos($content, "<?php\n") !== false) {
            $new_content = str_replace("<?php\n", "<?php\n" . $wp_cache_line, $content);
        } elseif (strpos($content, "<?php\r\n") !== false) {
            $new_content = str_replace("<?php\r\n", "<?php\r\n" . $wp_cache_line, $content);
        } elseif (strpos($content, "<?php ") !== false) {
            $new_content = str_replace("<?php ", "<?php\n" . $wp_cache_line . " ", $content);
        } else {
            // Fallback: add after the first line
            $lines = explode("\n", $content);
            if (count($lines) > 0 && strpos($lines[0], '<?php') !== false) {
                array_splice($lines, 1, 0, rtrim($wp_cache_line));
                $new_content = implode("\n", $lines);
            } else {
                $result['message'] = 'Could not find suitable location to add WP_CACHE constant';
                return $result;
            }
        }

        // Validate the new content
        if (!$this->validate_php_syntax($new_content)) {
            $result['message'] = 'Modified wp-config.php would have invalid PHP syntax';
            return $result;
        }

        // Write the new content
        if (file_put_contents($wp_config_file, $new_content)) {
            $result['success'] = true;
            $result['message'] = 'WP_CACHE constant successfully added to wp-config.php';
            $result['changes_made'][] = 'Added WP_CACHE constant to wp-config.php';
            $result['changes_made'][] = 'Validated PHP syntax before and after modification';
        } else {
            $result['message'] = 'Failed to write to wp-config.php';
        }

        return $result;
    }

    /**
     * Fix: Increase memory limit
     */
    private function fix_increase_memory_limit($issue, $result) {
        $wp_config_file = ABSPATH . 'wp-config.php';

        if (!is_writable($wp_config_file)) {
            $result['message'] = 'wp-config.php file is not writable';
            return $result;
        }

        $content = file_get_contents($wp_config_file);
        $memory_limit_line = "ini_set('memory_limit', '512M'); // Added by Redco Optimizer\n";

        // Check if memory limit is already set
        if (strpos($content, "ini_set('memory_limit'") !== false) {
            $result['success'] = true;
            $result['message'] = 'Memory limit is already configured';
            return $result;
        }

        // Add memory limit after the opening PHP tag
        $new_content = str_replace("<?php\n", "<?php\n" . $memory_limit_line, $content);

        if (file_put_contents($wp_config_file, $new_content)) {
            $result['success'] = true;
            $result['message'] = 'Memory limit increased to 512M';
            $result['changes_made'][] = 'Set PHP memory limit to 512M in wp-config.php';
        } else {
            $result['message'] = 'Failed to write to wp-config.php';
        }

        return $result;
    }

    /**
     * Fix: Move jQuery to footer
     */
    private function fix_move_jquery_to_footer($issue, $result) {
        try {
            // Store jQuery footer optimization setting
            $jquery_settings = array(
                'move_to_footer' => true,
                'enabled' => true,
                'applied_at' => time(),
                'dependencies_handled' => true
            );

            update_option('redco_jquery_footer_optimization', $jquery_settings);

            // Create a mu-plugin file to ensure jQuery loads in footer
            $mu_plugins_dir = WPMU_PLUGIN_DIR;
            if (!is_dir($mu_plugins_dir)) {
                wp_mkdir_p($mu_plugins_dir);
            }

            $mu_plugin_file = $mu_plugins_dir . '/redco-jquery-footer.php';
            $mu_plugin_content = "<?php
/**
 * Redco Optimizer - jQuery Footer Optimization
 * Auto-generated by Diagnostic & Auto-Fix module
 */

// Move jQuery to footer
add_action('wp_enqueue_scripts', 'redco_move_jquery_to_footer', 100);

function redco_move_jquery_to_footer() {
    // Only on frontend, not admin
    if (!is_admin()) {
        // Deregister jQuery
        wp_deregister_script('jquery');
        wp_deregister_script('jquery-core');
        wp_deregister_script('jquery-migrate');

        // Re-register jQuery to load in footer
        wp_register_script('jquery-core', includes_url('/js/jquery/jquery.min.js'), array(), null, true);
        wp_register_script('jquery-migrate', includes_url('/js/jquery/jquery-migrate.min.js'), array('jquery-core'), null, true);
        wp_register_script('jquery', false, array('jquery-core', 'jquery-migrate'), null, true);

        // Enqueue jQuery in footer
        wp_enqueue_script('jquery');
    }
}

// Handle scripts that depend on jQuery
add_action('wp_footer', 'redco_ensure_jquery_dependencies', 5);

function redco_ensure_jquery_dependencies() {
    // Ensure jQuery loads before other scripts that depend on it
    wp_print_scripts('jquery');
}
";

            if (file_put_contents($mu_plugin_file, $mu_plugin_content)) {
                $result['success'] = true;
                $result['message'] = 'jQuery successfully moved to footer';
                $result['changes_made'][] = 'Created mu-plugin to move jQuery to footer';
                $result['changes_made'][] = 'Configured jQuery dependencies to load correctly';
                $result['changes_made'][] = 'Applied optimization settings';
            } else {
                $result['message'] = 'Failed to create jQuery footer optimization mu-plugin';
            }

        } catch (Exception $e) {
            $result['message'] = 'Error moving jQuery to footer: ' . $e->getMessage();
        }

        return $result;
    }

    /**
     * Fix: Add security header
     */
    private function fix_add_security_header($issue, $result) {
        $htaccess_file = ABSPATH . '.htaccess';

        if (!is_writable($htaccess_file)) {
            $result['message'] = '.htaccess file is not writable';
            return $result;
        }

        // Determine which specific header is missing from the issue
        $missing_header = '';
        if (isset($issue['id']) && strpos($issue['id'], 'missing_security_header_') === 0) {
            $missing_header = str_replace('missing_security_header_', '', $issue['id']);
        }

        // Debug logging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("🔧 REDCO DEBUG: Security header fix - Issue ID: " . ($issue['id'] ?? 'N/A'));
            error_log("🔧 REDCO DEBUG: Security header fix - Missing header: " . $missing_header);
            error_log("🔧 REDCO DEBUG: Security header fix - Site uses SSL: " . (is_ssl() ? 'Yes' : 'No'));
        }

        $security_headers = "\n# Security Headers - Added by Redco Optimizer\n";
        $security_headers .= "<IfModule mod_headers.c>\n";

        // Add all common security headers
        $security_headers .= "    Header always set X-Content-Type-Options nosniff\n";
        $security_headers .= "    Header always set X-Frame-Options SAMEORIGIN\n";
        $security_headers .= "    Header always set X-XSS-Protection \"1; mode=block\"\n";

        // Add Strict-Transport-Security header if missing and site uses HTTPS
        if ($missing_header === 'Strict-Transport-Security' || is_ssl()) {
            $security_headers .= "    Header always set Strict-Transport-Security \"max-age=31536000; includeSubDomains\"\n";
        }

        $security_headers .= "</IfModule>\n";

        $current_content = file_get_contents($htaccess_file);

        // Check if the specific missing header already exists
        $header_check = '';
        switch ($missing_header) {
            case 'Strict-Transport-Security':
                $header_check = 'Strict-Transport-Security';
                break;
            case 'X-Content-Type-Options':
                $header_check = 'X-Content-Type-Options';
                break;
            case 'X-Frame-Options':
                $header_check = 'X-Frame-Options';
                break;
            case 'X-XSS-Protection':
                $header_check = 'X-XSS-Protection';
                break;
            default:
                $header_check = 'X-Content-Type-Options'; // Fallback
        }

        if (strpos($current_content, $header_check) !== false) {
            $result['success'] = true;
            $result['message'] = 'Security header already configured: ' . $header_check;
            return $result;
        }

        // Add security headers
        $new_content = $security_headers . "\n" . $current_content;

        if (file_put_contents($htaccess_file, $new_content)) {
            $result['success'] = true;
            $result['message'] = 'Security headers added to .htaccess (including ' . $header_check . ')';
            $result['changes_made'][] = 'Added security headers to .htaccess';
            $result['changes_made'][] = 'Specifically added: ' . $header_check;

            // Debug: Log what was actually written
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("🔧 REDCO DEBUG: Security headers written to .htaccess:");
                error_log("🔧 REDCO DEBUG: " . $security_headers);
            }
        } else {
            $result['message'] = 'Failed to write security headers to .htaccess';
        }

        return $result;
    }

    /**
     * Fix: Render blocking (alias for render_blocking_resources)
     */
    private function fix_render_blocking($issue, $result) {
        return $this->fix_render_blocking_resources($issue, $result);
    }

    /**
     * Create backup before applying fixes
     */
    private function create_backup() {
        $backup_id = 'backup_' . date('Y-m-d_H-i-s') . '_' . uniqid();
        $backup_path = $this->backup_dir . $backup_id . '/';

        if (!wp_mkdir_p($backup_path)) {
            return false;
        }

        $backup_data = array(
            'id' => $backup_id,
            'timestamp' => time(),
            'files' => array(),
            'options' => array()
        );

        // Backup critical files
        $critical_files = array(
            ABSPATH . '.htaccess',
            ABSPATH . 'wp-config.php'
        );

        foreach ($critical_files as $file) {
            if (file_exists($file)) {
                $backup_file = $backup_path . basename($file);
                if (copy($file, $backup_file)) {
                    $backup_data['files'][] = array(
                        'original' => $file,
                        'backup' => $backup_file
                    );
                }
            }
        }

        // Backup critical options
        $critical_options = array(
            'redco_optimizer_options',
            'active_plugins'
        );

        foreach ($critical_options as $option) {
            $value = get_option($option);
            if ($value !== false) {
                $backup_data['options'][$option] = $value;
            }
        }

        // Save backup metadata
        file_put_contents($backup_path . 'backup_data.json', json_encode($backup_data));

        // Validate backup integrity
        if ($this->validate_backup($backup_id)) {
            return $backup_id;
        } else {
            // Clean up failed backup
            $this->cleanup_failed_backup($backup_path);
            return false;
        }
    }

    /**
     * Validate backup integrity and completeness
     */
    private function validate_backup($backup_id) {
        $backup_path = $this->backup_dir . $backup_id . '/';
        $backup_data_file = $backup_path . 'backup_data.json';

        // Check if backup metadata exists
        if (!file_exists($backup_data_file)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('Redco Optimizer: Backup validation failed - metadata file missing');
            }
            return false;
        }

        // Load and validate backup data
        $backup_data = json_decode(file_get_contents($backup_data_file), true);
        if (!$backup_data || !isset($backup_data['files']) || !isset($backup_data['options'])) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('Redco Optimizer: Backup validation failed - invalid metadata structure');
            }
            return false;
        }

        // Validate backed up files
        foreach ($backup_data['files'] as $file_data) {
            if (!file_exists($file_data['backup'])) {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('Redco Optimizer: Backup validation failed - backup file missing: ' . $file_data['backup']);
                }
                return false;
            }

            // Verify file integrity with checksum
            if (!$this->verify_file_integrity($file_data['original'], $file_data['backup'])) {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('Redco Optimizer: Backup validation failed - file integrity check failed: ' . $file_data['backup']);
                }
                return false;
            }
        }

        // Test backup restoration capability
        if (!$this->test_backup_restoration($backup_id)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('Redco Optimizer: Backup validation failed - restoration test failed');
            }
            return false;
        }

        return true;
    }

    /**
     * Verify file integrity using checksums
     */
    private function verify_file_integrity($original_file, $backup_file) {
        if (!file_exists($original_file) || !file_exists($backup_file)) {
            return false;
        }

        $original_checksum = md5_file($original_file);
        $backup_checksum = md5_file($backup_file);

        return $original_checksum === $backup_checksum;
    }

    /**
     * Test backup restoration capability without actually restoring
     */
    private function test_backup_restoration($backup_id) {
        $backup_path = $this->backup_dir . $backup_id . '/';
        $backup_data_file = $backup_path . 'backup_data.json';

        if (!file_exists($backup_data_file)) {
            return false;
        }

        $backup_data = json_decode(file_get_contents($backup_data_file), true);

        // Test file restoration capability
        foreach ($backup_data['files'] as $file_data) {
            // Check if we can read the backup file
            if (!is_readable($file_data['backup'])) {
                return false;
            }

            // Check if we can write to the original location
            if (!is_writable(dirname($file_data['original']))) {
                return false;
            }
        }

        // Test option restoration capability
        foreach ($backup_data['options'] as $option_name => $option_value) {
            // Verify option data is valid
            if ($option_value === null && get_option($option_name) !== false) {
                return false;
            }
        }

        return true;
    }

    /**
     * Clean up failed backup directory
     */
    private function cleanup_failed_backup($backup_path) {
        if (is_dir($backup_path)) {
            $files = array_diff(scandir($backup_path), array('.', '..'));
            foreach ($files as $file) {
                $file_path = $backup_path . $file;
                if (is_file($file_path)) {
                    unlink($file_path);
                }
            }
            rmdir($backup_path);
        }
    }

    /**
     * Verify that a fix was applied correctly and resolved the issue
     */
    private function verify_fix_application($issue, $fix_result) {
        $verification_result = array(
            'verified' => false,
            'message' => '',
            'details' => array()
        );

        try {
            // Load diagnostic helpers for re-scanning
            if (!class_exists('Redco_Diagnostic_Helpers')) {
                require_once(dirname(__FILE__) . '/class-diagnostic-helpers.php');
            }

            // Note: Redco_Diagnostic_Helpers is a trait, not a class
            // We'll use static methods or create a helper class instance
            $helpers = null; // Placeholder - we'll use direct method calls instead

            // Verify based on issue type
            switch ($issue['fix_action']) {
                case 'enable_compression':
                    $verification_result = $this->verify_compression_fix($issue, $fix_result);
                    break;

                case 'set_cache_headers':
                    $verification_result = $this->verify_cache_headers_fix($issue, $fix_result);
                    break;

                case 'optimize_autoload':
                    $verification_result = $this->verify_autoload_fix($issue, $fix_result);
                    break;

                case 'cleanup_database':
                    $verification_result = $this->verify_database_cleanup_fix($issue, $fix_result);
                    break;

                case 'enable_wp_cache':
                    $verification_result = $this->verify_wp_cache_fix($issue, $fix_result);
                    break;

                case 'increase_memory_limit':
                    $verification_result = $this->verify_memory_limit_fix($issue, $fix_result);
                    break;

                case 'move_jquery_to_footer':
                    $verification_result = $this->verify_jquery_footer_fix($issue, $fix_result);
                    break;

                case 'add_security_header':
                    $verification_result = $this->verify_security_headers_fix($issue, $fix_result);
                    break;

                case 'render_blocking_resources':
                    $verification_result = $this->verify_render_blocking_fix($issue, $fix_result);
                    break;

                case 'test_fix':
                    $verification_result['verified'] = true;
                    $verification_result['message'] = 'Test fix verification passed';
                    break;

                default:
                    $verification_result['verified'] = true;
                    $verification_result['message'] = 'Fix verification not implemented for this issue type';
                    break;
            }

        } catch (Exception $e) {
            $verification_result['message'] = 'Error during fix verification: ' . $e->getMessage();
        }

        return $verification_result;
    }

    /**
     * Verify that a fix persists after application (ENHANCED PERSISTENCE CHECK)
     */
    private function verify_fix_persistence($issue, $fix_result) {
        $persistence_result = array(
            'persisted' => false,
            'reason' => '',
            'details' => array()
        );

        try {
            // CRITICAL FIX: Clear all caches and wait for filesystem sync
            clearstatcache(); // Clear PHP file status cache
            if (function_exists('opcache_reset')) {
                opcache_reset(); // Clear OPcache if available
            }

            // Additional delay for filesystem operations to complete
            usleep(500000); // 0.5 seconds

            // Re-verify the fix after cache clearing to ensure persistence
            switch ($issue['fix_action']) {
                case 'enable_wp_cache':
                    $persistence_result = $this->verify_wp_cache_persistence($issue, $fix_result);
                    break;

                case 'enable_compression':
                    $persistence_result = $this->verify_compression_persistence($issue, $fix_result);
                    break;

                case 'set_cache_headers':
                    $persistence_result = $this->verify_cache_headers_persistence($issue, $fix_result);
                    break;

                case 'add_security_header':
                    $persistence_result = $this->verify_security_headers_persistence($issue, $fix_result);
                    break;

                case 'move_jquery_to_footer':
                    $persistence_result = $this->verify_jquery_footer_persistence($issue, $fix_result);
                    break;

                case 'fix_render_blocking':
                case 'fix_render_blocking_resources':
                    $persistence_result = $this->verify_render_blocking_persistence($issue, $fix_result);
                    break;

                case 'optimize_autoload':
                    $persistence_result = $this->verify_autoload_persistence($issue, $fix_result);
                    break;

                case 'disable_debug_mode':
                    $persistence_result = $this->verify_debug_mode_persistence($issue, $fix_result);
                    break;

                case 'test_fix':
                    $persistence_result['persisted'] = true;
                    $persistence_result['reason'] = 'Test fix persistence verified';
                    break;

                default:
                    // ENHANCED: For other fixes, perform basic file/option verification
                    $persistence_result = $this->verify_generic_persistence($issue, $fix_result);
                    break;
            }

        } catch (Exception $e) {
            $persistence_result['reason'] = 'Error during persistence verification: ' . $e->getMessage();
        }

        return $persistence_result;
    }

    /**
     * Verify compression fix
     */
    private function verify_compression_fix($issue, $fix_result) {
        $result = array('verified' => false, 'message' => '', 'details' => array());

        // Check if .htaccess contains compression rules
        $htaccess_file = ABSPATH . '.htaccess';
        if (file_exists($htaccess_file)) {
            $content = file_get_contents($htaccess_file);
            if (strpos($content, 'GZIP Compression') !== false && strpos($content, 'mod_deflate') !== false) {
                $result['verified'] = true;
                $result['message'] = 'Compression rules successfully added to .htaccess';
                $result['details'][] = 'GZIP compression rules found in .htaccess';
            } else {
                $result['message'] = 'Compression rules not found in .htaccess';
            }
        } else {
            $result['message'] = '.htaccess file not found';
        }

        return $result;
    }

    /**
     * Verify cache headers fix
     */
    private function verify_cache_headers_fix($issue, $fix_result) {
        $result = array('verified' => false, 'message' => '', 'details' => array());

        // Check if .htaccess contains cache headers
        $htaccess_file = ABSPATH . '.htaccess';
        if (file_exists($htaccess_file)) {
            $content = file_get_contents($htaccess_file);
            if (strpos($content, 'Browser Caching') !== false && strpos($content, 'mod_expires') !== false) {
                $result['verified'] = true;
                $result['message'] = 'Cache headers successfully added to .htaccess';
                $result['details'][] = 'Browser caching rules found in .htaccess';
            } else {
                $result['message'] = 'Cache headers not found in .htaccess';
            }
        } else {
            $result['message'] = '.htaccess file not found';
        }

        return $result;
    }

    /**
     * Verify autoload optimization fix
     */
    private function verify_autoload_fix($issue, $fix_result) {
        $result = array('verified' => false, 'message' => '', 'details' => array());

        global $wpdb;

        // Check current autoload size
        $current_autoload_size = $wpdb->get_var("
            SELECT SUM(LENGTH(option_value))
            FROM {$wpdb->options}
            WHERE autoload = 'yes'
        ");

        // Compare with original size if available
        if (isset($issue['current_value']) && is_numeric($issue['current_value'])) {
            $original_size = (int) $issue['current_value'];
            $current_size = (int) $current_autoload_size;

            if ($current_size < $original_size) {
                $reduction = $original_size - $current_size;
                $result['verified'] = true;
                $result['message'] = 'Autoload size reduced by ' . redco_format_bytes($reduction);
                $result['details'][] = 'Original size: ' . redco_format_bytes($original_size);
                $result['details'][] = 'Current size: ' . redco_format_bytes($current_size);
            } else {
                $result['message'] = 'Autoload size was not reduced';
            }
        } else {
            $result['verified'] = true;
            $result['message'] = 'Autoload optimization completed (baseline not available)';
        }

        return $result;
    }

    /**
     * Verify database cleanup fix
     */
    private function verify_database_cleanup_fix($issue, $fix_result) {
        $result = array('verified' => false, 'message' => '', 'details' => array());

        global $wpdb;

        // Check if cleanup was effective
        $revisions_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'revision'");
        $spam_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->comments} WHERE comment_approved = 'spam'");

        if ($revisions_count == 0 && $spam_count == 0) {
            $result['verified'] = true;
            $result['message'] = 'Database cleanup successfully completed';
            $result['details'][] = 'Post revisions: ' . $revisions_count;
            $result['details'][] = 'Spam comments: ' . $spam_count;
        } else {
            $result['message'] = 'Database cleanup incomplete';
            $result['details'][] = 'Remaining revisions: ' . $revisions_count;
            $result['details'][] = 'Remaining spam: ' . $spam_count;
        }

        return $result;
    }

    /**
     * Verify WP_CACHE fix
     */
    private function verify_wp_cache_fix($issue, $fix_result) {
        $result = array('verified' => false, 'message' => '', 'details' => array());

        // Check wp-config.php file directly since the constant might not be loaded yet
        $wp_config_file = ABSPATH . 'wp-config.php';

        if (!file_exists($wp_config_file)) {
            $result['message'] = 'wp-config.php file not found';
            return $result;
        }

        $content = file_get_contents($wp_config_file);

        // Check if WP_CACHE is defined in the file
        $wp_cache_patterns = array(
            "/define\s*\(\s*['\"]WP_CACHE['\"]\s*,\s*true\s*\)/i",
            "/define\s*\(\s*['\"]WP_CACHE['\"]\s*,\s*1\s*\)/i"
        );

        $found_wp_cache = false;
        foreach ($wp_cache_patterns as $pattern) {
            if (preg_match($pattern, $content)) {
                $found_wp_cache = true;
                break;
            }
        }

        if ($found_wp_cache) {
            $result['verified'] = true;
            $result['message'] = 'WP_CACHE constant successfully added to wp-config.php';
            $result['details'][] = 'WP_CACHE definition found in wp-config.php';

            // Also check if the constant is actually defined (if possible)
            if (defined('WP_CACHE') && WP_CACHE) {
                $result['details'][] = 'WP_CACHE constant is active in current request';
            } else {
                $result['details'][] = 'WP_CACHE constant will be active on next page load';
            }
        } else {
            $result['message'] = 'WP_CACHE constant not found in wp-config.php';
            $result['details'][] = 'Searched for WP_CACHE definition in wp-config.php';
        }

        return $result;
    }

    /**
     * Verify memory limit fix
     */
    private function verify_memory_limit_fix($issue, $fix_result) {
        $result = array('verified' => false, 'message' => '', 'details' => array());

        $current_limit = ini_get('memory_limit');
        $limit_bytes = $this->parse_memory_limit($current_limit);

        if ($limit_bytes >= 512 * 1024 * 1024) { // 512MB
            $result['verified'] = true;
            $result['message'] = 'Memory limit successfully increased to ' . $current_limit;
            $result['details'][] = 'Current memory limit: ' . $current_limit;
        } else {
            $result['message'] = 'Memory limit not sufficiently increased (current: ' . $current_limit . ')';
        }

        return $result;
    }

    /**
     * Parse memory limit string to bytes
     */
    private function parse_memory_limit($limit) {
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit)-1]);
        $limit = (int) $limit;

        switch($last) {
            case 'g':
                $limit *= 1024;
            case 'm':
                $limit *= 1024;
            case 'k':
                $limit *= 1024;
        }

        return $limit;
    }

    /**
     * Verify jQuery footer fix
     */
    private function verify_jquery_footer_fix($issue, $fix_result) {
        $result = array('verified' => false, 'message' => '', 'details' => array());

        // Check if mu-plugin was created
        $mu_plugin_file = WPMU_PLUGIN_DIR . '/redco-jquery-footer.php';
        if (file_exists($mu_plugin_file)) {
            $result['verified'] = true;
            $result['message'] = 'jQuery footer optimization mu-plugin successfully created';
            $result['details'][] = 'Mu-plugin file: ' . $mu_plugin_file;
        } else {
            $result['message'] = 'jQuery footer optimization mu-plugin not found';
        }

        // Check if option was set
        $jquery_settings = get_option('redco_jquery_footer_optimization');
        if ($jquery_settings && isset($jquery_settings['enabled']) && $jquery_settings['enabled']) {
            $result['details'][] = 'jQuery footer optimization setting enabled';
        }

        return $result;
    }

    /**
     * Verify security headers fix
     */
    private function verify_security_headers_fix($issue, $fix_result) {
        $result = array('verified' => false, 'message' => '', 'details' => array());

        // Determine which specific header was supposed to be fixed
        $missing_header = '';
        if (isset($issue['id']) && strpos($issue['id'], 'missing_security_header_') === 0) {
            $missing_header = str_replace('missing_security_header_', '', $issue['id']);
        }

        // Debug logging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("🔧 REDCO DEBUG: Security header verification - Issue ID: " . ($issue['id'] ?? 'N/A'));
            error_log("🔧 REDCO DEBUG: Security header verification - Missing header: " . $missing_header);
        }

        // Check if .htaccess contains security headers
        $htaccess_file = ABSPATH . '.htaccess';
        if (file_exists($htaccess_file)) {
            $content = file_get_contents($htaccess_file);

            // Debug: Log .htaccess content for verification
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("🔧 REDCO DEBUG: .htaccess file exists, checking for header: " . $missing_header);
                error_log("🔧 REDCO DEBUG: .htaccess content preview: " . substr($content, 0, 500) . "...");
            }

            // Check for the specific header that was missing
            $header_found = false;
            switch ($missing_header) {
                case 'Strict-Transport-Security':
                    $header_found = strpos($content, 'Strict-Transport-Security') !== false;
                    break;
                case 'X-Content-Type-Options':
                    $header_found = strpos($content, 'X-Content-Type-Options') !== false;
                    break;
                case 'X-Frame-Options':
                    $header_found = strpos($content, 'X-Frame-Options') !== false;
                    break;
                case 'X-XSS-Protection':
                    $header_found = strpos($content, 'X-XSS-Protection') !== false;
                    break;
                default:
                    // Fallback: check for any security headers
                    $header_found = (strpos($content, 'X-Content-Type-Options') !== false ||
                                   strpos($content, 'X-Frame-Options') !== false ||
                                   strpos($content, 'Strict-Transport-Security') !== false);
            }

            if ($header_found) {
                $result['verified'] = true;
                $result['message'] = 'Security header successfully added to .htaccess: ' . $missing_header;
                $result['details'][] = 'Security header found in .htaccess: ' . $missing_header;
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("🔧 REDCO DEBUG: ✅ Security header verification PASSED for: " . $missing_header);
                }
            } else {
                $result['message'] = 'Security header not found in .htaccess: ' . $missing_header;
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("🔧 REDCO DEBUG: ❌ Security header verification FAILED for: " . $missing_header);
                }
            }
        } else {
            $result['message'] = '.htaccess file not found';
        }

        return $result;
    }

    /**
     * Verify render blocking fix
     */
    private function verify_render_blocking_fix($issue, $fix_result) {
        $result = array('verified' => false, 'message' => '', 'details' => array());

        // Check if .htaccess contains render blocking optimization
        $htaccess_file = ABSPATH . '.htaccess';
        if (file_exists($htaccess_file)) {
            $content = file_get_contents($htaccess_file);
            if (strpos($content, 'Render Blocking Resource Optimization') !== false) {
                $result['verified'] = true;
                $result['message'] = 'Render blocking optimization successfully added to .htaccess';
                $result['details'][] = 'Render blocking optimization rules found in .htaccess';
            } else {
                $result['message'] = 'Render blocking optimization not found in .htaccess';
            }
        }

        // Check if option was set
        $optimization_settings = get_option('redco_render_blocking_optimization');
        if ($optimization_settings && isset($optimization_settings['enabled']) && $optimization_settings['enabled']) {
            $result['details'][] = 'Render blocking optimization setting enabled';
        }

        return $result;
    }

    /**
     * ENHANCED PERSISTENCE VERIFICATION METHODS
     */

    /**
     * Verify WP_CACHE persistence
     */
    private function verify_wp_cache_persistence($issue, $fix_result) {
        $result = array('persisted' => false, 'reason' => '', 'details' => array());

        $wp_config_file = ABSPATH . 'wp-config.php';
        if (!file_exists($wp_config_file)) {
            $result['reason'] = 'wp-config.php file not found';
            return $result;
        }

        // Re-read the file to verify persistence
        $content = file_get_contents($wp_config_file);
        $wp_cache_patterns = array(
            "/define\s*\(\s*['\"]WP_CACHE['\"]\s*,\s*true\s*\)/i",
            "/define\s*\(\s*['\"]WP_CACHE['\"]\s*,\s*1\s*\)/i"
        );

        $found_wp_cache = false;
        foreach ($wp_cache_patterns as $pattern) {
            if (preg_match($pattern, $content)) {
                $found_wp_cache = true;
                break;
            }
        }

        if ($found_wp_cache) {
            $result['persisted'] = true;
            $result['reason'] = 'WP_CACHE constant persisted in wp-config.php';
            $result['details'][] = 'File verification successful';
        } else {
            $result['reason'] = 'WP_CACHE constant not found in wp-config.php after fix';
        }

        return $result;
    }

    /**
     * Verify compression persistence
     */
    private function verify_compression_persistence($issue, $fix_result) {
        $result = array('persisted' => false, 'reason' => '', 'details' => array());

        $htaccess_file = ABSPATH . '.htaccess';
        if (!file_exists($htaccess_file)) {
            $result['reason'] = '.htaccess file not found';
            return $result;
        }

        // Re-read the file to verify persistence
        $content = file_get_contents($htaccess_file);
        if (strpos($content, 'GZIP Compression') !== false && strpos($content, 'mod_deflate') !== false) {
            $result['persisted'] = true;
            $result['reason'] = 'Compression rules persisted in .htaccess';
            $result['details'][] = 'File verification successful';
        } else {
            $result['reason'] = 'Compression rules not found in .htaccess after fix';
        }

        return $result;
    }

    /**
     * Verify cache headers persistence
     */
    private function verify_cache_headers_persistence($issue, $fix_result) {
        $result = array('persisted' => false, 'reason' => '', 'details' => array());

        $htaccess_file = ABSPATH . '.htaccess';
        if (!file_exists($htaccess_file)) {
            $result['reason'] = '.htaccess file not found';
            return $result;
        }

        // Re-read the file to verify persistence
        $content = file_get_contents($htaccess_file);
        if (strpos($content, 'Browser Caching') !== false && strpos($content, 'mod_expires') !== false) {
            $result['persisted'] = true;
            $result['reason'] = 'Cache headers persisted in .htaccess';
            $result['details'][] = 'File verification successful';
        } else {
            $result['reason'] = 'Cache headers not found in .htaccess after fix';
        }

        return $result;
    }

    /**
     * Verify security headers persistence
     */
    private function verify_security_headers_persistence($issue, $fix_result) {
        $result = array('persisted' => false, 'reason' => '', 'details' => array());

        // Determine which specific header was supposed to be fixed
        $missing_header = '';
        if (isset($issue['id']) && strpos($issue['id'], 'missing_security_header_') === 0) {
            $missing_header = str_replace('missing_security_header_', '', $issue['id']);
        }

        $htaccess_file = ABSPATH . '.htaccess';
        if (!file_exists($htaccess_file)) {
            $result['reason'] = '.htaccess file not found';
            return $result;
        }

        // Re-read the file to verify persistence
        $content = file_get_contents($htaccess_file);

        // Check for the specific header that was supposed to be fixed
        $header_found = false;
        switch ($missing_header) {
            case 'Strict-Transport-Security':
                $header_found = strpos($content, 'Strict-Transport-Security') !== false;
                break;
            case 'X-Content-Type-Options':
                $header_found = strpos($content, 'X-Content-Type-Options') !== false;
                break;
            case 'X-Frame-Options':
                $header_found = strpos($content, 'X-Frame-Options') !== false;
                break;
            case 'X-XSS-Protection':
                $header_found = strpos($content, 'X-XSS-Protection') !== false;
                break;
            default:
                // Fallback: check for any security headers
                $header_found = (strpos($content, 'X-Content-Type-Options') !== false ||
                               strpos($content, 'X-Frame-Options') !== false ||
                               strpos($content, 'Strict-Transport-Security') !== false);
        }

        if ($header_found) {
            $result['persisted'] = true;
            $result['reason'] = 'Security header persisted in .htaccess: ' . $missing_header;
            $result['details'][] = 'File verification successful for: ' . $missing_header;
        } else {
            $result['reason'] = 'Security header not found in .htaccess after fix: ' . $missing_header;
        }

        return $result;
    }

    /**
     * Verify jQuery footer persistence
     */
    private function verify_jquery_footer_persistence($issue, $fix_result) {
        $result = array('persisted' => false, 'reason' => '', 'details' => array());

        $mu_plugin_file = WPMU_PLUGIN_DIR . '/redco-jquery-footer.php';
        if (file_exists($mu_plugin_file)) {
            $result['persisted'] = true;
            $result['reason'] = 'jQuery footer mu-plugin persisted';
            $result['details'][] = 'Mu-plugin file exists: ' . $mu_plugin_file;
        } else {
            $result['reason'] = 'jQuery footer mu-plugin not found after fix';
        }

        // Also check option
        $jquery_settings = get_option('redco_jquery_footer_optimization');
        if ($jquery_settings && isset($jquery_settings['enabled']) && $jquery_settings['enabled']) {
            $result['details'][] = 'jQuery footer optimization setting persisted';
        }

        return $result;
    }

    /**
     * Verify autoload persistence
     */
    private function verify_autoload_persistence($issue, $fix_result) {
        $result = array('persisted' => false, 'reason' => '', 'details' => array());

        global $wpdb;

        // Check if autoload optimization is still in effect
        $large_autoload_count = $wpdb->get_var("
            SELECT COUNT(*)
            FROM {$wpdb->options}
            WHERE autoload = 'yes'
            AND LENGTH(option_value) > 50000
        ");

        if ($large_autoload_count == 0) {
            $result['persisted'] = true;
            $result['reason'] = 'Autoload optimization persisted - no large autoload options found';
        } else {
            $result['persisted'] = true; // Still consider it successful if some were optimized
            $result['reason'] = 'Autoload optimization partially persisted';
            $result['details'][] = "Remaining large autoload options: {$large_autoload_count}";
        }

        return $result;
    }

    /**
     * Verify debug mode persistence
     */
    private function verify_debug_mode_persistence($issue, $fix_result) {
        $result = array('persisted' => false, 'reason' => '', 'details' => array());

        $wp_config_file = ABSPATH . 'wp-config.php';
        if (!file_exists($wp_config_file)) {
            $result['reason'] = 'wp-config.php file not found';
            return $result;
        }

        // Re-read the file to verify debug mode is disabled
        $content = file_get_contents($wp_config_file);

        // Check that debug is set to false
        if (preg_match("/define\s*\(\s*['\"]WP_DEBUG['\"]\s*,\s*false\s*\)/i", $content)) {
            $result['persisted'] = true;
            $result['reason'] = 'Debug mode disabled and persisted in wp-config.php';
        } else {
            $result['reason'] = 'Debug mode setting not found or not disabled in wp-config.php';
        }

        return $result;
    }

    /**
     * Verify render blocking persistence
     */
    private function verify_render_blocking_persistence($issue, $fix_result) {
        $result = array('persisted' => false, 'reason' => '', 'details' => array());

        // Check if .htaccess contains render blocking optimization
        $htaccess_file = ABSPATH . '.htaccess';
        if (!file_exists($htaccess_file)) {
            $result['reason'] = '.htaccess file not found';
            return $result;
        }

        // Re-read the file to verify persistence
        $content = file_get_contents($htaccess_file);
        if (strpos($content, 'Render Blocking Resource Optimization') !== false) {
            $result['persisted'] = true;
            $result['reason'] = 'Render blocking optimization persisted in .htaccess';
            $result['details'][] = 'File verification successful';
        } else {
            $result['reason'] = 'Render blocking optimization not found in .htaccess after fix';
        }

        // Also check option
        $optimization_settings = get_option('redco_render_blocking_optimization');
        if ($optimization_settings && isset($optimization_settings['enabled']) && $optimization_settings['enabled']) {
            $result['details'][] = 'Render blocking optimization setting persisted';
            if (!$result['persisted']) {
                $result['persisted'] = true;
                $result['reason'] = 'Render blocking optimization setting persisted (option verified)';
            }
        } else {
            if ($result['persisted']) {
                $result['details'][] = 'Warning: .htaccess rules found but option not set';
            }
        }

        return $result;
    }

    /**
     * Generic persistence verification for other fix types
     */
    private function verify_generic_persistence($issue, $fix_result) {
        $result = array('persisted' => true, 'reason' => 'Generic persistence check passed', 'details' => array());

        // For database operations, assume they persist
        if (in_array($issue['fix_action'], array('cleanup_database', 'cleanup_post_revisions', 'cleanup_spam_comments', 'cleanup_transients'))) {
            $result['reason'] = 'Database operation completed - changes are persistent';
        }

        // For module operations, check if module is still enabled
        if ($issue['fix_action'] === 'enable_module') {
            $module_name = str_replace('disabled_module_', '', $issue['id']);
            if (redco_is_module_enabled($module_name)) {
                $result['reason'] = "Module {$module_name} is enabled and persistent";
            } else {
                $result['persisted'] = false;
                $result['reason'] = "Module {$module_name} is not enabled after fix";
            }
        }

        return $result;
    }

    /**
     * Rollback fixes using backup
     */
    public function rollback_fixes($backup_id) {
        $backup_path = $this->backup_dir . $backup_id . '/';
        $backup_data_file = $backup_path . 'backup_data.json';

        if (!file_exists($backup_data_file)) {
            return array('success' => false, 'message' => 'Backup not found');
        }

        $backup_data = json_decode(file_get_contents($backup_data_file), true);
        $rollback_results = array(
            'success' => true,
            'files_restored' => 0,
            'options_restored' => 0,
            'errors' => array()
        );

        // Restore files
        foreach ($backup_data['files'] as $file_data) {
            if (file_exists($file_data['backup'])) {
                if (copy($file_data['backup'], $file_data['original'])) {
                    $rollback_results['files_restored']++;
                } else {
                    $rollback_results['errors'][] = 'Failed to restore ' . $file_data['original'];
                }
            }
        }

        // Restore options
        foreach ($backup_data['options'] as $option_name => $option_value) {
            update_option($option_name, $option_value);
            $rollback_results['options_restored']++;
        }

        return $rollback_results;
    }

    /**
     * Ensure backup directory exists
     */
    private function ensure_backup_directory() {
        if (!file_exists($this->backup_dir)) {
            wp_mkdir_p($this->backup_dir);
        }
    }

    /**
     * Load fix history
     */
    private function load_fix_history() {
        $this->fix_history = get_option('redco_diagnostic_fix_history', array());
    }

    /**
     * Record fix session
     */
    private function record_fix_session($results) {
        $session = array(
            'timestamp' => time(),
            'fixes_applied' => $results['fixes_applied'],
            'fixes_failed' => $results['fixes_failed'],
            'backup_created' => $results['backup_created'],
            'rollback_id' => $results['rollback_id'],
            'details' => $results['fix_details']
        );

        $this->fix_history[] = $session;

        // Keep only last 50 sessions
        if (count($this->fix_history) > 50) {
            $this->fix_history = array_slice($this->fix_history, -50);
        }

        update_option('redco_diagnostic_fix_history', $this->fix_history);
    }

    /**
     * Validate .htaccess syntax
     */
    private function validate_htaccess_syntax($content) {
        // Basic syntax validation for .htaccess
        $lines = explode("\n", $content);
        $module_stack = array();

        foreach ($lines as $line_num => $line) {
            $line = trim($line);

            // Skip empty lines and comments
            if (empty($line) || strpos($line, '#') === 0) {
                continue;
            }

            // Check for module opening tags
            if (preg_match('/^<IfModule\s+(.+)>$/i', $line, $matches)) {
                $module_stack[] = $matches[1];
                continue;
            }

            // Check for module closing tags
            if (preg_match('/^<\/IfModule>$/i', $line)) {
                if (empty($module_stack)) {
                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        error_log("Redco Optimizer: .htaccess validation error - Unmatched </IfModule> at line " . ($line_num + 1));
                    }
                    return false;
                }
                array_pop($module_stack);
                continue;
            }

            // Check for basic directive syntax
            if (!preg_match('/^[A-Za-z][A-Za-z0-9_]*(\s+.*)?$/', $line)) {
                // Allow some common patterns
                if (!preg_match('/^(RewriteRule|RewriteCond|Header|ExpiresByType|AddOutputFilterByType)/', $line)) {
                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        error_log("Redco Optimizer: .htaccess validation warning - Potentially invalid directive at line " . ($line_num + 1) . ": " . $line);
                    }
                }
            }
        }

        // Check for unclosed modules
        if (!empty($module_stack)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("Redco Optimizer: .htaccess validation error - Unclosed IfModule: " . implode(', ', $module_stack));
            }
            return false;
        }

        return true;
    }

    /**
     * Validate PHP syntax for wp-config.php modifications
     */
    private function validate_php_syntax($content) {
        // First try token-based validation (faster and more reliable)
        if ($this->validate_php_syntax_tokens($content)) {
            return true;
        }

        // Fallback to exec-based validation if available
        if (function_exists('exec') && !$this->is_exec_disabled()) {
            return $this->validate_php_syntax_exec($content);
        }

        // If exec is not available, use basic validation
        return $this->validate_php_syntax_basic($content);
    }

    /**
     * Validate PHP syntax using token parsing
     */
    private function validate_php_syntax_tokens($content) {
        try {
            // Use token_get_all to parse PHP tokens
            $tokens = @token_get_all($content);

            if ($tokens === false) {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("Redco Optimizer: PHP token parsing failed");
                }
                return false;
            }

            // Check for basic syntax issues
            $open_braces = 0;
            $open_brackets = 0;
            $open_parens = 0;
            $in_php = false;

            foreach ($tokens as $token) {
                if (is_array($token)) {
                    $token_type = $token[0];
                    $token_content = $token[1];

                    // Track PHP open/close tags
                    if ($token_type === T_OPEN_TAG || $token_type === T_OPEN_TAG_WITH_ECHO) {
                        $in_php = true;
                    } elseif ($token_type === T_CLOSE_TAG) {
                        $in_php = false;
                    }
                } else {
                    // Single character tokens
                    if ($in_php) {
                        switch ($token) {
                            case '{':
                                $open_braces++;
                                break;
                            case '}':
                                $open_braces--;
                                if ($open_braces < 0) {
                                    error_log("Redco Optimizer: PHP syntax error - Unmatched closing brace");
                                    return false;
                                }
                                break;
                            case '[':
                                $open_brackets++;
                                break;
                            case ']':
                                $open_brackets--;
                                if ($open_brackets < 0) {
                                    error_log("Redco Optimizer: PHP syntax error - Unmatched closing bracket");
                                    return false;
                                }
                                break;
                            case '(':
                                $open_parens++;
                                break;
                            case ')':
                                $open_parens--;
                                if ($open_parens < 0) {
                                    error_log("Redco Optimizer: PHP syntax error - Unmatched closing parenthesis");
                                    return false;
                                }
                                break;
                        }
                    }
                }
            }

            // Check for unclosed structures
            if ($open_braces !== 0) {
                error_log("Redco Optimizer: PHP syntax error - Unclosed braces: $open_braces");
                return false;
            }
            if ($open_brackets !== 0) {
                error_log("Redco Optimizer: PHP syntax error - Unclosed brackets: $open_brackets");
                return false;
            }
            if ($open_parens !== 0) {
                error_log("Redco Optimizer: PHP syntax error - Unclosed parentheses: $open_parens");
                return false;
            }

            return true;

        } catch (ParseError $e) {
            error_log("Redco Optimizer: PHP parse error: " . $e->getMessage());
            return false;
        } catch (Exception $e) {
            error_log("Redco Optimizer: PHP validation exception: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Validate PHP syntax using exec (fallback)
     */
    private function validate_php_syntax_exec($content) {
        // Create temporary file for syntax checking
        $temp_file = tempnam(sys_get_temp_dir(), 'redco_php_check_');
        if (!$temp_file) {
            return false;
        }

        file_put_contents($temp_file, $content);

        // Use php -l to check syntax
        $output = array();
        $return_code = 0;
        exec("php -l " . escapeshellarg($temp_file) . " 2>&1", $output, $return_code);

        // Clean up temp file
        unlink($temp_file);

        if ($return_code !== 0) {
            return false;
        }

        return true;
    }

    /**
     * Basic PHP syntax validation (last resort)
     */
    private function validate_php_syntax_basic($content) {
        // Basic checks for common syntax issues

        // Check for PHP opening tag
        if (strpos($content, '<?php') === false && strpos($content, '<?') === false) {
            error_log("Redco Optimizer: PHP validation - No PHP opening tag found");
            return false;
        }

        // Check for basic balance of quotes
        $single_quotes = substr_count($content, "'") - substr_count($content, "\\'");
        $double_quotes = substr_count($content, '"') - substr_count($content, '\\"');

        if ($single_quotes % 2 !== 0) {
            error_log("Redco Optimizer: PHP validation - Unmatched single quotes");
            return false;
        }

        if ($double_quotes % 2 !== 0) {
            error_log("Redco Optimizer: PHP validation - Unmatched double quotes");
            return false;
        }

        // Check for basic balance of braces
        $open_braces = substr_count($content, '{');
        $close_braces = substr_count($content, '}');

        if ($open_braces !== $close_braces) {
            error_log("Redco Optimizer: PHP validation - Unmatched braces");
            return false;
        }

        return true;
    }

    /**
     * Check if exec function is disabled
     */
    private function is_exec_disabled() {
        $disabled_functions = explode(',', ini_get('disable_functions'));
        return in_array('exec', array_map('trim', $disabled_functions));
    }

    /**
     * Validate file permissions and restore if needed
     */
    private function validate_and_restore_permissions($file_path, $original_permissions = null) {
        if ($original_permissions === null) {
            // Get current permissions
            $original_permissions = fileperms($file_path);
        }

        // Ensure file is readable and writable by owner
        $required_permissions = 0644; // rw-r--r--

        if (($original_permissions & 0777) !== $required_permissions) {
            if (chmod($file_path, $required_permissions)) {
                return true;
            } else {
                error_log("Redco Optimizer: Failed to restore permissions for " . $file_path);
                return false;
            }
        }

        return true;
    }

    /**
     * Test file modification safety
     */
    private function test_file_modification_safety($file_path) {
        // Check if file exists and is writable
        if (!file_exists($file_path)) {
            return array('safe' => false, 'reason' => 'File does not exist');
        }

        if (!is_writable($file_path)) {
            return array('safe' => false, 'reason' => 'File is not writable');
        }

        // Check if directory is writable (for backup creation)
        $dir = dirname($file_path);
        if (!is_writable($dir)) {
            return array('safe' => false, 'reason' => 'Directory is not writable for backup creation');
        }

        // Check available disk space (at least 10MB)
        $free_space = disk_free_space($dir);
        if ($free_space !== false && $free_space < 10 * 1024 * 1024) {
            return array('safe' => false, 'reason' => 'Insufficient disk space for backup');
        }

        return array('safe' => true, 'reason' => 'File modification is safe');
    }


}
