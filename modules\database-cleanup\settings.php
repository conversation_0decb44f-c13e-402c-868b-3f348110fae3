<?php
/**
 * Database Cleanup Module Settings
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get module instance
$database_cleanup = new Redco_Database_Cleanup();
$is_enabled = redco_is_module_enabled('database-cleanup');

// Get current settings
$auto_cleanup = redco_get_module_option('database-cleanup', 'auto_cleanup', false);
$cleanup_interval = redco_get_module_option('database-cleanup', 'cleanup_interval', 'weekly');
$cleanup_revisions = redco_get_module_option('database-cleanup', 'cleanup_revisions', true);
$cleanup_auto_drafts = redco_get_module_option('database-cleanup', 'cleanup_auto_drafts', true);
$cleanup_trashed_posts = redco_get_module_option('database-cleanup', 'cleanup_trashed_posts', true);
$cleanup_spam_comments = redco_get_module_option('database-cleanup', 'cleanup_spam_comments', true);
$cleanup_trashed_comments = redco_get_module_option('database-cleanup', 'cleanup_trashed_comments', true);
$cleanup_expired_transients = redco_get_module_option('database-cleanup', 'cleanup_expired_transients', true);
$cleanup_orphaned_postmeta = redco_get_module_option('database-cleanup', 'cleanup_orphaned_postmeta', true);
$cleanup_orphaned_commentmeta = redco_get_module_option('database-cleanup', 'cleanup_orphaned_commentmeta', true);
$keep_revisions = redco_get_module_option('database-cleanup', 'keep_revisions', 5);

// Get current database statistics
$cleanup_stats = redco_get_cleanup_stats();
$stats = array(
    'total_cleaned' => array_sum($cleanup_stats),
    'last_cleanup' => get_option('redco_last_cleanup', 'Never'),
    'auto_cleanup' => $auto_cleanup ? 'Enabled' : 'Disabled',
    'cleanup_interval' => ucfirst($cleanup_interval)
);
?>

<div class="redco-module-tab" data-module="database-cleanup">
    <!-- Enhanced Professional Header Section -->
    <div class="module-header-section">
        <!-- Breadcrumb Navigation -->
        <div class="header-breadcrumb">
            <div class="breadcrumb-nav">
                <a href="<?php echo admin_url('admin.php?page=redco-optimizer'); ?>">
                    <span class="dashicons dashicons-performance"></span>
                    <?php _e('Redco Optimizer', 'redco-optimizer'); ?>
                </a>
                <span class="breadcrumb-separator">›</span>
                <a href="<?php echo admin_url('admin.php?page=redco-optimizer-modules'); ?>">
                    <?php _e('Modules', 'redco-optimizer'); ?>
                </a>
                <span class="breadcrumb-separator">›</span>
                <span class="breadcrumb-current"><?php _e('Database Cleanup', 'redco-optimizer'); ?></span>
            </div>
        </div>

        <!-- Main Header Content -->
        <div class="header-content">
            <div class="header-main">
                <div class="header-icon">
                    <span class="dashicons dashicons-database"></span>
                </div>
                <div class="header-text">
                    <h1><?php _e('Database Cleanup', 'redco-optimizer'); ?></h1>
                    <p><?php _e('Clean and optimize your WordPress database for better performance and reduced storage', 'redco-optimizer'); ?></p>

                    <!-- Status Indicators -->
                    <div class="header-status">
                        <?php if ($is_enabled): ?>
                            <div class="status-badge enabled">
                                <span class="dashicons dashicons-yes-alt"></span>
                                <?php _e('Active', 'redco-optimizer'); ?>
                            </div>
                            <?php if ($auto_cleanup): ?>
                                <div class="status-badge performance">
                                    <span class="dashicons dashicons-clock"></span>
                                    <?php _e('Auto Cleanup', 'redco-optimizer'); ?>
                                </div>
                            <?php endif; ?>
                            <?php if ($cleanup_revisions): ?>
                                <div class="status-badge performance">
                                    <span class="dashicons dashicons-backup"></span>
                                    <?php _e('Revisions', 'redco-optimizer'); ?>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="status-badge disabled">
                                <span class="dashicons dashicons-warning"></span>
                                <?php _e('Inactive', 'redco-optimizer'); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Header Actions -->
            <div class="header-actions">
                <div class="header-action-group">
                    <!-- Quick Actions -->
                    <div class="header-quick-actions">
                        <?php if ($is_enabled): ?>
                            <button type="button" class="header-action-btn" id="run-cleanup-now" data-redco-action="optimize_database">
                                <span class="dashicons dashicons-database"></span>
                                <?php _e('Run Cleanup', 'redco-optimizer'); ?>
                            </button>
                            <button type="button" class="header-action-btn" id="enable-optimal-schedule">
                                <span class="dashicons dashicons-clock"></span>
                                <?php _e('Auto Schedule', 'redco-optimizer'); ?>
                            </button>
                            <a href="<?php echo admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix'); ?>" class="header-action-btn">
                                <span class="dashicons dashicons-admin-tools"></span>
                                <?php _e('Diagnose', 'redco-optimizer'); ?>
                            </a>
                        <?php endif; ?>
                        <a href="<?php echo admin_url('admin.php?page=redco-optimizer-modules'); ?>" class="header-action-btn">
                            <span class="dashicons dashicons-admin-generic"></span>
                            <?php _e('All Modules', 'redco-optimizer'); ?>
                        </a>
                    </div>
                </div>

                <!-- Performance Metrics -->
                <?php if ($is_enabled): ?>
                    <div class="header-metrics">
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo number_format($stats['total_cleaned']); ?>
                            </div>
                            <div class="header-metric-label"><?php _e('Items Cleaned', 'redco-optimizer'); ?></div>
                        </div>
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo $auto_cleanup ? '✓' : '✗'; ?>
                            </div>
                            <div class="header-metric-label"><?php _e('Auto', 'redco-optimizer'); ?></div>
                        </div>
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo ucfirst($cleanup_interval); ?>
                            </div>
                            <div class="header-metric-label"><?php _e('Schedule', 'redco-optimizer'); ?></div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <?php if ($is_enabled): ?>
    <!-- Module Content -->
    <div class="redco-module-content">
        <div class="module-layout">
            <div class="redco-content-main">
                <form class="redco-module-form" data-module="database-cleanup">
                    <!-- Automatic Cleanup Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-clock"></span>
                                <?php _e('Automated Database Maintenance', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="automation-intro">
                                <p class="description">
                                    <?php _e('Set up automated database maintenance to keep your WordPress database clean and optimized without manual intervention. Automated cleanup runs in the background based on your schedule.', 'redco-optimizer'); ?>
                                </p>
                                <div class="automation-status">
                                    <span class="status-label"><?php _e('Current Status:', 'redco-optimizer'); ?></span>
                                    <span class="status-indicator <?php echo $auto_cleanup ? 'enabled' : 'disabled'; ?>" id="automation-status">
                                        <?php echo $auto_cleanup ? __('Enabled', 'redco-optimizer') : __('Disabled', 'redco-optimizer'); ?>
                                    </span>
                                    <?php if ($auto_cleanup): ?>
                                        <span class="next-cleanup"><?php _e('Next cleanup:', 'redco-optimizer'); ?> <strong id="next-cleanup-time"><?php echo ucfirst($cleanup_interval); ?></strong></span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="automation-settings">
                                <h4 class="section-title">
                                    <span class="dashicons dashicons-admin-settings"></span>
                                    <?php _e('Automation Configuration', 'redco-optimizer'); ?>
                                </h4>

                                <div class="automation-control-group">
                                    <div class="automation-toggle">
                                        <label class="toggle-switch">
                                            <input type="checkbox" name="settings[auto_cleanup]" value="1" <?php checked($auto_cleanup); ?> id="auto-cleanup-toggle" class="toggle-input">
                                            <span class="toggle-slider"></span>
                                            <span class="toggle-label">
                                                <strong><?php _e('Enable Automatic Cleanup', 'redco-optimizer'); ?></strong>
                                                <span class="toggle-description"><?php _e('Automatically maintain database health on schedule', 'redco-optimizer'); ?></span>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="schedule-settings" id="schedule-settings" style="<?php echo $auto_cleanup ? '' : 'display: none;'; ?>">
                                        <div class="schedule-control">
                                            <label for="cleanup_interval" class="schedule-label">
                                                <strong><?php _e('Cleanup Schedule', 'redco-optimizer'); ?></strong>
                                                <span class="schedule-description"><?php _e('How often to run automatic maintenance', 'redco-optimizer'); ?></span>
                                            </label>
                                            <div class="schedule-options">
                                                <div class="schedule-option <?php echo $cleanup_interval === 'daily' ? 'selected' : ''; ?>" data-value="daily">
                                                    <input type="radio" name="settings[cleanup_interval]" value="daily" <?php checked($cleanup_interval, 'daily'); ?> id="interval-daily">
                                                    <label for="interval-daily" class="schedule-option-label">
                                                        <span class="option-icon">📅</span>
                                                        <span class="option-content">
                                                            <span class="option-title"><?php _e('Daily', 'redco-optimizer'); ?></span>
                                                            <span class="option-description"><?php _e('Every 24 hours', 'redco-optimizer'); ?></span>
                                                            <span class="option-recommendation high-traffic"><?php _e('High Traffic Sites', 'redco-optimizer'); ?></span>
                                                        </span>
                                                    </label>
                                                </div>

                                                <div class="schedule-option <?php echo $cleanup_interval === 'weekly' ? 'selected' : ''; ?>" data-value="weekly">
                                                    <input type="radio" name="settings[cleanup_interval]" value="weekly" <?php checked($cleanup_interval, 'weekly'); ?> id="interval-weekly">
                                                    <label for="interval-weekly" class="schedule-option-label">
                                                        <span class="option-icon">📊</span>
                                                        <span class="option-content">
                                                            <span class="option-title"><?php _e('Weekly', 'redco-optimizer'); ?></span>
                                                            <span class="option-description"><?php _e('Every 7 days', 'redco-optimizer'); ?></span>
                                                            <span class="option-recommendation recommended"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                                        </span>
                                                    </label>
                                                </div>

                                                <div class="schedule-option <?php echo $cleanup_interval === 'monthly' ? 'selected' : ''; ?>" data-value="monthly">
                                                    <input type="radio" name="settings[cleanup_interval]" value="monthly" <?php checked($cleanup_interval, 'monthly'); ?> id="interval-monthly">
                                                    <label for="interval-monthly" class="schedule-option-label">
                                                        <span class="option-icon">🗓️</span>
                                                        <span class="option-content">
                                                            <span class="option-title"><?php _e('Monthly', 'redco-optimizer'); ?></span>
                                                            <span class="option-description"><?php _e('Every 30 days', 'redco-optimizer'); ?></span>
                                                            <span class="option-recommendation low-traffic"><?php _e('Low Traffic Sites', 'redco-optimizer'); ?></span>
                                                        </span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="schedule-info">
                                            <div class="info-grid">
                                                <div class="info-item">
                                                    <span class="info-label"><?php _e('Last Cleanup:', 'redco-optimizer'); ?></span>
                                                    <span class="info-value" id="last-cleanup-display"><?php echo $stats['last_cleanup']; ?></span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label"><?php _e('Items Cleaned:', 'redco-optimizer'); ?></span>
                                                    <span class="info-value"><?php echo number_format($stats['total_cleaned']); ?></span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label"><?php _e('Automation Status:', 'redco-optimizer'); ?></span>
                                                    <span class="info-value status-<?php echo $auto_cleanup ? 'active' : 'inactive'; ?>"><?php echo $stats['auto_cleanup']; ?></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="automation-benefits">
                                <h4 class="benefits-title">
                                    <span class="dashicons dashicons-yes-alt"></span>
                                    <?php _e('Automation Benefits', 'redco-optimizer'); ?>
                                </h4>
                                <div class="benefits-grid">
                                    <div class="benefit-item">
                                        <span class="benefit-icon">⚡</span>
                                        <span class="benefit-text">
                                            <strong><?php _e('Consistent Performance', 'redco-optimizer'); ?></strong>
                                            <span><?php _e('Maintains optimal database speed automatically', 'redco-optimizer'); ?></span>
                                        </span>
                                    </div>
                                    <div class="benefit-item">
                                        <span class="benefit-icon">🛡️</span>
                                        <span class="benefit-text">
                                            <strong><?php _e('Preventive Maintenance', 'redco-optimizer'); ?></strong>
                                            <span><?php _e('Prevents database bloat before it becomes a problem', 'redco-optimizer'); ?></span>
                                        </span>
                                    </div>
                                    <div class="benefit-item">
                                        <span class="benefit-icon">💾</span>
                                        <span class="benefit-text">
                                            <strong><?php _e('Storage Optimization', 'redco-optimizer'); ?></strong>
                                            <span><?php _e('Reduces database size and hosting costs', 'redco-optimizer'); ?></span>
                                        </span>
                                    </div>
                                    <div class="benefit-item">
                                        <span class="benefit-icon">🔄</span>
                                        <span class="benefit-text">
                                            <strong><?php _e('Hands-Free Operation', 'redco-optimizer'); ?></strong>
                                            <span><?php _e('No manual intervention required', 'redco-optimizer'); ?></span>
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="automation-summary">
                                <div class="summary-stats">
                                    <span class="automation-efficiency"><?php _e('Automation efficiency: High', 'redco-optimizer'); ?></span>
                                    <span class="maintenance-impact"><?php _e('Maintenance impact: Minimal', 'redco-optimizer'); ?></span>
                                </div>
                                <div class="automation-notice">
                                    <span class="dashicons dashicons-info"></span>
                                    <?php _e('Automated cleanup runs during low-traffic periods to minimize impact on your website performance.', 'redco-optimizer'); ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Cleanup Options Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-admin-tools"></span>
                                <?php _e('Database Cleanup Options', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="cleanup-intro">
                                <p class="description">
                                    <?php _e('Select database items to clean up automatically. Items will be removed based on your schedule settings. Hover over items for detailed information.', 'redco-optimizer'); ?>
                                </p>
                                <div class="cleanup-summary">
                                    <span class="total-items"><?php printf(__('Total cleanable items: %s', 'redco-optimizer'), '<strong>' . number_format(array_sum($cleanup_stats)) . '</strong>'); ?></span>
                                    <span class="estimated-space"><?php _e('Estimated space savings: ~2-15MB', 'redco-optimizer'); ?></span>
                                </div>
                            </div>

                            <div class="cleanup-options" style="display: grid; gap: 15px; margin-top: 20px;">
            <!-- Content Cleanup Section -->
            <div class="cleanup-section">
                <h4 class="section-title">
                    <span class="dashicons dashicons-edit"></span>
                    <?php _e('Content Cleanup', 'redco-optimizer'); ?>
                </h4>

                <div class="cleanup-option recommended" data-impact="medium" title="<?php _e('Recommended: Removes old post revisions that accumulate over time', 'redco-optimizer'); ?>">
                    <label class="checkbox-item">
                        <input type="checkbox"
                               name="settings[cleanup_revisions]"
                               value="1"
                               <?php checked($cleanup_revisions); ?>>
                        <span class="option-icon">📝</span>
                        <span class="option-text">
                            <strong><?php _e('Post Revisions', 'redco-optimizer'); ?></strong>
                            <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                        </span>
                        <?php echo redco_help_icon('database-cleanup', 'cleanup-options', 'post_revisions', 'Remove old post revisions to save database space'); ?>
                        <span class="count high">(<?php echo number_format($cleanup_stats['revisions']); ?>)</span>
                    </label>
                    <div class="description">
                        <?php _e('Remove excess post revisions while keeping your latest changes safe. Revisions accumulate over time and can significantly increase database size.', 'redco-optimizer'); ?>
                        <div class="impact-info">
                            <span class="impact-level medium"><?php _e('Impact: Medium', 'redco-optimizer'); ?></span>
                            <span class="safety-level safe"><?php _e('Safety: Safe', 'redco-optimizer'); ?></span>
                        </div>
                    </div>
                    <div class="sub-option">
                        <label for="keep_revisions"><?php _e('Keep latest:', 'redco-optimizer'); ?></label>
                        <input type="number"
                               name="settings[keep_revisions]"
                               id="keep_revisions"
                               value="<?php echo esc_attr($keep_revisions); ?>"
                               min="0"
                               max="50"
                               style="width: 80px;">
                        <span><?php _e('revisions per post', 'redco-optimizer'); ?></span>
                        <small class="help-text"><?php _e('WordPress default: unlimited. Recommended: 3-5 revisions.', 'redco-optimizer'); ?></small>
                    </div>
                </div>

                <div class="cleanup-option recommended" data-impact="low" title="<?php _e('Recommended: Removes automatically created draft posts', 'redco-optimizer'); ?>">
                    <label class="checkbox-item">
                        <input type="checkbox"
                               name="settings[cleanup_auto_drafts]"
                               value="1"
                               <?php checked($cleanup_auto_drafts); ?>>
                        <span class="option-icon">📄</span>
                        <span class="option-text">
                            <strong><?php _e('Auto-Draft Posts', 'redco-optimizer'); ?></strong>
                            <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                        </span>
                        <span class="count medium">(<?php echo number_format($cleanup_stats['auto_drafts']); ?>)</span>
                    </label>
                    <div class="description">
                        <?php _e('Remove auto-draft posts older than 7 days. These are created automatically when you start writing and are safe to remove.', 'redco-optimizer'); ?>
                        <div class="impact-info">
                            <span class="impact-level low"><?php _e('Impact: Low', 'redco-optimizer'); ?></span>
                            <span class="safety-level safe"><?php _e('Safety: Safe', 'redco-optimizer'); ?></span>
                        </div>
                    </div>
                </div>

                <div class="cleanup-option" data-impact="high" title="<?php _e('Caution: Permanently removes trashed posts', 'redco-optimizer'); ?>">
                    <label class="checkbox-item">
                        <input type="checkbox"
                               name="settings[cleanup_trashed_posts]"
                               value="1"
                               <?php checked($cleanup_trashed_posts); ?>>
                        <span class="option-icon">🗑️</span>
                        <span class="option-text">
                            <strong><?php _e('Trashed Posts', 'redco-optimizer'); ?></strong>
                            <span class="caution-badge"><?php _e('Caution', 'redco-optimizer'); ?></span>
                        </span>
                        <span class="count low">(<?php echo number_format($cleanup_stats['trashed_posts']); ?>)</span>
                    </label>
                    <div class="description">
                        <?php _e('Permanently remove posts in trash older than 30 days. Cannot be undone! Review your trash before enabling.', 'redco-optimizer'); ?>
                        <div class="impact-info">
                            <span class="impact-level high"><?php _e('Impact: High', 'redco-optimizer'); ?></span>
                            <span class="safety-level caution"><?php _e('Safety: Caution', 'redco-optimizer'); ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Comments Cleanup Section -->
            <div class="cleanup-section">
                <h4 class="section-title">
                    <span class="dashicons dashicons-admin-comments"></span>
                    <?php _e('Comments Cleanup', 'redco-optimizer'); ?>
                </h4>

                <div class="cleanup-option recommended" data-impact="low" title="<?php _e('Recommended: Removes spam comments that clutter your database', 'redco-optimizer'); ?>">
                    <label class="checkbox-item">
                        <input type="checkbox"
                               name="settings[cleanup_spam_comments]"
                               value="1"
                               <?php checked($cleanup_spam_comments); ?>>
                        <span class="option-icon">🚫</span>
                        <span class="option-text">
                            <strong><?php _e('Spam Comments', 'redco-optimizer'); ?></strong>
                            <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                        </span>
                        <?php echo redco_help_icon('database-cleanup', 'cleanup-options', 'spam_comments', 'Remove comments marked as spam'); ?>
                        <span class="count high">(<?php echo number_format($cleanup_stats['spam_comments']); ?>)</span>
                    </label>
                    <div class="description">
                        <?php _e('Remove all comments marked as spam. These are usually unwanted and safe to delete.', 'redco-optimizer'); ?>
                        <div class="impact-info">
                            <span class="impact-level low"><?php _e('Impact: Low', 'redco-optimizer'); ?></span>
                            <span class="safety-level safe"><?php _e('Safety: Safe', 'redco-optimizer'); ?></span>
                        </div>
                    </div>
                </div>

                <div class="cleanup-option" data-impact="medium" title="<?php _e('Removes comments in trash - review before enabling', 'redco-optimizer'); ?>">
                    <label class="checkbox-item">
                        <input type="checkbox"
                               name="settings[cleanup_trashed_comments]"
                               value="1"
                               <?php checked($cleanup_trashed_comments); ?>>
                        <span class="option-icon">💬</span>
                        <span class="option-text">
                            <strong><?php _e('Trashed Comments', 'redco-optimizer'); ?></strong>
                        </span>
                        <span class="count medium">(<?php echo number_format($cleanup_stats['trashed_comments']); ?>)</span>
                    </label>
                    <div class="description">
                        <?php _e('Remove all comments in trash. Review your trash before enabling this option.', 'redco-optimizer'); ?>
                        <div class="impact-info">
                            <span class="impact-level medium"><?php _e('Impact: Medium', 'redco-optimizer'); ?></span>
                            <span class="safety-level review"><?php _e('Safety: Review', 'redco-optimizer'); ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Cleanup Section -->
            <div class="cleanup-section">
                <h4 class="section-title">
                    <span class="dashicons dashicons-performance"></span>
                    <?php _e('System & Performance Cleanup', 'redco-optimizer'); ?>
                </h4>

                <div class="cleanup-option recommended" data-impact="low" title="<?php _e('Recommended: Removes expired cache data for better performance', 'redco-optimizer'); ?>">
                    <label class="checkbox-item">
                        <input type="checkbox"
                               name="settings[cleanup_expired_transients]"
                               value="1"
                               <?php checked($cleanup_expired_transients); ?>>
                        <span class="option-icon">⚡</span>
                        <span class="option-text">
                            <strong><?php _e('Expired Transients', 'redco-optimizer'); ?></strong>
                            <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                        </span>
                        <?php echo redco_help_icon('database-cleanup', 'cleanup-options', 'expired_transients', 'Remove expired transient data that WordPress and plugins use for temporary caching'); ?>
                        <span class="count high">(<?php echo number_format($cleanup_stats['expired_transients']); ?>)</span>
                    </label>
                    <div class="description">
                        <?php _e('Remove expired transient cache data. Improves database performance and reduces size.', 'redco-optimizer'); ?>
                        <div class="impact-info">
                            <span class="impact-level low"><?php _e('Impact: Low', 'redco-optimizer'); ?></span>
                            <span class="safety-level safe"><?php _e('Safety: Safe', 'redco-optimizer'); ?></span>
                        </div>
                    </div>
                </div>

                <div class="cleanup-option advanced" data-impact="medium" title="<?php _e('Advanced: Removes metadata for deleted posts', 'redco-optimizer'); ?>">
                    <label class="checkbox-item">
                        <input type="checkbox"
                               name="settings[cleanup_orphaned_postmeta]"
                               value="1"
                               <?php checked($cleanup_orphaned_postmeta); ?>>
                        <span class="option-icon">🔗</span>
                        <span class="option-text">
                            <strong><?php _e('Orphaned Post Meta', 'redco-optimizer'); ?></strong>
                            <span class="advanced-badge"><?php _e('Advanced', 'redco-optimizer'); ?></span>
                        </span>
                        <span class="count medium">(<?php echo number_format($cleanup_stats['orphaned_postmeta']); ?>)</span>
                    </label>
                    <div class="description">
                        <?php _e('Remove metadata for posts that no longer exist. Cleans up database relationships.', 'redco-optimizer'); ?>
                        <div class="impact-info">
                            <span class="impact-level medium"><?php _e('Impact: Medium', 'redco-optimizer'); ?></span>
                            <span class="safety-level safe"><?php _e('Safety: Safe', 'redco-optimizer'); ?></span>
                        </div>
                    </div>
                </div>

                <div class="cleanup-option advanced" data-impact="medium" title="<?php _e('Advanced: Removes metadata for deleted comments', 'redco-optimizer'); ?>">
                    <label class="checkbox-item">
                        <input type="checkbox"
                               name="settings[cleanup_orphaned_commentmeta]"
                               value="1"
                               <?php checked($cleanup_orphaned_commentmeta); ?>>
                        <span class="option-icon">💭</span>
                        <span class="option-text">
                            <strong><?php _e('Orphaned Comment Meta', 'redco-optimizer'); ?></strong>
                            <span class="advanced-badge"><?php _e('Advanced', 'redco-optimizer'); ?></span>
                        </span>
                        <span class="count low">(<?php echo number_format($cleanup_stats['orphaned_commentmeta']); ?>)</span>
                    </label>
                    <div class="description">
                        <?php _e('Remove metadata for comments that no longer exist. Maintains database integrity.', 'redco-optimizer'); ?>
                        <div class="impact-info">
                            <span class="impact-level medium"><?php _e('Impact: Medium', 'redco-optimizer'); ?></span>
                            <span class="safety-level safe"><?php _e('Safety: Safe', 'redco-optimizer'); ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cleanup Summary -->
            <div class="cleanup-summary-footer">
                <div class="summary-stats">
                    <span class="selected-count">0 <?php _e('items selected', 'redco-optimizer'); ?></span>
                    <span class="estimated-impact"><?php _e('Estimated impact: Low', 'redco-optimizer'); ?></span>
                </div>
                <div class="safety-notice">
                    <span class="dashicons dashicons-info"></span>
                    <?php _e('Always backup your database before running cleanup operations.', 'redco-optimizer'); ?>
                </div>
            </div>
                            </div>
                        </div>
                    </div>

                </form>
            </div>

            <!-- Sidebar -->
            <div class="redco-content-sidebar">
                <!-- Database Statistics -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-chart-bar"></span>
                            <?php _e('Database Statistics', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="stats-grid">
                            <div class="stat-item stat-revisions">
                                <span class="stat-value"><?php echo number_format($cleanup_stats['revisions']); ?></span>
                                <span class="stat-label"><?php _e('Revisions', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-drafts">
                                <span class="stat-value"><?php echo number_format($cleanup_stats['auto_drafts']); ?></span>
                                <span class="stat-label"><?php _e('Auto Drafts', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-spam">
                                <span class="stat-value"><?php echo number_format($cleanup_stats['spam_comments']); ?></span>
                                <span class="stat-label"><?php _e('Spam Comments', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-transients">
                                <span class="stat-value"><?php echo number_format($cleanup_stats['expired_transients']); ?></span>
                                <span class="stat-label"><?php _e('Transients', 'redco-optimizer'); ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Manual Cleanup -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-admin-tools"></span>
                            <?php _e('Manual Cleanup', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <button type="button" id="run-cleanup-now" class="button button-secondary" data-redco-action="optimize_database" style="width: 100%; margin-bottom: 10px;">
                            <span class="dashicons dashicons-database"></span>
                            <?php _e('Run Cleanup Now', 'redco-optimizer'); ?>
                        </button>
                        <p class="description">
                            <?php _e('Run database cleanup immediately with current settings.', 'redco-optimizer'); ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php else: ?>
    <!-- Module Disabled State -->
    <div class="redco-module-disabled">
        <div class="disabled-message">
            <span class="dashicons dashicons-database"></span>
            <h3><?php _e('Database Cleanup Module Disabled', 'redco-optimizer'); ?></h3>
            <p><?php _e('This module is currently disabled. Enable it from the Modules page to access database cleanup and optimization features.', 'redco-optimizer'); ?></p>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
jQuery(document).ready(function($) {
    // Enhanced Database Cleanup Options Functionality

    // Track selected items and update summary
    function updateCleanupSummary() {
        const selectedCheckboxes = $('.cleanup-option input[type="checkbox"]:checked');
        const selectedCount = selectedCheckboxes.length;

        // Update selected count
        $('.selected-count').text(selectedCount + ' <?php _e("items selected", "redco-optimizer"); ?>');

        // Calculate estimated impact
        let impactLevel = 'Low';
        let hasHighImpact = false;
        let hasMediumImpact = false;

        selectedCheckboxes.each(function() {
            const option = $(this).closest('.cleanup-option');
            const impact = option.data('impact');

            if (impact === 'high') {
                hasHighImpact = true;
            } else if (impact === 'medium') {
                hasMediumImpact = true;
            }
        });

        if (hasHighImpact) {
            impactLevel = 'High';
        } else if (hasMediumImpact) {
            impactLevel = 'Medium';
        }

        $('.estimated-impact').text('<?php _e("Estimated impact:", "redco-optimizer"); ?> ' + impactLevel);

        // Update impact color
        $('.estimated-impact').removeClass('impact-low impact-medium impact-high');
        $('.estimated-impact').addClass('impact-' + impactLevel.toLowerCase());
    }

    // Select All button functionality
    $('#select-all-cleanup').on('click', function(e) {
        e.preventDefault();

        const button = $(this);
        const allCheckboxes = $('.cleanup-option input[type="checkbox"]');
        const allChecked = allCheckboxes.length === allCheckboxes.filter(':checked').length;

        if (allChecked) {
            // Uncheck all
            allCheckboxes.prop('checked', false);
            button.text('<?php _e("Select All", "redco-optimizer"); ?>');
            button.removeClass('button-primary').addClass('button-secondary');
        } else {
            // Check all
            allCheckboxes.prop('checked', true);
            button.text('<?php _e("Unselect All", "redco-optimizer"); ?>');
            button.removeClass('button-secondary').addClass('button-primary');
        }

        updateCleanupSummary();

        // Add visual feedback
        $('.cleanup-option').addClass('selection-highlight');
        setTimeout(function() {
            $('.cleanup-option').removeClass('selection-highlight');
        }, 300);
    });

    // Recommended button functionality
    $('#select-recommended').on('click', function(e) {
        e.preventDefault();

        const button = $(this);
        const recommendedCheckboxes = $('.cleanup-option.recommended input[type="checkbox"]');
        const allRecommendedChecked = recommendedCheckboxes.length === recommendedCheckboxes.filter(':checked').length;

        if (allRecommendedChecked) {
            // Uncheck recommended
            recommendedCheckboxes.prop('checked', false);
            button.text('<?php _e("Select Recommended", "redco-optimizer"); ?>');
            button.removeClass('button-primary').addClass('button-secondary');
        } else {
            // First uncheck all
            $('.cleanup-option input[type="checkbox"]').prop('checked', false);

            // Then check only recommended
            recommendedCheckboxes.prop('checked', true);
            button.text('<?php _e("Unselect Recommended", "redco-optimizer"); ?>');
            button.removeClass('button-secondary').addClass('button-primary');

            // Update Select All button
            $('#select-all-cleanup').text('<?php _e("Select All", "redco-optimizer"); ?>');
            $('#select-all-cleanup').removeClass('button-primary').addClass('button-secondary');
        }

        updateCleanupSummary();

        // Add visual feedback to recommended items
        $('.cleanup-option.recommended').addClass('selection-highlight');
        setTimeout(function() {
            $('.cleanup-option.recommended').removeClass('selection-highlight');
        }, 300);
    });

    // Individual checkbox change handler
    $('.cleanup-option input[type="checkbox"]').on('change', function() {
        updateCleanupSummary();

        // Update button states based on current selection
        const allCheckboxes = $('.cleanup-option input[type="checkbox"]');
        const recommendedCheckboxes = $('.cleanup-option.recommended input[type="checkbox"]');
        const allChecked = allCheckboxes.length === allCheckboxes.filter(':checked').length;
        const allRecommendedChecked = recommendedCheckboxes.length === recommendedCheckboxes.filter(':checked').length;
        const onlyRecommendedChecked = allRecommendedChecked && (allCheckboxes.filter(':checked').length === recommendedCheckboxes.length);

        // Update Select All button
        if (allChecked) {
            $('#select-all-cleanup').text('<?php _e("Unselect All", "redco-optimizer"); ?>');
            $('#select-all-cleanup').removeClass('button-secondary').addClass('button-primary');
        } else {
            $('#select-all-cleanup').text('<?php _e("Select All", "redco-optimizer"); ?>');
            $('#select-all-cleanup').removeClass('button-primary').addClass('button-secondary');
        }

        // Update Recommended button
        if (onlyRecommendedChecked) {
            $('#select-recommended').text('<?php _e("Unselect Recommended", "redco-optimizer"); ?>');
            $('#select-recommended').removeClass('button-secondary').addClass('button-primary');
        } else {
            $('#select-recommended').text('<?php _e("Select Recommended", "redco-optimizer"); ?>');
            $('#select-recommended').removeClass('button-primary').addClass('button-secondary');
        }
    });

    // Initialize summary on page load
    updateCleanupSummary();

    // Add tooltips for better UX
    $('.cleanup-option[title]').each(function() {
        $(this).attr('data-tooltip', $(this).attr('title'));
        $(this).removeAttr('title');
    });

    // Enhanced hover effects
    $('.cleanup-option').on('mouseenter', function() {
        $(this).addClass('option-hover');

        // Show tooltip if exists
        const tooltip = $(this).data('tooltip');
        if (tooltip) {
            const tooltipEl = $('<div class="cleanup-tooltip">' + tooltip + '</div>');
            $('body').append(tooltipEl);

            const rect = this.getBoundingClientRect();
            tooltipEl.css({
                top: rect.top - tooltipEl.outerHeight() - 5,
                left: rect.left + (rect.width / 2) - (tooltipEl.outerWidth() / 2)
            });
        }
    }).on('mouseleave', function() {
        $(this).removeClass('option-hover');
        $('.cleanup-tooltip').remove();
    });

    // Keyboard accessibility
    $('.cleanup-option input[type="checkbox"]').on('keydown', function(e) {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            $(this).click();
        }
    });

    // Enhanced Automatic Cleanup Functionality

    // Toggle schedule settings visibility
    function toggleScheduleSettings() {
        const isEnabled = $('#auto-cleanup-toggle').is(':checked');
        const scheduleSettings = $('#schedule-settings');
        const statusIndicator = $('#automation-status');
        const nextCleanupTime = $('#next-cleanup-time');

        if (isEnabled) {
            scheduleSettings.slideDown(300);
            statusIndicator.removeClass('disabled').addClass('enabled').text('<?php _e("Enabled", "redco-optimizer"); ?>');
            updateNextCleanupDisplay();
        } else {
            scheduleSettings.slideUp(300);
            statusIndicator.removeClass('enabled').addClass('disabled').text('<?php _e("Disabled", "redco-optimizer"); ?>');
        }

        updateAutomationSummary();
    }

    // Update next cleanup display
    function updateNextCleanupDisplay() {
        const selectedInterval = $('input[name="settings[cleanup_interval]"]:checked').val();
        let nextCleanupText = '';

        switch(selectedInterval) {
            case 'daily':
                nextCleanupText = '<?php _e("Daily", "redco-optimizer"); ?>';
                break;
            case 'weekly':
                nextCleanupText = '<?php _e("Weekly", "redco-optimizer"); ?>';
                break;
            case 'monthly':
                nextCleanupText = '<?php _e("Monthly", "redco-optimizer"); ?>';
                break;
        }

        $('#next-cleanup-time').text(nextCleanupText);
    }

    // Update automation summary
    function updateAutomationSummary() {
        const isEnabled = $('#auto-cleanup-toggle').is(':checked');
        const selectedInterval = $('input[name="settings[cleanup_interval]"]:checked').val();

        let efficiency = '<?php _e("High", "redco-optimizer"); ?>';
        let impact = '<?php _e("Minimal", "redco-optimizer"); ?>';

        if (!isEnabled) {
            efficiency = '<?php _e("Disabled", "redco-optimizer"); ?>';
            impact = '<?php _e("Manual Only", "redco-optimizer"); ?>';
        } else if (selectedInterval === 'daily') {
            efficiency = '<?php _e("Very High", "redco-optimizer"); ?>';
            impact = '<?php _e("Low", "redco-optimizer"); ?>';
        }

        $('.automation-efficiency').text('<?php _e("Automation efficiency:", "redco-optimizer"); ?> ' + efficiency);
        $('.maintenance-impact').text('<?php _e("Maintenance impact:", "redco-optimizer"); ?> ' + impact);
    }

    // Enable Optimal Schedule button
    $('#enable-optimal-schedule').on('click', function(e) {
        e.preventDefault();

        const button = $(this);
        button.addClass('loading').text('<?php _e("Applying...", "redco-optimizer"); ?>');

        setTimeout(() => {
            // Enable automation
            $('#auto-cleanup-toggle').prop('checked', true);

            // Set weekly schedule (optimal for most sites)
            $('input[name="settings[cleanup_interval]"][value="weekly"]').prop('checked', true);

            // Update schedule option selection
            $('.schedule-option').removeClass('selected');
            $('.schedule-option[data-value="weekly"]').addClass('selected');

            toggleScheduleSettings();
            updateNextCleanupDisplay();

            button.removeClass('loading').text('<?php _e("Enable Optimal Schedule", "redco-optimizer"); ?>');

            // Show success notification
            showAutomationNotification('<?php _e("Optimal automation schedule enabled! Weekly cleanup will maintain your database automatically.", "redco-optimizer"); ?>', 'success');

            // Highlight the automation section
            $('.automation-control-group').addClass('settings-updated');
            setTimeout(() => {
                $('.automation-control-group').removeClass('settings-updated');
            }, 1000);
        }, 500);
    });

    // Disable Automation button
    $('#disable-auto-cleanup').on('click', function(e) {
        e.preventDefault();

        const button = $(this);
        button.addClass('loading').text('<?php _e("Disabling...", "redco-optimizer"); ?>');

        setTimeout(() => {
            // Disable automation
            $('#auto-cleanup-toggle').prop('checked', false);

            toggleScheduleSettings();

            button.removeClass('loading').text('<?php _e("Disable Automation", "redco-optimizer"); ?>');

            showAutomationNotification('<?php _e("Automatic cleanup disabled. You can still run manual cleanups anytime.", "redco-optimizer"); ?>', 'info');

            // Highlight the automation section
            $('.automation-control-group').addClass('settings-updated');
            setTimeout(() => {
                $('.automation-control-group').removeClass('settings-updated');
            }, 1000);
        }, 500);
    });

    // Automation toggle change
    $('#auto-cleanup-toggle').on('change', function() {
        toggleScheduleSettings();

        // Visual feedback
        $('.automation-toggle').addClass('toggle-changed');
        setTimeout(() => {
            $('.automation-toggle').removeClass('toggle-changed');
        }, 500);
    });

    // Schedule option changes
    $('input[name="settings[cleanup_interval]"]').on('change', function() {
        const selectedValue = $(this).val();

        // Update visual selection
        $('.schedule-option').removeClass('selected');
        $('.schedule-option[data-value="' + selectedValue + '"]').addClass('selected');

        updateNextCleanupDisplay();
        updateAutomationSummary();

        // Visual feedback
        $('.schedule-option[data-value="' + selectedValue + '"]').addClass('option-selected');
        setTimeout(() => {
            $('.schedule-option').removeClass('option-selected');
        }, 300);
    });

    // Schedule option click handling (for better UX)
    $('.schedule-option').on('click', function() {
        const value = $(this).data('value');
        const radio = $(this).find('input[type="radio"]');

        if (!radio.is(':checked')) {
            radio.prop('checked', true).trigger('change');
        }
    });

    // Page-specific notification system removed - using global toast notifications

    // Initialize automation settings
    toggleScheduleSettings();
    updateAutomationSummary();

    // Set initial schedule option selection
    const currentInterval = $('input[name="settings[cleanup_interval]"]:checked').val();
    $('.schedule-option[data-value="' + currentInterval + '"]').addClass('selected');

    // Database Cleanup Options enhanced functionality loaded
    // Automated Database Maintenance enhanced functionality loaded
});
</script>


