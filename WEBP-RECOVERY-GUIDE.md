# 🔧 WebP Recovery Guide
## Complete Solution for Recovering Deleted Original Images

This guide provides a comprehensive solution for recovering original images when they were deleted during WebP conversion with "Backup Originals" disabled.

---

## 🔍 **Problem Analysis**

When WebP conversion is performed with "Backup Originals" disabled, the plugin:
1. ✅ Converts images to WebP format
2. ❌ Deletes original JPG/PNG/GIF files  
3. ❌ Updates WordPress to point to WebP files
4. ❌ Causes 404 errors when original URLs are requested

---

## 📊 **How Original Format is Identified**

The plugin stores conversion metadata in `_webp_conversion_data` which contains:

```json
{
  "converted": true,
  "file_path": "/path/to/original/image.jpg",
  "webp_path": "/path/to/converted/image.jpg.webp",
  "original_size": 150000,
  "webp_size": 95000,
  "conversion_date": "2024-01-15 10:30:45"
}
```

**Key insight:** The `file_path` contains the original file extension (jpg, png, gif)!

---

## 🛠️ **Recovery Methods**

### **Method 1: Database Analysis (Safe)**

Run this SQL query in phpMyAdmin to analyze your situation:

```sql
-- Check how many images need recovery
SELECT 
    COUNT(*) as total_converted,
    SUM(CASE WHEN meta_value LIKE '%jpg%' OR meta_value LIKE '%jpeg%' THEN 1 ELSE 0 END) as jpeg_images,
    SUM(CASE WHEN meta_value LIKE '%png%' THEN 1 ELSE 0 END) as png_images,
    SUM(CASE WHEN meta_value LIKE '%gif%' THEN 1 ELSE 0 END) as gif_images
FROM wp_postmeta 
WHERE meta_key = '_webp_conversion_data' 
AND meta_value LIKE '%"converted":true%';
```

### **Method 2: Complete SQL Recovery Script**

1. **Download the SQL script:** `webp-recovery-script.sql`
2. **Open phpMyAdmin**
3. **Select your WordPress database**
4. **Go to SQL tab**
5. **Import and run the script**

The script will:
- ✅ Analyze converted images
- ✅ Identify original formats
- ✅ Restore correct MIME types
- ✅ Update file path references
- ✅ Provide verification results

### **Method 3: File System Recovery**

1. **Download the PHP script:** `webp-file-recovery.php`
2. **Upload to your WordPress root directory**
3. **Run via browser:** `yoursite.com/webp-file-recovery.php?run=1`
4. **Or via command line:** `php webp-file-recovery.php`

The script will:
- ✅ Convert WebP files back to original formats
- ✅ Preserve image quality (90% JPEG, PNG level 6)
- ✅ Update WordPress database references
- ✅ Handle transparency for PNG images
- ✅ Provide detailed progress reports

---

## 📋 **Step-by-Step Recovery Process**

### **Step 1: Backup Everything**
```bash
# Backup database
mysqldump -u username -p database_name > backup.sql

# Backup uploads directory
tar -czf uploads-backup.tar.gz wp-content/uploads/
```

### **Step 2: Run Analysis**
```sql
-- Run this first to understand your situation
SELECT 
    pm.post_id,
    p.post_title,
    CASE 
        WHEN pm.meta_value LIKE '%jpg%' OR pm.meta_value LIKE '%jpeg%' THEN 'JPEG'
        WHEN pm.meta_value LIKE '%png%' THEN 'PNG'
        WHEN pm.meta_value LIKE '%gif%' THEN 'GIF'
        ELSE 'UNKNOWN'
    END as original_format
FROM wp_postmeta pm
INNER JOIN wp_posts p ON pm.post_id = p.ID
WHERE pm.meta_key = '_webp_conversion_data' 
AND pm.meta_value LIKE '%"converted":true%'
LIMIT 10;
```

### **Step 3: Database Recovery**
Run the complete SQL recovery script to restore MIME types and file references.

### **Step 4: File Recovery**
Run the PHP script to convert WebP files back to original formats.

### **Step 5: Verification**
```sql
-- Verify recovery success
SELECT 
    post_mime_type,
    COUNT(*) as count
FROM wp_posts 
WHERE post_type = 'attachment' 
AND ID IN (
    SELECT post_id FROM wp_postmeta 
    WHERE meta_key = '_webp_conversion_data'
)
GROUP BY post_mime_type;
```

---

## 🎯 **Expected Results**

### **Before Recovery:**
- ❌ Original files deleted
- ❌ WordPress points to WebP files
- ❌ 404 errors for original URLs
- ❌ Console errors in browser

### **After Recovery:**
- ✅ Original files restored from WebP
- ✅ WordPress points to original files
- ✅ All URLs work correctly
- ✅ No console errors
- ✅ Images display properly

---

## 🔧 **Manual Recovery (Advanced)**

If automated scripts don't work, you can manually recover images:

### **1. Extract File Paths:**
```sql
SELECT 
    post_id,
    SUBSTRING_INDEX(SUBSTRING_INDEX(meta_value, '"file_path":"', -1), '"', 1) as original_path,
    SUBSTRING_INDEX(SUBSTRING_INDEX(meta_value, '"webp_path":"', -1), '"', 1) as webp_path
FROM wp_postmeta 
WHERE meta_key = '_webp_conversion_data' 
AND meta_value LIKE '%"converted":true%';
```

### **2. Convert Files Using ImageMagick:**
```bash
# Convert WebP to JPEG
convert image.jpg.webp -quality 90 image.jpg

# Convert WebP to PNG (preserve transparency)
convert image.png.webp image.png

# Batch convert all WebP files
find /path/to/uploads -name "*.webp" -exec sh -c 'convert "$1" "${1%.webp}"' _ {} \;
```

### **3. Update Database:**
```sql
-- Restore JPEG MIME types
UPDATE wp_posts 
SET post_mime_type = 'image/jpeg' 
WHERE ID IN (SELECT post_id FROM wp_postmeta WHERE meta_key = '_webp_conversion_data' AND meta_value LIKE '%jpg%');
```

---

## ⚠️ **Important Notes**

### **Quality Considerations:**
- WebP to JPEG: Some quality loss is inevitable
- WebP to PNG: Transparency preserved, minimal quality loss
- WebP to GIF: Limited color palette may affect quality

### **File Size Impact:**
- Recovered files will be larger than WebP versions
- JPEG files: ~20-30% larger than WebP
- PNG files: ~40-50% larger than WebP

### **Performance Impact:**
- Page load times may increase due to larger file sizes
- Consider re-enabling WebP with "Backup Originals" enabled

---

## 🎉 **Success Verification**

After recovery, verify everything works:

1. **Check browser console** - No 404 errors
2. **Test image URLs** - All images load correctly
3. **Verify file system** - Original files exist
4. **Check WordPress admin** - Media library shows correct formats
5. **Test frontend** - All images display properly

---

## 🔄 **Prevention for Future**

To prevent this issue in the future:

1. ✅ **Always enable "Backup Originals"**
2. ✅ **Test on staging site first**
3. ✅ **Monitor console for errors**
4. ✅ **Regular database backups**
5. ✅ **Use the new smart WebP approach** (keeps originals as fallback)

---

## 📞 **Support**

If you encounter issues during recovery:

1. Check server error logs
2. Verify GD library has WebP support
3. Ensure write permissions on uploads directory
4. Test with a small batch first
5. Contact support with specific error messages

**Remember: Always backup before running recovery scripts!** 🛡️
