<?php
/**
 * Temporary debug file for security features
 * 
 * Add this to wp-config.php to enable debug mode:
 * define('REDCO_SECURITY_DEBUG', true);
 * 
 * This will temporarily disable all security filtering to help troubleshoot issues.
 * Remove this line once issues are resolved.
 */

// Enable debug mode for security features
define('REDCO_SECURITY_DEBUG', true);

// Enable WordPress debug logging
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);

// Log all AJAX requests for debugging
add_action('wp_ajax_redco_save_setting', function() {
    error_log('AJAX Request: redco_save_setting');
    error_log('POST data: ' . print_r($_POST, true));
    error_log('User logged in: ' . (is_user_logged_in() ? 'Yes' : 'No'));
    error_log('User can manage options: ' . (current_user_can('manage_options') ? 'Yes' : 'No'));
});

// Log security manager initialization
add_action('init', function() {
    if (class_exists('Redco_Security_Manager')) {
        error_log('Redco Security Manager initialized');
        error_log('Security config: ' . print_r(get_option('redco_optimizer_security_config', array()), true));
    }
});
