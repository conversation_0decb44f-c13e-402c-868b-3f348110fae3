<?php
/**
 * WordPress Core Tweaks Module Settings
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get module instance
$wordpress_core_tweaks = new Redco_WordPress_Core_Tweaks();
$stats = $wordpress_core_tweaks->get_stats();
$is_enabled = redco_is_module_enabled('wordpress-core-tweaks');

// Auto-enable module if not enabled (ensure it's always available)
if (!$is_enabled) {
    $enabled_modules = redco_get_option('modules_enabled', array());
    if (!is_array($enabled_modules)) {
        $enabled_modules = array();
    }
    if (!in_array('wordpress-core-tweaks', $enabled_modules)) {
        $enabled_modules[] = 'wordpress-core-tweaks';
        redco_update_option('modules_enabled', $enabled_modules);
        $is_enabled = true; // Update the local variable
    }
}

// Get current settings
$current_settings = array(
    // Emoji Stripper settings
    'emoji_remove_frontend' => redco_get_module_option('wordpress-core-tweaks', 'emoji_remove_frontend', true),
    'emoji_remove_admin' => redco_get_module_option('wordpress-core-tweaks', 'emoji_remove_admin', false),
    'emoji_remove_feeds' => redco_get_module_option('wordpress-core-tweaks', 'emoji_remove_feeds', true),
    'emoji_remove_emails' => redco_get_module_option('wordpress-core-tweaks', 'emoji_remove_emails', true),

    // Version Remover settings
    'remove_css_versions' => redco_get_module_option('wordpress-core-tweaks', 'remove_css_versions', true),
    'remove_js_versions' => redco_get_module_option('wordpress-core-tweaks', 'remove_js_versions', true),
    'remove_theme_versions' => redco_get_module_option('wordpress-core-tweaks', 'remove_theme_versions', true),
    'remove_plugin_versions' => redco_get_module_option('wordpress-core-tweaks', 'remove_plugin_versions', true),
    'remove_wp_version' => redco_get_module_option('wordpress-core-tweaks', 'remove_wp_version', true),
    'exclude_handles' => redco_get_module_option('wordpress-core-tweaks', 'exclude_handles', array()),

    // Query String Remover settings
    'remove_css_query_strings' => redco_get_module_option('wordpress-core-tweaks', 'remove_css_query_strings', true),
    'remove_js_query_strings' => redco_get_module_option('wordpress-core-tweaks', 'remove_js_query_strings', true),
    'query_string_exclude_handles' => redco_get_module_option('wordpress-core-tweaks', 'query_string_exclude_handles', array()),
    'remove_all_query_params' => redco_get_module_option('wordpress-core-tweaks', 'remove_all_query_params', false),

    // Autosave Reducer settings
    'autosave_interval' => redco_get_module_option('wordpress-core-tweaks', 'autosave_interval', 300),
    'disable_autosave' => redco_get_module_option('wordpress-core-tweaks', 'disable_autosave', false),
    'autosave_post_types' => redco_get_module_option('wordpress-core-tweaks', 'autosave_post_types', array('post', 'page')),
    'limit_revisions' => redco_get_module_option('wordpress-core-tweaks', 'limit_revisions', true),
    'max_revisions' => redco_get_module_option('wordpress-core-tweaks', 'max_revisions', 5)
);

// Ensure array settings are actually arrays to prevent errors
$current_settings['exclude_handles'] = is_array($current_settings['exclude_handles']) ? $current_settings['exclude_handles'] : array();
$current_settings['query_string_exclude_handles'] = is_array($current_settings['query_string_exclude_handles']) ? $current_settings['query_string_exclude_handles'] : array();
$current_settings['autosave_post_types'] = is_array($current_settings['autosave_post_types']) ? $current_settings['autosave_post_types'] : array('post', 'page');

// Get available post types
$post_types = get_post_types(array('public' => true), 'objects');

// Debug: Add error reporting to catch any issues
if (defined('WP_DEBUG') && WP_DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}

// Debug: Check if all required variables are set
$debug_vars = array(
    'is_enabled' => $is_enabled,
    'current_settings_count' => count($current_settings),
    'post_types_count' => count($post_types),
    'exclude_handles_type' => gettype($current_settings['exclude_handles']),
    'query_exclude_handles_type' => gettype($current_settings['query_string_exclude_handles']),
    'autosave_post_types_type' => gettype($current_settings['autosave_post_types'])
);

// Debug code removed for production
?>

<!-- Enqueue standardized module CSS -->
<link rel="stylesheet" href="<?php echo REDCO_OPTIMIZER_PLUGIN_URL; ?>assets/css/module-layout-standard.css">

<div class="redco-module-tab" data-module="wordpress-core-tweaks">
    <!-- Enhanced Professional Header Section -->
    <div class="module-header-section">
        <!-- Breadcrumb Navigation -->
        <div class="header-breadcrumb">
            <div class="breadcrumb-nav">
                <a href="<?php echo admin_url('admin.php?page=redco-optimizer'); ?>">
                    <span class="dashicons dashicons-performance"></span>
                    <?php _e('Redco Optimizer', 'redco-optimizer'); ?>
                </a>
                <span class="breadcrumb-separator">›</span>
                <a href="<?php echo admin_url('admin.php?page=redco-optimizer-modules'); ?>">
                    <?php _e('Modules', 'redco-optimizer'); ?>
                </a>
                <span class="breadcrumb-separator">›</span>
                <span class="breadcrumb-current"><?php _e('WordPress Core Tweaks', 'redco-optimizer'); ?></span>
            </div>
        </div>

        <!-- Main Header Content -->
        <div class="header-content">
            <div class="header-main">
                <div class="header-icon">
                    <span class="dashicons dashicons-admin-tools"></span>
                </div>
                <div class="header-text">
                    <h1><?php _e('WordPress Core Tweaks', 'redco-optimizer'); ?></h1>
                    <p><?php _e('Optimize WordPress core functionality, remove unnecessary features, and enhance security for better performance', 'redco-optimizer'); ?></p>

                    <!-- Status Indicators -->
                    <div class="header-status">
                        <?php if ($is_enabled): ?>
                            <div class="status-badge enabled">
                                <span class="dashicons dashicons-yes-alt"></span>
                                <?php _e('Active', 'redco-optimizer'); ?>
                            </div>
                            <?php if (redco_get_module_option('wordpress-core-tweaks', 'disable_emojis', true)): ?>
                                <div class="status-badge performance">
                                    <span class="dashicons dashicons-smiley"></span>
                                    <?php _e('No Emojis', 'redco-optimizer'); ?>
                                </div>
                            <?php endif; ?>
                            <?php if (redco_get_module_option('wordpress-core-tweaks', 'remove_version_numbers', true)): ?>
                                <div class="status-badge performance">
                                    <span class="dashicons dashicons-shield"></span>
                                    <?php _e('Security', 'redco-optimizer'); ?>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="status-badge disabled">
                                <span class="dashicons dashicons-warning"></span>
                                <?php _e('Inactive', 'redco-optimizer'); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Header Actions -->
            <div class="header-actions">
                <div class="header-action-group">
                    <!-- Quick Actions -->
                    <div class="header-quick-actions">
                        <?php if ($is_enabled): ?>
                            <button type="button" class="header-action-btn" id="apply-recommended-tweaks">
                                <span class="dashicons dashicons-yes"></span>
                                <?php _e('Apply Recommended', 'redco-optimizer'); ?>
                            </button>
                            <button type="button" class="header-action-btn" id="reset-all-tweaks">
                                <span class="dashicons dashicons-undo"></span>
                                <?php _e('Reset All', 'redco-optimizer'); ?>
                            </button>
                            <a href="<?php echo admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix'); ?>" class="header-action-btn">
                                <span class="dashicons dashicons-admin-tools"></span>
                                <?php _e('Diagnose', 'redco-optimizer'); ?>
                            </a>
                        <?php endif; ?>
                        <a href="<?php echo admin_url('admin.php?page=redco-optimizer-modules'); ?>" class="header-action-btn">
                            <span class="dashicons dashicons-admin-generic"></span>
                            <?php _e('All Modules', 'redco-optimizer'); ?>
                        </a>
                    </div>


                </div>

                <!-- Performance Metrics -->
                <?php if ($is_enabled): ?>
                    <div class="header-metrics">
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php
                                $active_tweaks = 0;
                                if (redco_get_module_option('wordpress-core-tweaks', 'disable_emojis', true)) $active_tweaks++;
                                if (redco_get_module_option('wordpress-core-tweaks', 'remove_version_numbers', true)) $active_tweaks++;
                                if (redco_get_module_option('wordpress-core-tweaks', 'disable_xmlrpc', true)) $active_tweaks++;
                                if (redco_get_module_option('wordpress-core-tweaks', 'limit_post_revisions', true)) $active_tweaks++;
                                echo $active_tweaks;
                                ?>
                            </div>
                            <div class="header-metric-label"><?php _e('Tweaks', 'redco-optimizer'); ?></div>
                        </div>
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo redco_get_module_option('wordpress-core-tweaks', 'disable_emojis', true) ? '✓' : '✗'; ?>
                            </div>
                            <div class="header-metric-label"><?php _e('Emojis', 'redco-optimizer'); ?></div>
                        </div>
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo redco_get_module_option('wordpress-core-tweaks', 'remove_version_numbers', true) ? '✓' : '✗'; ?>
                            </div>
                            <div class="header-metric-label"><?php _e('Security', 'redco-optimizer'); ?></div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <?php if ($is_enabled): ?>
    <!-- Module Content -->
    <div class="redco-module-content">
        <div class="module-layout">
            <div class="redco-content-main">
                <form class="redco-module-form" data-module="wordpress-core-tweaks">

                    <!-- Emoji Removal Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-smiley"></span>
                                <?php _e('WordPress Emoji Optimization', 'redco-optimizer'); ?>
                            </h3>
                            <div class="card-header-actions">
                                <button type="button" class="button button-small" id="enable-all-emoji-removal">
                                    <?php _e('Remove All Emojis', 'redco-optimizer'); ?>
                                </button>
                                <button type="button" class="button button-small" id="reset-emoji-settings">
                                    <?php _e('Reset to Defaults', 'redco-optimizer'); ?>
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="settings-intro">
                                <p class="description">
                                    <?php _e('WordPress includes emoji support that loads additional scripts and styles on every page. Removing these can reduce page size by 15-20KB and improve loading speed.', 'redco-optimizer'); ?>
                                </p>
                                <div class="performance-indicator">
                                    <span class="indicator-label"><?php _e('Performance Savings:', 'redco-optimizer'); ?></span>
                                    <span class="impact-level high" id="emoji-impact"><?php _e('15-20KB per page', 'redco-optimizer'); ?></span>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h4 class="section-title">
                                    <span class="dashicons dashicons-admin-settings"></span>
                                    <?php _e('Emoji Removal Options', 'redco-optimizer'); ?>
                                </h4>

                                <div class="emoji-options">
                                    <div class="emoji-option recommended">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="settings[emoji_remove_frontend]" value="1" <?php checked($current_settings['emoji_remove_frontend']); ?> class="emoji-checkbox">
                                            <span class="option-icon">🌐</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('Frontend Pages', 'redco-optimizer'); ?></strong>
                                                    <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Removes emoji scripts and styles from all public-facing pages. This provides the biggest performance benefit for your visitors.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Size Reduction:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high">15-20KB</span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('HTTP Requests:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high">-2</span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="emoji-option advanced">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="settings[emoji_remove_admin]" value="1" <?php checked($current_settings['emoji_remove_admin']); ?> class="emoji-checkbox">
                                            <span class="option-icon">⚙️</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('Admin Area', 'redco-optimizer'); ?></strong>
                                                    <span class="advanced-badge"><?php _e('Advanced', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Removes emoji support from WordPress admin area. Only enable if you don\'t use emojis in posts, comments, or admin interface.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Admin Performance:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value medium"><?php _e('Improved', 'redco-optimizer'); ?></span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Compatibility:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value medium"><?php _e('Good', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="emoji-option recommended">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="settings[emoji_remove_feeds]" value="1" <?php checked($current_settings['emoji_remove_feeds']); ?> class="emoji-checkbox">
                                            <span class="option-icon">📡</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('RSS Feeds', 'redco-optimizer'); ?></strong>
                                                    <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Removes emoji processing from RSS feeds. Improves feed performance and compatibility with feed readers.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Feed Size:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Reduced', 'redco-optimizer'); ?></span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Compatibility:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Improved', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="emoji-option recommended">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="settings[emoji_remove_emails]" value="1" <?php checked($current_settings['emoji_remove_emails']); ?> class="emoji-checkbox">
                                            <span class="option-icon">📧</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('Email Processing', 'redco-optimizer'); ?></strong>
                                                    <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Removes emoji processing from WordPress emails. Improves email delivery speed and reduces server processing time.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Email Performance:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Faster', 'redco-optimizer'); ?></span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Server Load:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value medium"><?php _e('Reduced', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>
                                </div>

                                <div class="emoji-summary">
                                    <div class="summary-stats">
                                        <span class="enabled-count">0 <?php _e('optimizations enabled', 'redco-optimizer'); ?></span>
                                        <span class="estimated-savings"><?php _e('Estimated savings: 0KB', 'redco-optimizer'); ?></span>
                                    </div>
                                    <div class="emoji-notice">
                                        <span class="dashicons dashicons-info"></span>
                                        <?php _e('Note: Removing emoji support will not affect existing emojis in your content, but new emojis may not display correctly.', 'redco-optimizer'); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Version Removal Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-admin-links"></span>
                                <?php _e('Version String & Security Optimization', 'redco-optimizer'); ?>
                            </h3>
                            <div class="card-header-actions">
                                <button type="button" class="button button-small" id="enable-all-version-removal">
                                    <?php _e('Enable All Security', 'redco-optimizer'); ?>
                                </button>
                                <button type="button" class="button button-small" id="reset-version-settings">
                                    <?php _e('Reset Versions', 'redco-optimizer'); ?>
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="settings-intro">
                                <p class="description">
                                    <?php _e('Remove version query strings from assets and hide WordPress version information to improve caching effectiveness and enhance security by reducing information disclosure.', 'redco-optimizer'); ?>
                                </p>
                                <div class="performance-indicator">
                                    <span class="indicator-label"><?php _e('Security & Caching Benefits:', 'redco-optimizer'); ?></span>
                                    <span class="impact-level high" id="version-impact"><?php _e('High Security', 'redco-optimizer'); ?></span>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h4 class="section-title">
                                    <span class="dashicons dashicons-shield"></span>
                                    <?php _e('Asset Version Removal', 'redco-optimizer'); ?>
                                </h4>

                                <div class="version-options">
                                    <div class="version-option recommended">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="settings[remove_css_versions]" value="1" <?php checked($current_settings['remove_css_versions']); ?> class="version-checkbox">
                                            <span class="option-icon">🎨</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('CSS Files', 'redco-optimizer'); ?></strong>
                                                    <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Removes version parameters from CSS files (e.g., style.css?ver=1.0). Improves browser caching and CDN effectiveness.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Caching:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Improved', 'redco-optimizer'); ?></span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('CDN Friendly:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Yes', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="version-option recommended">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="settings[remove_js_versions]" value="1" <?php checked($current_settings['remove_js_versions']); ?> class="version-checkbox">
                                            <span class="option-icon">⚡</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('JavaScript Files', 'redco-optimizer'); ?></strong>
                                                    <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Removes version parameters from JavaScript files (e.g., script.js?ver=1.0). Enhances caching and reduces cache-busting issues.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Caching:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Improved', 'redco-optimizer'); ?></span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Performance:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Better', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="version-option advanced">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="settings[remove_theme_versions]" value="1" <?php checked($current_settings['remove_theme_versions']); ?> class="version-checkbox">
                                            <span class="option-icon">🎭</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('Theme Files', 'redco-optimizer'); ?></strong>
                                                    <span class="advanced-badge"><?php _e('Advanced', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Removes version strings specifically from theme assets. Useful for custom themes with frequent updates.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Theme Caching:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value medium"><?php _e('Enhanced', 'redco-optimizer'); ?></span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Compatibility:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Good', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="version-option advanced">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="settings[remove_plugin_versions]" value="1" <?php checked($current_settings['remove_plugin_versions']); ?> class="version-checkbox">
                                            <span class="option-icon">🔌</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('Plugin Files', 'redco-optimizer'); ?></strong>
                                                    <span class="advanced-badge"><?php _e('Advanced', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Removes version strings from plugin assets. May affect some plugins that rely on version parameters for functionality.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Plugin Caching:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value medium"><?php _e('Enhanced', 'redco-optimizer'); ?></span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Compatibility:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value medium"><?php _e('Good', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="version-option security">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="settings[remove_wp_version]" value="1" <?php checked($current_settings['remove_wp_version']); ?> class="version-checkbox">
                                            <span class="option-icon">🛡️</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('WordPress Version Generator', 'redco-optimizer'); ?></strong>
                                                    <span class="security-badge"><?php _e('Security', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Removes WordPress version from HTML meta generator tag. Improves security by hiding version information from potential attackers.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Security:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Enhanced', 'redco-optimizer'); ?></span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Information Hiding:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Yes', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>
                                </div>

                                <div class="exclusion-settings">
                                    <h5 class="exclusion-title">
                                        <span class="dashicons dashicons-admin-generic"></span>
                                        <?php _e('Advanced Exclusion Settings', 'redco-optimizer'); ?>
                                    </h5>
                                    <div class="exclusion-content">
                                        <label for="exclude_handles" class="exclusion-label">
                                            <strong><?php _e('Exclude Specific Handles', 'redco-optimizer'); ?></strong>
                                            <span class="exclusion-description"><?php _e('Script/style handles to exclude from version removal', 'redco-optimizer'); ?></span>
                                        </label>
                                        <textarea name="settings[exclude_handles]" id="exclude_handles" rows="4" class="exclusion-textarea" placeholder="<?php _e('Enter handles one per line, e.g.:\njquery\nbootstrap\ncustom-script', 'redco-optimizer'); ?>"><?php echo esc_textarea(implode("\n", $current_settings['exclude_handles'])); ?></textarea>
                                        <div class="exclusion-help">
                                            <span class="dashicons dashicons-info"></span>
                                            <?php _e('Use this to exclude specific scripts or styles that may break when version parameters are removed.', 'redco-optimizer'); ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="version-summary">
                                    <div class="summary-stats">
                                        <span class="enabled-count">0 <?php _e('optimizations enabled', 'redco-optimizer'); ?></span>
                                        <span class="security-level"><?php _e('Security level: Basic', 'redco-optimizer'); ?></span>
                                    </div>
                                    <div class="version-notice">
                                        <span class="dashicons dashicons-info"></span>
                                        <?php _e('Version removal improves caching but may affect some plugins. Test thoroughly after enabling.', 'redco-optimizer'); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- DEBUG: Query String Section Start -->
                    <?php if (isset($_GET['debug_core_tweaks'])) echo '<!-- DEBUG: Query String Section Starting -->'; ?>

                    <!-- Query String Remover Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-admin-links"></span>
                                <?php _e('Advanced Query String Optimization', 'redco-optimizer'); ?>
                            </h3>
                            <div class="card-header-actions">
                                <button type="button" class="button button-small" id="enable-aggressive-query">
                                    <?php _e('Enable Aggressive Mode', 'redco-optimizer'); ?>
                                </button>
                                <button type="button" class="button button-small" id="reset-query-settings">
                                    <?php _e('Reset Query', 'redco-optimizer'); ?>
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="settings-intro">
                                <p class="description">
                                    <?php _e('Advanced query string removal goes beyond version strings to eliminate ALL query parameters from assets. This provides maximum caching effectiveness but requires careful testing to ensure compatibility.', 'redco-optimizer'); ?>
                                </p>
                                <div class="performance-indicator">
                                    <span class="indicator-label"><?php _e('Caching Effectiveness:', 'redco-optimizer'); ?></span>
                                    <span class="impact-level high" id="query-impact"><?php _e('Maximum Caching', 'redco-optimizer'); ?></span>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h4 class="section-title">
                                    <span class="dashicons dashicons-admin-settings"></span>
                                    <?php _e('Query Parameter Removal Strategy', 'redco-optimizer'); ?>
                                </h4>

                                <div class="query-options">
                                    <div class="query-option recommended">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="settings[remove_css_query_strings]" value="1" <?php checked($current_settings['remove_css_query_strings']); ?> class="query-checkbox">
                                            <span class="option-icon">🎨</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('CSS Query Parameters', 'redco-optimizer'); ?></strong>
                                                    <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Removes ALL query parameters from CSS files (e.g., style.css?ver=1.0&param=value becomes style.css). This maximizes CSS caching effectiveness across all CDNs and browsers.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('CSS Caching:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Maximum', 'redco-optimizer'); ?></span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('CDN Friendly:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Yes', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="query-option recommended">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="settings[remove_js_query_strings]" value="1" <?php checked($current_settings['remove_js_query_strings']); ?> class="query-checkbox">
                                            <span class="option-icon">⚡</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('JavaScript Query Parameters', 'redco-optimizer'); ?></strong>
                                                    <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Removes ALL query parameters from JavaScript files (e.g., script.js?ver=1.0&param=value becomes script.js). Enhances JavaScript caching and reduces cache-busting issues.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('JS Caching:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Maximum', 'redco-optimizer'); ?></span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Performance:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Enhanced', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="query-option advanced">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="settings[remove_all_query_params]" value="1" <?php checked($current_settings['remove_all_query_params']); ?> class="query-checkbox">
                                            <span class="option-icon">🚀</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('Aggressive Mode - All Parameters', 'redco-optimizer'); ?></strong>
                                                    <span class="advanced-badge"><?php _e('Advanced', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Removes ALL query parameters from ALL assets, including custom parameters used by themes and plugins. This provides maximum caching but may break functionality that relies on query parameters.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Caching Level:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Ultimate', 'redco-optimizer'); ?></span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Compatibility Risk:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value medium"><?php _e('Medium', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                                <div class="option-warning">
                                                    <span class="dashicons dashicons-warning"></span>
                                                    <?php _e('Warning: Test thoroughly after enabling. Some plugins may rely on query parameters for functionality.', 'redco-optimizer'); ?>
                                                </div>
                                            </span>
                                        </label>
                                    </div>
                                </div>

                                <div class="query-exclusion-settings">
                                    <h5 class="query-exclusion-title">
                                        <span class="dashicons dashicons-admin-generic"></span>
                                        <?php _e('Advanced Exclusion Settings', 'redco-optimizer'); ?>
                                    </h5>
                                    <div class="query-exclusion-content">
                                        <label for="query_string_exclude_handles" class="query-exclusion-label">
                                            <strong><?php _e('Exclude Specific Handles', 'redco-optimizer'); ?></strong>
                                            <span class="query-exclusion-description"><?php _e('Script/style handles to exclude from query string removal', 'redco-optimizer'); ?></span>
                                        </label>
                                        <textarea name="settings[query_string_exclude_handles]" id="query_string_exclude_handles" rows="4" class="query-exclusion-textarea" placeholder="<?php _e('Enter handles one per line, e.g.:\njquery\nbootstrap\ncustom-script\ntheme-style', 'redco-optimizer'); ?>"><?php echo esc_textarea(implode("\n", $current_settings['query_string_exclude_handles'])); ?></textarea>
                                        <div class="query-exclusion-help">
                                            <span class="dashicons dashicons-info"></span>
                                            <?php _e('Use this to exclude specific scripts or styles that may break when ALL query parameters are removed. Essential for compatibility.', 'redco-optimizer'); ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="query-summary">
                                    <div class="summary-stats">
                                        <span class="enabled-count">0 <?php _e('optimizations enabled', 'redco-optimizer'); ?></span>
                                        <span class="caching-level"><?php _e('Caching level: Basic', 'redco-optimizer'); ?></span>
                                    </div>
                                    <div class="query-notice">
                                        <span class="dashicons dashicons-info"></span>
                                        <?php _e('Query string removal is more aggressive than version removal. Test thoroughly, especially with aggressive mode enabled.', 'redco-optimizer'); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- DEBUG: Autosave Section Start -->
                    <?php if (isset($_GET['debug_core_tweaks'])) echo '<!-- DEBUG: Autosave Section Starting -->'; ?>

                    <!-- Autosave Optimization Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-backup"></span>
                                <?php _e('Autosave & Revision Optimization', 'redco-optimizer'); ?>
                            </h3>
                            <div class="card-header-actions">
                                <button type="button" class="button button-small" id="optimize-autosave">
                                    <?php _e('Optimize Autosave', 'redco-optimizer'); ?>
                                </button>
                                <button type="button" class="button button-small" id="reset-autosave-settings">
                                    <?php _e('Reset Autosave', 'redco-optimizer'); ?>
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="settings-intro">
                                <p class="description">
                                    <?php _e('Autosave optimization reduces server load and database bloat by controlling automatic saving frequency and limiting post revisions. This improves admin performance and reduces database size.', 'redco-optimizer'); ?>
                                </p>
                                <div class="performance-indicator">
                                    <span class="indicator-label"><?php _e('Server Performance Impact:', 'redco-optimizer'); ?></span>
                                    <span class="impact-level high" id="autosave-impact"><?php _e('High Server Optimization', 'redco-optimizer'); ?></span>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h4 class="section-title">
                                    <span class="dashicons dashicons-admin-settings"></span>
                                    <?php _e('Autosave Control Strategy', 'redco-optimizer'); ?>
                                </h4>

                                <div class="autosave-options">
                                    <div class="autosave-option advanced">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="settings[disable_autosave]" value="1" <?php checked($current_settings['disable_autosave']); ?> class="autosave-checkbox">
                                            <span class="option-icon">🚫</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('Disable Autosave Completely', 'redco-optimizer'); ?></strong>
                                                    <span class="advanced-badge"><?php _e('Advanced', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Completely disables WordPress autosave functionality. This eliminates all automatic AJAX requests but requires manual saving. Best for experienced users who save frequently.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Server Load:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Eliminated', 'redco-optimizer'); ?></span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('AJAX Requests:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('None', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                                <div class="option-warning">
                                                    <span class="dashicons dashicons-warning"></span>
                                                    <?php _e('Warning: You must remember to save manually. No automatic backup will occur.', 'redco-optimizer'); ?>
                                                </div>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="autosave-option recommended">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="settings[limit_revisions]" value="1" <?php checked($current_settings['limit_revisions']); ?> class="autosave-checkbox">
                                            <span class="option-icon">📚</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('Limit Post Revisions', 'redco-optimizer'); ?></strong>
                                                    <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Limits the number of revisions stored for each post. WordPress stores unlimited revisions by default, which can bloat your database significantly over time.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Database Size:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Reduced', 'redco-optimizer'); ?></span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Query Performance:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Improved', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>
                                </div>

                                <div class="autosave-settings">
                                    <h5 class="autosave-settings-title">
                                        <span class="dashicons dashicons-admin-generic"></span>
                                        <?php _e('Advanced Autosave Configuration', 'redco-optimizer'); ?>
                                    </h5>

                                    <div class="autosave-settings-content">
                                        <div class="setting-group">
                                            <label for="autosave_interval" class="setting-label">
                                                <strong><?php _e('Autosave Interval', 'redco-optimizer'); ?></strong>
                                                <span class="setting-description"><?php _e('How often WordPress should autosave (in seconds)', 'redco-optimizer'); ?></span>
                                            </label>
                                            <div class="setting-control">
                                                <input type="number" name="settings[autosave_interval]" id="autosave_interval" value="<?php echo esc_attr($current_settings['autosave_interval']); ?>" min="60" max="3600" class="setting-input" />
                                                <span class="setting-unit"><?php _e('seconds', 'redco-optimizer'); ?></span>
                                            </div>
                                            <div class="setting-info">
                                                <span class="current-setting">
                                                    <?php printf(__('Current: %d seconds (%s minutes)', 'redco-optimizer'),
                                                        $current_settings['autosave_interval'],
                                                        round($current_settings['autosave_interval'] / 60, 1)
                                                    ); ?>
                                                </span>
                                                <span class="default-setting"><?php _e('WordPress default: 60 seconds', 'redco-optimizer'); ?></span>
                                            </div>
                                        </div>

                                        <div class="setting-group">
                                            <label for="max_revisions" class="setting-label">
                                                <strong><?php _e('Maximum Revisions', 'redco-optimizer'); ?></strong>
                                                <span class="setting-description"><?php _e('Maximum number of revisions to keep per post', 'redco-optimizer'); ?></span>
                                            </label>
                                            <div class="setting-control">
                                                <input type="number" name="settings[max_revisions]" id="max_revisions" value="<?php echo esc_attr($current_settings['max_revisions']); ?>" min="1" max="50" class="setting-input" />
                                                <span class="setting-unit"><?php _e('revisions', 'redco-optimizer'); ?></span>
                                            </div>
                                            <div class="setting-info">
                                                <span class="recommended-setting"><?php _e('Recommended: 3-5 revisions', 'redco-optimizer'); ?></span>
                                                <span class="default-setting"><?php _e('WordPress default: Unlimited', 'redco-optimizer'); ?></span>
                                            </div>
                                        </div>

                                        <div class="setting-group">
                                            <label class="setting-label">
                                                <strong><?php _e('Affected Post Types', 'redco-optimizer'); ?></strong>
                                                <span class="setting-description"><?php _e('Select which post types should be affected by autosave optimization', 'redco-optimizer'); ?></span>
                                            </label>
                                            <div class="post-types-grid">
                                                <?php foreach ($post_types as $post_type): ?>
                                                    <label class="post-type-item">
                                                        <input type="checkbox" name="settings[autosave_post_types][]" value="<?php echo esc_attr($post_type->name); ?>"
                                                            <?php checked(in_array($post_type->name, $current_settings['autosave_post_types'])); ?> class="autosave-checkbox" />
                                                        <span class="post-type-name"><?php echo esc_html($post_type->labels->name); ?></span>
                                                        <span class="post-type-slug">(<?php echo esc_html($post_type->name); ?>)</span>
                                                    </label>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="autosave-summary">
                                    <div class="summary-stats">
                                        <span class="enabled-count">0 <?php _e('optimizations enabled', 'redco-optimizer'); ?></span>
                                        <span class="server-impact"><?php _e('Server impact: Minimal', 'redco-optimizer'); ?></span>
                                    </div>
                                    <div class="autosave-notice">
                                        <span class="dashicons dashicons-info"></span>
                                        <?php _e('Autosave optimization reduces server load and database size. Consider your editing habits when configuring these settings.', 'redco-optimizer'); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </form>
            </div>

            <!-- Sidebar -->
            <div class="redco-content-sidebar">
                <!-- Module Statistics -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-chart-bar"></span>
                            <?php _e('Module Statistics', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="stats-grid">
                            <div class="stat-item stat-css-versions">
                                <span class="stat-value"><?php echo $stats['css_versions_removed'] ? __('Yes', 'redco-optimizer') : __('No', 'redco-optimizer'); ?></span>
                                <span class="stat-label"><?php _e('CSS Versions', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-js-versions">
                                <span class="stat-value"><?php echo $stats['js_versions_removed'] ? __('Yes', 'redco-optimizer') : __('No', 'redco-optimizer'); ?></span>
                                <span class="stat-label"><?php _e('JS Versions', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-excluded">
                                <span class="stat-value"><?php echo $stats['excluded_handles']; ?></span>
                                <span class="stat-label"><?php _e('Excluded', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-autosave">
                                <span class="stat-value"><?php echo isset($stats['current_autosave_interval']) ? round($stats['current_autosave_interval'] / 60, 1) : '0'; ?>m</span>
                                <span class="stat-label"><?php _e('Autosave', 'redco-optimizer'); ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Performance Impact -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-performance"></span>
                            <?php _e('Performance Impact', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <p><strong><?php _e('Estimated Improvements:', 'redco-optimizer'); ?></strong></p>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li><?php _e('15-20KB reduction from emoji removal', 'redco-optimizer'); ?></li>
                            <li><?php _e('Better caching from version removal', 'redco-optimizer'); ?></li>
                            <li><?php _e('Reduced server load from autosave optimization', 'redco-optimizer'); ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php else: ?>
    <!-- Module Disabled State -->
    <div class="redco-module-disabled">
        <div class="disabled-message">
            <div class="disabled-icon">
                <span class="dashicons dashicons-admin-tools"></span>
            </div>
            <div class="disabled-content">
                <h3><?php _e('WordPress Core Tweaks Module Disabled', 'redco-optimizer'); ?></h3>
                <p><?php _e('This module is currently disabled. Enable it to access WordPress core optimization features including emoji removal, version string optimization, query string removal, and autosave optimization.', 'redco-optimizer'); ?></p>

                <div class="disabled-features">
                    <h4><?php _e('Available Features:', 'redco-optimizer'); ?></h4>
                    <ul class="feature-list">
                        <li><span class="dashicons dashicons-smiley"></span> <?php _e('Emoji Removal - Remove WordPress emoji scripts and styles', 'redco-optimizer'); ?></li>
                        <li><span class="dashicons dashicons-admin-links"></span> <?php _e('Version String Optimization - Remove version parameters from assets', 'redco-optimizer'); ?></li>
                        <li><span class="dashicons dashicons-admin-settings"></span> <?php _e('Query String Optimization - Advanced query parameter removal', 'redco-optimizer'); ?></li>
                        <li><span class="dashicons dashicons-edit"></span> <?php _e('Autosave Optimization - Reduce autosave frequency and limit revisions', 'redco-optimizer'); ?></li>
                    </ul>
                </div>

                <div class="disabled-actions">
                    <button type="button" class="button button-primary" id="enable-wordpress-core-tweaks">
                        <span class="dashicons dashicons-yes"></span>
                        <?php _e('Enable WordPress Core Tweaks', 'redco-optimizer'); ?>
                    </button>
                    <a href="<?php echo admin_url('admin.php?page=redco-optimizer-modules'); ?>" class="button button-secondary">
                        <span class="dashicons dashicons-admin-generic"></span>
                        <?php _e('Manage All Modules', 'redco-optimizer'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <style>
    .redco-module-disabled {
        padding: 40px 20px;
        text-align: center;
        background: #f9f9f9;
        border: 1px solid #ddd;
        border-radius: 8px;
        margin: 20px 0;
    }

    .disabled-message {
        max-width: 600px;
        margin: 0 auto;
    }

    .disabled-icon .dashicons {
        font-size: 48px;
        color: #999;
        margin-bottom: 20px;
    }

    .disabled-content h3 {
        font-size: 24px;
        margin-bottom: 15px;
        color: #333;
    }

    .disabled-content p {
        font-size: 16px;
        color: #666;
        margin-bottom: 30px;
        line-height: 1.6;
    }

    .disabled-features {
        text-align: left;
        margin: 30px 0;
        padding: 20px;
        background: white;
        border-radius: 6px;
        border: 1px solid #e0e0e0;
    }

    .disabled-features h4 {
        margin-bottom: 15px;
        color: #333;
        font-size: 16px;
    }

    .feature-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .feature-list li {
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        align-items: center;
    }

    .feature-list li:last-child {
        border-bottom: none;
    }

    .feature-list .dashicons {
        color: #4CAF50;
        margin-right: 10px;
        font-size: 16px;
    }

    .disabled-actions {
        margin-top: 30px;
    }

    .disabled-actions .button {
        margin: 0 10px;
        padding: 10px 20px;
        height: auto;
        font-size: 14px;
    }

    .disabled-actions .button-primary {
        background: #4CAF50;
        border-color: #4CAF50;
    }

    .disabled-actions .button-primary:hover {
        background: #45a049;
        border-color: #45a049;
    }
    </style>

    <script>
    jQuery(document).ready(function($) {
        $('#enable-wordpress-core-tweaks').on('click', function() {
            const button = $(this);
            const originalText = button.html();

            button.prop('disabled', true).html('<span class="dashicons dashicons-update spin"></span> <?php _e("Enabling...", "redco-optimizer"); ?>');

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_toggle_module',
                    module: 'wordpress-core-tweaks',
                    module_action: 'enable',
                    nonce: '<?php echo wp_create_nonce("redco_optimizer_nonce"); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        // Use global toast notification system
                        if (typeof showToast === 'function') {
                            showToast('<?php _e("WordPress Core Tweaks module enabled successfully! Reloading page...", "redco-optimizer"); ?>', 'success');
                        }

                        // Reload page after short delay
                        setTimeout(function() {
                            window.location.reload();
                        }, 1500);
                    } else {
                        button.prop('disabled', false).html(originalText);
                        if (typeof showToast === 'function') {
                            showToast('<?php _e("Failed to enable module. Please try again.", "redco-optimizer"); ?>', 'error');
                        }
                    }
                },
                error: function() {
                    button.prop('disabled', false).html(originalText);
                    if (typeof showToast === 'function') {
                        showToast('<?php _e("An error occurred. Please try again.", "redco-optimizer"); ?>', 'error');
                    }
                }
            });
        });
    });
    </script>
    <?php endif; ?>
</div>

<script>
jQuery(document).ready(function($) {
    // WordPress Core Tweaks Enhanced Functionality

    // Update emoji summary
    function updateEmojiSummary() {
        const emojiCheckboxes = $('.emoji-checkbox:checked');
        const enabledCount = emojiCheckboxes.length;

        $('.emoji-summary .enabled-count').text(enabledCount + ' <?php _e("optimizations enabled", "redco-optimizer"); ?>');

        // Calculate estimated savings
        let savings = 0;
        if ($('input[name="settings[emoji_remove_frontend]"]').is(':checked')) {
            savings += 18; // 15-20KB average
        }
        if ($('input[name="settings[emoji_remove_admin]"]').is(':checked')) {
            savings += 5;
        }
        if ($('input[name="settings[emoji_remove_feeds]"]').is(':checked')) {
            savings += 2;
        }
        if ($('input[name="settings[emoji_remove_emails]"]').is(':checked')) {
            savings += 1;
        }

        $('.emoji-summary .estimated-savings').text('<?php _e("Estimated savings:", "redco-optimizer"); ?> ' + savings + 'KB');

        // Update impact indicator
        let impactText = savings > 15 ? '<?php _e("15-20KB per page", "redco-optimizer"); ?>' :
                        savings > 5 ? '<?php _e("5-15KB per page", "redco-optimizer"); ?>' :
                        '<?php _e("Minimal savings", "redco-optimizer"); ?>';
        $('#emoji-impact').text(impactText);
    }

    // Update version summary
    function updateVersionSummary() {
        const versionCheckboxes = $('.version-checkbox:checked');
        const enabledCount = versionCheckboxes.length;

        $('.version-summary .enabled-count').text(enabledCount + ' <?php _e("optimizations enabled", "redco-optimizer"); ?>');

        // Calculate security level
        let securityLevel = '<?php _e("Basic", "redco-optimizer"); ?>';
        if ($('input[name="settings[remove_wp_version]"]').is(':checked')) {
            securityLevel = enabledCount >= 3 ? '<?php _e("High Security", "redco-optimizer"); ?>' : '<?php _e("Medium Security", "redco-optimizer"); ?>';
        } else if (enabledCount >= 2) {
            securityLevel = '<?php _e("Medium Security", "redco-optimizer"); ?>';
        }

        $('.version-summary .security-level').text('<?php _e("Security level:", "redco-optimizer"); ?> ' + securityLevel);

        // Update impact indicator
        $('#version-impact').text(securityLevel);
    }

    // Update query string summary
    function updateQuerySummary() {
        const queryCheckboxes = $('.query-checkbox:checked');
        const enabledCount = queryCheckboxes.length;

        $('.query-summary .enabled-count').text(enabledCount + ' <?php _e("optimizations enabled", "redco-optimizer"); ?>');

        // Calculate caching level
        let cachingLevel = '<?php _e("Basic", "redco-optimizer"); ?>';
        if ($('input[name="settings[remove_all_query_params]"]').is(':checked')) {
            cachingLevel = '<?php _e("Ultimate Caching", "redco-optimizer"); ?>';
        } else if (enabledCount >= 2) {
            cachingLevel = '<?php _e("Maximum Caching", "redco-optimizer"); ?>';
        } else if (enabledCount >= 1) {
            cachingLevel = '<?php _e("Enhanced Caching", "redco-optimizer"); ?>';
        }

        $('.query-summary .caching-level').text('<?php _e("Caching level:", "redco-optimizer"); ?> ' + cachingLevel);

        // Update impact indicator
        $('#query-impact').text(cachingLevel);
    }

    // Update autosave summary
    function updateAutosaveSummary() {
        const autosaveCheckboxes = $('.autosave-checkbox:checked');
        const enabledCount = autosaveCheckboxes.length;

        $('.autosave-summary .enabled-count').text(enabledCount + ' <?php _e("optimizations enabled", "redco-optimizer"); ?>');

        // Calculate server impact
        let serverImpact = '<?php _e("Minimal", "redco-optimizer"); ?>';
        if ($('input[name="settings[disable_autosave]"]').is(':checked')) {
            serverImpact = '<?php _e("Maximum Server Optimization", "redco-optimizer"); ?>';
        } else if ($('input[name="settings[limit_revisions]"]').is(':checked')) {
            serverImpact = '<?php _e("High Server Optimization", "redco-optimizer"); ?>';
        }

        $('.autosave-summary .server-impact').text('<?php _e("Server impact:", "redco-optimizer"); ?> ' + serverImpact);

        // Update impact indicator
        $('#autosave-impact').text(serverImpact);
    }

    // Enable All Emoji Removal button
    $('#enable-all-emoji-removal').on('click', function(e) {
        e.preventDefault();

        const button = $(this);
        const allEmojiCheckboxes = $('.emoji-checkbox');
        const allChecked = allEmojiCheckboxes.length === allEmojiCheckboxes.filter(':checked').length;

        button.addClass('loading').text('<?php _e("Processing...", "redco-optimizer"); ?>');

        setTimeout(() => {
            if (allChecked) {
                // Disable all
                allEmojiCheckboxes.prop('checked', false);
                button.text('<?php _e("Remove All Emojis", "redco-optimizer"); ?>');
                button.removeClass('button-primary').addClass('button-secondary');
            } else {
                // Enable all
                allEmojiCheckboxes.prop('checked', true);
                button.text('<?php _e("Keep All Emojis", "redco-optimizer"); ?>');
                button.removeClass('button-secondary').addClass('button-primary');
            }

            updateEmojiSummary();
            button.removeClass('loading');

            // Visual feedback
            $('.emoji-option').addClass('selection-highlight');
            setTimeout(() => {
                $('.emoji-option').removeClass('selection-highlight');
            }, 300);

            // Use global toast notification system
            if (typeof showToast === 'function') {
                showToast('<?php _e("Emoji settings updated successfully!", "redco-optimizer"); ?>', 'success');
            }
        }, 500);
    });

    // Reset Emoji Settings button
    $('#reset-emoji-settings').on('click', function(e) {
        e.preventDefault();

        const button = $(this);
        button.addClass('loading').text('<?php _e("Resetting...", "redco-optimizer"); ?>');

        setTimeout(() => {
            // Reset to defaults (frontend, feeds, emails enabled; admin disabled)
            $('input[name="settings[emoji_remove_frontend]"]').prop('checked', true);
            $('input[name="settings[emoji_remove_admin]"]').prop('checked', false);
            $('input[name="settings[emoji_remove_feeds]"]').prop('checked', true);
            $('input[name="settings[emoji_remove_emails]"]').prop('checked', true);

            updateEmojiSummary();
            button.removeClass('loading').text('<?php _e("Reset to Defaults", "redco-optimizer"); ?>');

            // Use global toast notification system
            if (typeof showToast === 'function') {
                showToast('<?php _e("Emoji settings reset to recommended defaults", "redco-optimizer"); ?>', 'info');
            }
        }, 500);
    });

    // Enable All Version Removal button
    $('#enable-all-version-removal').on('click', function(e) {
        e.preventDefault();

        const button = $(this);
        const allVersionCheckboxes = $('.version-checkbox');
        const allChecked = allVersionCheckboxes.length === allVersionCheckboxes.filter(':checked').length;

        button.addClass('loading').text('<?php _e("Processing...", "redco-optimizer"); ?>');

        setTimeout(() => {
            if (allChecked) {
                // Disable all
                allVersionCheckboxes.prop('checked', false);
                button.text('<?php _e("Enable All Security", "redco-optimizer"); ?>');
                button.removeClass('button-primary').addClass('button-secondary');
            } else {
                // Enable all
                allVersionCheckboxes.prop('checked', true);
                button.text('<?php _e("Disable All Security", "redco-optimizer"); ?>');
                button.removeClass('button-secondary').addClass('button-primary');
            }

            updateVersionSummary();
            button.removeClass('loading');

            // Visual feedback
            $('.version-option').addClass('selection-highlight');
            setTimeout(() => {
                $('.version-option').removeClass('selection-highlight');
            }, 300);

            // Use global toast notification system
            if (typeof showToast === 'function') {
                showToast('<?php _e("Security settings updated successfully!", "redco-optimizer"); ?>', 'success');
            }
        }, 500);
    });

    // Reset Version Settings button
    $('#reset-version-settings').on('click', function(e) {
        e.preventDefault();

        const button = $(this);
        button.addClass('loading').text('<?php _e("Resetting...", "redco-optimizer"); ?>');

        setTimeout(() => {
            // Reset to defaults (CSS and JS enabled, others disabled)
            $('input[name="settings[remove_css_versions]"]').prop('checked', true);
            $('input[name="settings[remove_js_versions]"]').prop('checked', true);
            $('input[name="settings[remove_theme_versions]"]').prop('checked', false);
            $('input[name="settings[remove_plugin_versions]"]').prop('checked', false);
            $('input[name="settings[remove_wp_version]"]').prop('checked', true);

            updateVersionSummary();
            button.removeClass('loading').text('<?php _e("Reset Versions", "redco-optimizer"); ?>');

            // Use global toast notification system
            if (typeof showToast === 'function') {
                showToast('<?php _e("Version settings reset to recommended defaults", "redco-optimizer"); ?>', 'info');
            }
        }, 500);
    });

    // Enable Aggressive Query button
    $('#enable-aggressive-query').on('click', function(e) {
        e.preventDefault();

        const button = $(this);
        const aggressiveCheckbox = $('input[name="settings[remove_all_query_params]"]');
        const isChecked = aggressiveCheckbox.is(':checked');

        button.addClass('loading').text('<?php _e("Processing...", "redco-optimizer"); ?>');

        setTimeout(() => {
            if (isChecked) {
                // Disable aggressive mode
                aggressiveCheckbox.prop('checked', false);
                button.text('<?php _e("Enable Aggressive Mode", "redco-optimizer"); ?>');
            } else {
                // Enable aggressive mode and recommended options
                $('input[name="settings[remove_css_query_strings]"]').prop('checked', true);
                $('input[name="settings[remove_js_query_strings]"]').prop('checked', true);
                aggressiveCheckbox.prop('checked', true);
                button.text('<?php _e("Disable Aggressive Mode", "redco-optimizer"); ?>');
            }

            updateQuerySummary();
            button.removeClass('loading');

            // Use global toast notification system
            if (typeof showToast === 'function') {
                showToast('<?php _e("Query string settings updated successfully!", "redco-optimizer"); ?>', 'success');
            }
        }, 500);
    });

    // Reset Query Settings button
    $('#reset-query-settings').on('click', function(e) {
        e.preventDefault();

        const button = $(this);
        button.addClass('loading').text('<?php _e("Resetting...", "redco-optimizer"); ?>');

        setTimeout(() => {
            // Reset to defaults (CSS and JS enabled, aggressive disabled)
            $('input[name="settings[remove_css_query_strings]"]').prop('checked', true);
            $('input[name="settings[remove_js_query_strings]"]').prop('checked', true);
            $('input[name="settings[remove_all_query_params]"]').prop('checked', false);

            updateQuerySummary();
            button.removeClass('loading').text('<?php _e("Reset Query", "redco-optimizer"); ?>');

            // Use global toast notification system
            if (typeof showToast === 'function') {
                showToast('<?php _e("Query string settings reset to recommended defaults", "redco-optimizer"); ?>', 'info');
            }
        }, 500);
    });

    // Optimize Autosave button
    $('#optimize-autosave').on('click', function(e) {
        e.preventDefault();

        const button = $(this);
        button.addClass('loading').text('<?php _e("Optimizing...", "redco-optimizer"); ?>');

        setTimeout(() => {
            // Enable recommended autosave optimizations
            $('input[name="settings[limit_revisions]"]').prop('checked', true);
            $('input[name="settings[max_revisions]"]').val('5');
            $('input[name="settings[autosave_interval]"]').val('300'); // 5 minutes

            updateAutosaveSummary();
            button.removeClass('loading').text('<?php _e("Optimize Autosave", "redco-optimizer"); ?>');

            // Use global toast notification system
            if (typeof showToast === 'function') {
                showToast('<?php _e("Autosave settings optimized for performance!", "redco-optimizer"); ?>', 'success');
            }
        }, 500);
    });

    // Reset Autosave Settings button
    $('#reset-autosave-settings').on('click', function(e) {
        e.preventDefault();

        const button = $(this);
        button.addClass('loading').text('<?php _e("Resetting...", "redco-optimizer"); ?>');

        setTimeout(() => {
            // Reset to WordPress defaults
            $('input[name="settings[disable_autosave]"]').prop('checked', false);
            $('input[name="settings[limit_revisions]"]').prop('checked', false);
            $('input[name="settings[max_revisions]"]').val('10');
            $('input[name="settings[autosave_interval]"]').val('60');

            updateAutosaveSummary();
            button.removeClass('loading').text('<?php _e("Reset Autosave", "redco-optimizer"); ?>');

            // Use global toast notification system
            if (typeof showToast === 'function') {
                showToast('<?php _e("Autosave settings reset to WordPress defaults", "redco-optimizer"); ?>', 'info');
            }
        }, 500);
    });

    // Individual checkbox changes
    $('.emoji-checkbox').on('change', function() {
        updateEmojiSummary();

        // Update button states
        const allEmojiCheckboxes = $('.emoji-checkbox');
        const allChecked = allEmojiCheckboxes.length === allEmojiCheckboxes.filter(':checked').length;

        if (allChecked) {
            $('#enable-all-emoji-removal').text('<?php _e("Keep All Emojis", "redco-optimizer"); ?>');
            $('#enable-all-emoji-removal').removeClass('button-secondary').addClass('button-primary');
        } else {
            $('#enable-all-emoji-removal').text('<?php _e("Remove All Emojis", "redco-optimizer"); ?>');
            $('#enable-all-emoji-removal').removeClass('button-primary').addClass('button-secondary');
        }
    });

    $('.version-checkbox').on('change', function() {
        updateVersionSummary();

        // Update button states
        const allVersionCheckboxes = $('.version-checkbox');
        const allChecked = allVersionCheckboxes.length === allVersionCheckboxes.filter(':checked').length;

        if (allChecked) {
            $('#enable-all-version-removal').text('<?php _e("Disable All Security", "redco-optimizer"); ?>');
            $('#enable-all-version-removal').removeClass('button-secondary').addClass('button-primary');
        } else {
            $('#enable-all-version-removal').text('<?php _e("Enable All Security", "redco-optimizer"); ?>');
            $('#enable-all-version-removal').removeClass('button-primary').addClass('button-secondary');
        }
    });

    $('.query-checkbox').on('change', updateQuerySummary);
    $('.autosave-checkbox').on('change', updateAutosaveSummary);

    // Update summaries when input values change
    $('#autosave_interval, #max_revisions').on('input', updateAutosaveSummary);

    // Page-specific notification system removed - now using global toast notifications

    // Initialize
    updateEmojiSummary();
    updateVersionSummary();
    updateQuerySummary();
    updateAutosaveSummary();

    // WordPress Core Tweaks enhanced functionality loaded
});
</script>
