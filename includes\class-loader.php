<?php
/**
 * Main loader class for Redco Optimizer
 *
 * This class handles the initialization and loading of all plugin components,
 * including modules, admin UI, and core functionality.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Optimizer_Loader {

    /**
     * Available modules
     */
    private $modules = array();

    /**
     * Plugin options
     */
    private $options = array();

    /**
     * Initialize the plugin
     */
    public function init() {
        // Load text domain FIRST to prevent translation loading warnings
        $this->load_textdomain();

        // Load core files
        $this->load_dependencies();

        // Initialize hooks
        $this->init_hooks();

        // Load modules AFTER init hook to ensure text domain is available
        add_action('init', array($this, 'load_modules'), 1);

        // Initialize admin if in admin area
        if (is_admin()) {
            add_action('init', array($this, 'init_admin'), 2);
        }
    }

    /**
     * Load required dependencies
     */
    private function load_dependencies() {
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/helpers.php';
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-admin-ui.php';
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-license-handler.php';
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-addon-handler.php';
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-setup-wizard.php';
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-progress-tracker.php';
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-progress-processors.php';
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-performance-optimizer.php';
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-pagespeed-optimizer.php';
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_assets'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
        add_action('admin_init', array($this, 'check_setup_wizard_redirect'));
    }

    /**
     * Load plugin text domain
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'redco-optimizer',
            false,
            dirname(REDCO_OPTIMIZER_PLUGIN_BASENAME) . '/languages'
        );
    }

    /**
     * Enqueue frontend assets
     */
    public function enqueue_frontend_assets() {
        // Only enqueue if needed by active modules and not in admin
        if (is_admin() || is_customize_preview()) {
            return;
        }

        $enabled_modules = $this->get_enabled_modules();

        // Only load lazy load script if module is enabled and we're on frontend
        if (in_array('lazy_load', $enabled_modules)) {
            wp_enqueue_script(
                'redco-lazy-load',
                REDCO_OPTIMIZER_PLUGIN_URL . 'assets/js/lazy-load.js',
                array(),
                REDCO_OPTIMIZER_VERSION,
                true
            );
        }
    }

    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook) {
        // Only load on plugin pages
        if (strpos($hook, 'redco-optimizer') === false) {
            return;
        }

        // Skip loading main admin assets on setup wizard page
        if (strpos($hook, 'redco-optimizer-setup') !== false) {
            return;
        }

        // Generate cache-busting version with file modification time
        $css_file_path = REDCO_OPTIMIZER_PLUGIN_DIR . 'assets/css/admin-style.css';
        $css_version = REDCO_OPTIMIZER_VERSION . '.' . filemtime($css_file_path);

        wp_enqueue_style(
            'redco-admin-style',
            REDCO_OPTIMIZER_PLUGIN_URL . 'assets/css/admin-style.css',
            array(),
            $css_version
        );

        // Enhanced UI styles for sidebar navigation and setup wizard
        $enhanced_css_path = REDCO_OPTIMIZER_PLUGIN_DIR . 'assets/css/enhanced-ui.css';
        $enhanced_css_version = REDCO_OPTIMIZER_VERSION . '.' . filemtime($enhanced_css_path);

        wp_enqueue_style(
            'redco-enhanced-ui',
            REDCO_OPTIMIZER_PLUGIN_URL . 'assets/css/enhanced-ui.css',
            array('redco-admin-style'),
            $enhanced_css_version
        );

        // Load simplified settings styles and scripts on settings page
        if (strpos($hook, 'redco-optimizer-settings') !== false) {
            // Enqueue simplified settings CSS (card-free design)
            $settings_css_path = REDCO_OPTIMIZER_PLUGIN_DIR . 'assets/css/settings-simplified.css';
            if (file_exists($settings_css_path)) {
                $settings_css_version = REDCO_OPTIMIZER_VERSION . '.' . filemtime($settings_css_path);

                wp_enqueue_style(
                    'redco-optimizer-settings-simplified',
                    REDCO_OPTIMIZER_PLUGIN_URL . 'assets/css/settings-simplified.css',
                    array(),
                    $settings_css_version
                );
            }

            // settings-clean.js functionality has been consolidated into settings-script.js

            // Enqueue consolidated settings script with comprehensive auto-save
            $main_settings_js_path = REDCO_OPTIMIZER_PLUGIN_DIR . 'assets/js/settings-script.js';
            if (file_exists($main_settings_js_path)) {
                $main_settings_js_version = REDCO_OPTIMIZER_VERSION . '.' . filemtime($main_settings_js_path);

                wp_enqueue_script(
                    'redco-optimizer-settings-script',
                    REDCO_OPTIMIZER_PLUGIN_URL . 'assets/js/settings-script.js',
                    array('jquery'),
                    $main_settings_js_version,
                    true
                );

                // Ensure the same nonce is available for both scripts
                wp_localize_script('redco-optimizer-settings-script', 'redco_settings', array(
                    'nonce' => wp_create_nonce('redco_settings_nonce'),
                    'ajaxurl' => admin_url('admin-ajax.php')
                ));
            }

            // Load auto-save test script (only in debug mode)
            if (defined('WP_DEBUG') && WP_DEBUG) {
                $test_js_path = REDCO_OPTIMIZER_PLUGIN_DIR . 'assets/js/auto-save-test.js';
                if (file_exists($test_js_path)) {
                    $test_js_version = REDCO_OPTIMIZER_VERSION . '.' . filemtime($test_js_path);

                    wp_enqueue_script(
                        'redco-optimizer-auto-save-test',
                        REDCO_OPTIMIZER_PLUGIN_URL . 'assets/js/auto-save-test.js',
                        array('jquery'),
                        $test_js_version,
                        true
                    );
                }
            }
        }

        // Load modules-specific styles on modules page
        if (strpos($hook, 'redco-optimizer-modules') !== false) {
            wp_enqueue_style(
                'redco-optimizer-modules',
                REDCO_OPTIMIZER_PLUGIN_URL . 'assets/css/modules-style.css',
                array('redco-admin-style'),
                REDCO_OPTIMIZER_VERSION
            );
        }

        // Load standardized module layout CSS for all module pages
        if (strpos($hook, 'redco-optimizer') !== false) {
            wp_enqueue_style(
                'redco-module-layout-standard',
                REDCO_OPTIMIZER_PLUGIN_URL . 'assets/css/module-layout-standard.css',
                array('redco-admin-style'),
                REDCO_OPTIMIZER_VERSION
            );
        }

        // Determine JavaScript dependencies based on page
        $js_dependencies = array('jquery');

        // Enqueue Chart.js for Core Web Vitals chart (only on dashboard page)
        if (strpos($hook, 'redco-optimizer') !== false && !strpos($hook, 'redco-optimizer-modules') && !strpos($hook, 'redco-optimizer-settings')) {
            wp_enqueue_script(
                'chart-js',
                'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js',
                array(),
                '3.9.1',
                true
            );
            $js_dependencies[] = 'chart-js';
        }

        // Generate cache-busting version for JavaScript
        $js_file_path = REDCO_OPTIMIZER_PLUGIN_DIR . 'assets/js/admin-scripts.js';
        $js_version = REDCO_OPTIMIZER_VERSION . '.' . filemtime($js_file_path);

        wp_enqueue_script(
            'redco-admin-scripts',
            REDCO_OPTIMIZER_PLUGIN_URL . 'assets/js/admin-scripts.js',
            $js_dependencies,
            $js_version,
            true
        );

        // Debug scripts removed for production

        // Get performance settings for JavaScript
        $performance_options = get_option('redco_optimizer_performance', array());
        $update_interval = isset($performance_options['update_interval']) ? $performance_options['update_interval'] : 30;
        $monitoring_enabled = isset($performance_options['enable_monitoring']) ? $performance_options['enable_monitoring'] : 1;

        // Check if plugin is enabled
        $general_options = get_option('redco_optimizer_options', array());
        $plugin_enabled = isset($general_options['enabled']) ? $general_options['enabled'] : 1;

        // Localize script for AJAX
        wp_localize_script('redco-admin-scripts', 'redcoAjax', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('redco_optimizer_nonce'),
            'settings' => array(
                'performanceUpdateInterval' => $update_interval * 1000, // Convert to milliseconds
                'monitoringEnabled' => $monitoring_enabled,
                'pluginEnabled' => $plugin_enabled
            ),
            'strings' => array(
                'saving' => __('Saving...', 'redco-optimizer'),
                'saved' => __('Settings saved!', 'redco-optimizer'),
                'error' => __('Error saving settings.', 'redco-optimizer')
            ),
            'debug' => array(
                'cssVersion' => $css_version,
                'jsVersion' => $js_version,
                'cssUrl' => REDCO_OPTIMIZER_PLUGIN_URL . 'assets/css/admin-style.css',
                'jsUrl' => REDCO_OPTIMIZER_PLUGIN_URL . 'assets/js/admin-scripts.js'
            )
        ));
    }

    /**
     * Load all available modules
     */
    public function load_modules() {
        $modules_dir = REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/';

        // Define available modules
        $available_modules = array(
            'page-cache' => array(
                'name' => __('Page Cache', 'redco-optimizer'),
                'description' => __('Enable full page caching for faster load times', 'redco-optimizer'),
                'type' => 'free',
                'file' => 'class-page-cache.php'
            ),
            'lazy-load' => array(
                'name' => __('Lazy Load Images', 'redco-optimizer'),
                'description' => __('Load images only when they come into view', 'redco-optimizer'),
                'type' => 'free',
                'file' => 'class-lazy-load.php'
            ),
            'css-js-minifier' => array(
                'name' => __('CSS/JS Minifier', 'redco-optimizer'),
                'description' => __('Minify CSS and JavaScript files', 'redco-optimizer'),
                'type' => 'free',
                'file' => 'class-css-js-minifier.php'
            ),
            'database-cleanup' => array(
                'name' => __('Database Cleanup', 'redco-optimizer'),
                'description' => __('Clean up database from unnecessary data', 'redco-optimizer'),
                'type' => 'free',
                'file' => 'class-database-cleanup.php'
            ),
            'heartbeat-control' => array(
                'name' => __('Heartbeat Control', 'redco-optimizer'),
                'description' => __('Control WordPress Heartbeat API frequency', 'redco-optimizer'),
                'type' => 'free',
                'file' => 'class-heartbeat-control.php'
            ),
            'wordpress-core-tweaks' => array(
                'name' => __('WordPress Core Tweaks', 'redco-optimizer'),
                'description' => __('Optimize WordPress core features: emoji removal, version strings, and autosave', 'redco-optimizer'),
                'type' => 'free',
                'file' => 'class-wordpress-core-tweaks.php'
            ),
            'critical-resource-optimizer' => array(
                'name' => __('Critical Resource Optimizer', 'redco-optimizer'),
                'description' => __('Eliminate render-blocking resources and optimize critical above-the-fold content for maximum PageSpeed performance', 'redco-optimizer'),
                'type' => 'free',
                'file' => 'class-critical-resource-optimizer.php'
            ),
            'diagnostic-autofix' => array(
                'name' => __('Diagnostic & Auto-Fix', 'redco-optimizer'),
                'description' => __('Comprehensive WordPress performance diagnostic tool with intelligent auto-fix capabilities for all performance-related issues', 'redco-optimizer'),
                'type' => 'free',
                'file' => 'class-diagnostic-autofix.php',
                'class' => 'Redco_Diagnostic_AutoFix'
            ),
            // Pro modules (coming soon)
            'ai-auto-optimizer' => array(
                'name' => __('AI-Based Auto Optimizer', 'redco-optimizer'),
                'description' => __('AI-powered automatic optimization', 'redco-optimizer'),
                'type' => 'premium',
                'coming_soon' => true
            ),
            'ai-image-upscaler' => array(
                'name' => __('AI Image Upscaler', 'redco-optimizer'),
                'description' => __('AI-powered image upscaling and optimization', 'redco-optimizer'),
                'type' => 'premium',
                'coming_soon' => true
            ),
            'cdn-integrations' => array(
                'name' => __('CDN Integrations', 'redco-optimizer'),
                'description' => __('Integrate with popular CDN services', 'redco-optimizer'),
                'type' => 'premium',
                'coming_soon' => true
            ),
            'woocommerce-booster' => array(
                'name' => __('WooCommerce Booster', 'redco-optimizer'),
                'description' => __('Specialized WooCommerce optimizations', 'redco-optimizer'),
                'type' => 'premium',
                'coming_soon' => true
            ),
            'preload-crawler' => array(
                'name' => __('Preload Crawler', 'redco-optimizer'),
                'description' => __('Intelligent page preloading system', 'redco-optimizer'),
                'type' => 'premium',
                'coming_soon' => true
            ),
            'smart-webp-conversion' => array(
                'name' => __('Smart WebP Conversion', 'redco-optimizer'),
                'description' => __('Automatically convert images to WebP format for better performance', 'redco-optimizer'),
                'type' => 'free',
                'file' => 'webp-global-handlers.php'
            ),
            'role-based-access' => array(
                'name' => __('Role-Based Module Access', 'redco-optimizer'),
                'description' => __('Control module access by user roles', 'redco-optimizer'),
                'type' => 'premium',
                'coming_soon' => true
            )
        );

        $this->modules = apply_filters('redco_optimizer_modules', $available_modules);

        // Load free modules that exist
        foreach ($this->modules as $module_key => $module_data) {
            if ($module_data['type'] === 'free' && !isset($module_data['coming_soon'])) {
                $module_file = $modules_dir . $module_key . '/' . $module_data['file'];
                if (file_exists($module_file)) {
                    require_once $module_file;
                }
            }
        }
    }

    /**
     * Initialize admin interface
     */
    public function init_admin() {
        $admin_ui = new Redco_Optimizer_Admin_UI($this->modules);
        $admin_ui->init();

        // Initialize setup wizard
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-setup-wizard.php';
        $setup_wizard = new Redco_Optimizer_Setup_Wizard();

        // Initialize CSS troubleshooter
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-css-troubleshooter.php';
        $css_troubleshooter = new Redco_Optimizer_CSS_Troubleshooter();
        $css_troubleshooter->init();

        // Initialize performance analyzer
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-performance-analyzer.php';
        $performance_analyzer = new Redco_Optimizer_Performance_Analyzer();
        $performance_analyzer->init();

        // Initialize PageSpeed diagnostics
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-pagespeed-diagnostics.php';
        $pagespeed_diagnostics = new Redco_Optimizer_PageSpeed_Diagnostics();
        $pagespeed_diagnostics->init();

        // Initialize Module Auditor
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-module-auditor.php';
        $module_auditor = new Redco_Optimizer_Module_Auditor();
        $module_auditor->init();

        // Initialize Module Consolidation Cleanup
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-module-consolidation-cleanup.php';

        // Check for setup wizard redirect
        add_action('admin_init', array($this, 'check_setup_wizard_redirect'));
    }

    /**
     * Check if we should redirect to setup wizard
     */
    public function check_setup_wizard_redirect() {
        // Only redirect on plugin activation
        if (get_transient('redco_optimizer_activation_redirect')) {
            delete_transient('redco_optimizer_activation_redirect');

            // Don't redirect if we're already on the setup page or if setup was completed/skipped
            if (isset($_GET['page']) && $_GET['page'] === 'redco-optimizer-setup') {
                return;
            }

            require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-setup-wizard.php';
            $setup_wizard = new Redco_Optimizer_Setup_Wizard();
            if ($setup_wizard->should_show_wizard()) {
                wp_safe_redirect(admin_url('admin.php?page=redco-optimizer-setup'));
                exit;
            }
        }
    }

    /**
     * Get enabled modules
     */
    public function get_enabled_modules() {
        $options = get_option('redco_optimizer_options', array());
        return isset($options['modules_enabled']) ? $options['modules_enabled'] : array();
    }

    /**
     * Get all modules
     */
    public function get_modules() {
        return $this->modules;
    }
}
