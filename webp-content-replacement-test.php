<?php
/**
 * Comprehensive Test for "Replace Images in Content" Setting
 * 
 * This file tests the WebP content replacement functionality
 * to verify if it's working correctly.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test the Replace Images in Content functionality
 */
function test_webp_content_replacement() {
    echo "<h2>🔍 WebP Content Replacement Test Results</h2>";
    
    // Test 1: Check if WebP module is loaded
    echo "<h3>1. Module Loading Test</h3>";
    if (class_exists('Redco_Smart_WebP_Conversion')) {
        echo "✅ WebP module class is loaded<br>";
        $webp_instance = new Redco_Smart_WebP_Conversion();
    } else {
        echo "❌ WebP module class is NOT loaded<br>";
        return;
    }
    
    // Test 2: Check setting retrieval
    echo "<h3>2. Setting Retrieval Test</h3>";
    $settings = $webp_instance->get_settings();
    $replace_in_content = $settings['replace_in_content'] ?? false;
    echo "Replace in Content setting: " . ($replace_in_content ? "✅ ENABLED" : "❌ DISABLED") . "<br>";
    
    // Test 3: Browser support detection
    echo "<h3>3. Browser Support Detection Test</h3>";
    $browser_supports_webp = method_exists($webp_instance, 'browser_supports_webp') ? 
        $webp_instance->browser_supports_webp() : false;
    echo "Current browser WebP support: " . ($browser_supports_webp ? "✅ SUPPORTED" : "❌ NOT SUPPORTED") . "<br>";
    echo "HTTP_ACCEPT header: " . ($_SERVER['HTTP_ACCEPT'] ?? 'Not available') . "<br>";
    
    // Test 4: Hook registration
    echo "<h3>4. WordPress Hook Registration Test</h3>";
    $content_filter_priority = has_filter('the_content', array($webp_instance, 'replace_images_in_content'));
    echo "the_content filter registered: " . ($content_filter_priority !== false ? "✅ YES (Priority: $content_filter_priority)" : "❌ NO") . "<br>";
    
    $image_src_filter_priority = has_filter('wp_get_attachment_image_src', array($webp_instance, 'serve_webp_if_supported'));
    echo "wp_get_attachment_image_src filter registered: " . ($image_src_filter_priority !== false ? "✅ YES (Priority: $image_src_filter_priority)" : "❌ NO") . "<br>";
    
    // Test 5: Content replacement simulation
    echo "<h3>5. Content Replacement Simulation Test</h3>";
    
    // Simulate content with images
    $test_content = '
        <p>This is a test post with images:</p>
        <img src="/wp-content/uploads/2024/01/test-image.jpg" alt="Test Image 1" class="wp-image-123">
        <p>Another paragraph with an image:</p>
        <img src="/wp-content/uploads/2024/01/another-image.png" alt="Test Image 2" width="300" height="200">
        <p>External image (should not be replaced):</p>
        <img src="https://external-site.com/image.jpg" alt="External Image">
    ';
    
    echo "<strong>Original content:</strong><br>";
    echo "<pre>" . htmlspecialchars($test_content) . "</pre>";
    
    // Test the replacement function
    if (method_exists($webp_instance, 'replace_images_in_content')) {
        $processed_content = $webp_instance->replace_images_in_content($test_content);
        echo "<strong>Processed content:</strong><br>";
        echo "<pre>" . htmlspecialchars($processed_content) . "</pre>";
        
        if ($test_content !== $processed_content) {
            echo "✅ Content was processed (changes detected)<br>";
        } else {
            echo "⚠️ Content was not changed (no WebP files found or browser doesn't support WebP)<br>";
        }
    } else {
        echo "❌ replace_images_in_content method not found<br>";
    }
    
    // Test 6: URL conversion logic
    echo "<h3>6. URL Conversion Logic Test</h3>";
    if (method_exists($webp_instance, 'get_webp_url_from_original')) {
        $test_url = "/wp-content/uploads/2024/01/test-image.jpg";
        $webp_url = $webp_instance->get_webp_url_from_original($test_url);
        echo "Original URL: $test_url<br>";
        echo "WebP URL: $webp_url<br>";
        echo "Conversion result: " . ($webp_url !== $test_url ? "✅ URL was converted" : "⚠️ URL was not converted (WebP file may not exist)") . "<br>";
    } else {
        echo "❌ get_webp_url_from_original method not found<br>";
    }
    
    // Test 7: Regex pattern test
    echo "<h3>7. Regex Pattern Test</h3>";
    $test_html = '<img src="/wp-content/uploads/2024/01/test.jpg" alt="Test" class="wp-image-123">';
    $pattern = '/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i';
    
    if (preg_match($pattern, $test_html, $matches)) {
        echo "✅ Regex pattern matches correctly<br>";
        echo "Full match: " . htmlspecialchars($matches[0]) . "<br>";
        echo "URL extracted: " . htmlspecialchars($matches[1]) . "<br>";
    } else {
        echo "❌ Regex pattern does not match<br>";
    }
    
    // Test 8: Performance considerations
    echo "<h3>8. Performance Analysis</h3>";
    $start_time = microtime(true);
    
    // Simulate processing a large content block
    $large_content = str_repeat($test_content, 10);
    if (method_exists($webp_instance, 'replace_images_in_content')) {
        $processed_large_content = $webp_instance->replace_images_in_content($large_content);
    }
    
    $end_time = microtime(true);
    $processing_time = ($end_time - $start_time) * 1000; // Convert to milliseconds
    
    echo "Processing time for large content: " . number_format($processing_time, 2) . " ms<br>";
    echo "Performance status: " . ($processing_time < 100 ? "✅ GOOD" : ($processing_time < 500 ? "⚠️ ACCEPTABLE" : "❌ SLOW")) . "<br>";
    
    // Test 9: Integration points
    echo "<h3>9. Integration Points Test</h3>";
    echo "Module enabled: " . (redco_is_module_enabled('smart-webp-conversion') ? "✅ YES" : "❌ NO") . "<br>";
    echo "WordPress upload directory accessible: " . (wp_upload_dir()['error'] === false ? "✅ YES" : "❌ NO") . "<br>";
    
    // Test 10: Edge cases
    echo "<h3>10. Edge Cases Test</h3>";
    
    $edge_cases = array(
        'Empty content' => '',
        'No images' => '<p>This content has no images.</p>',
        'Malformed img tag' => '<img src="/test.jpg">', // Missing closing >
        'Single quotes' => "<img src='/wp-content/uploads/test.jpg' alt='Test'>",
        'Mixed quotes' => '<img src="/wp-content/uploads/test.jpg" alt=\'Test\'>',
        'Multiple images' => '<img src="/wp-content/uploads/1.jpg"><img src="/wp-content/uploads/2.png">',
    );
    
    foreach ($edge_cases as $case_name => $case_content) {
        if (method_exists($webp_instance, 'replace_images_in_content')) {
            $result = $webp_instance->replace_images_in_content($case_content);
            echo "$case_name: " . (is_string($result) ? "✅ HANDLED" : "❌ ERROR") . "<br>";
        }
    }
    
    echo "<h3>📋 Summary</h3>";
    echo "<p>The test has completed. Review the results above to identify any issues with the 'Replace Images in Content' functionality.</p>";
}

// Run the test if this file is accessed directly
if (isset($_GET['run_webp_test']) && $_GET['run_webp_test'] === '1') {
    test_webp_content_replacement();
}
?>
