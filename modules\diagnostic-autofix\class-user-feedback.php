<?php
/**
 * User Feedback Collection System for Diagnostic & Auto-Fix Module
 * 
 * Collects user experience reports and performance feedback
 * 
 * @package RedcoOptimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Diagnostic_User_Feedback {
    
    /**
     * Initialize feedback system
     */
    public static function init() {
        // AJAX handlers
        add_action('wp_ajax_redco_submit_feedback', array(__CLASS__, 'ajax_submit_feedback'));
        add_action('wp_ajax_redco_get_feedback_stats', array(__CLASS__, 'ajax_get_feedback_stats'));
        
        // Admin hooks
        add_action('admin_footer', array(__CLASS__, 'add_feedback_modal'));
        add_action('admin_enqueue_scripts', array(__CLASS__, 'enqueue_feedback_scripts'));
        
        // Schedule feedback cleanup
        if (!wp_next_scheduled('redco_feedback_cleanup')) {
            wp_schedule_event(time(), 'weekly', 'redco_feedback_cleanup');
        }
        add_action('redco_feedback_cleanup', array(__CLASS__, 'cleanup_old_feedback'));
    }
    
    /**
     * Enqueue feedback scripts
     */
    public static function enqueue_feedback_scripts($hook) {
        // Only load on Redco Optimizer pages
        if (strpos($hook, 'redco-optimizer') === false) {
            return;
        }
        
        wp_add_inline_script('redco-admin-scripts', self::get_feedback_js());
    }
    
    /**
     * Get feedback JavaScript
     */
    private static function get_feedback_js() {
        return "
        // User Feedback System
        window.RedcoFeedback = {
            init: function() {
                this.bindEvents();
                this.schedulePeriodicFeedback();
            },
            
            bindEvents: function() {
                // Feedback trigger button
                jQuery(document).on('click', '.redco-feedback-trigger', this.showFeedbackModal);
                
                // Feedback form submission
                jQuery(document).on('submit', '#redco-feedback-form', this.submitFeedback);
                
                // Quick feedback buttons
                jQuery(document).on('click', '.redco-quick-feedback', this.submitQuickFeedback);
                
                // Performance rating
                jQuery(document).on('click', '.performance-rating .star', this.setPerformanceRating);
            },
            
            schedulePeriodicFeedback: function() {
                // Show feedback request after 5 minutes of usage
                setTimeout(() => {
                    const lastFeedback = localStorage.getItem('redco_last_feedback');
                    const now = Date.now();
                    const oneWeek = 7 * 24 * 60 * 60 * 1000;
                    
                    if (!lastFeedback || (now - parseInt(lastFeedback)) > oneWeek) {
                        this.showPeriodicFeedbackRequest();
                    }
                }, 300000); // 5 minutes
            },
            
            showPeriodicFeedbackRequest: function() {
                const notification = jQuery('<div class=\"redco-feedback-notification\">' +
                    '<div class=\"feedback-content\">' +
                        '<h4>How is your experience with the Diagnostic module?</h4>' +
                        '<p>Your feedback helps us improve performance and user experience.</p>' +
                        '<div class=\"feedback-actions\">' +
                            '<button class=\"button redco-quick-feedback\" data-rating=\"5\" data-comment=\"Great experience\">😊 Great</button>' +
                            '<button class=\"button redco-quick-feedback\" data-rating=\"3\" data-comment=\"Good but could be better\">😐 Good</button>' +
                            '<button class=\"button redco-quick-feedback\" data-rating=\"1\" data-comment=\"Poor experience\">😞 Poor</button>' +
                            '<button class=\"button redco-feedback-trigger\">📝 Detailed Feedback</button>' +
                        '</div>' +
                        '<button class=\"feedback-close\" onclick=\"this.parentElement.parentElement.remove()\">×</button>' +
                    '</div>' +
                '</div>');
                
                jQuery('body').append(notification);
                
                // Auto-hide after 30 seconds
                setTimeout(() => {
                    notification.fadeOut();
                }, 30000);
            },
            
            showFeedbackModal: function(e) {
                if (e) e.preventDefault();
                
                jQuery('#redco-feedback-modal').show();
                jQuery('#redco-feedback-form')[0].reset();
                
                // Pre-fill performance data
                RedcoFeedback.prefillPerformanceData();
            },
            
            prefillPerformanceData: function() {
                // Get current performance metrics
                const loadTime = performance.now();
                const userAgent = navigator.userAgent;
                const screenSize = screen.width + 'x' + screen.height;
                
                jQuery('#feedback-load-time').val(Math.round(loadTime));
                jQuery('#feedback-user-agent').val(userAgent);
                jQuery('#feedback-screen-size').val(screenSize);
                jQuery('#feedback-timestamp').val(Date.now());
            },
            
            setPerformanceRating: function(e) {
                const rating = jQuery(e.target).data('rating');
                jQuery('.performance-rating .star').removeClass('active');
                jQuery('.performance-rating .star').each(function(index) {
                    if (index < rating) {
                        jQuery(this).addClass('active');
                    }
                });
                jQuery('#feedback-performance-rating').val(rating);
            },
            
            submitQuickFeedback: function(e) {
                e.preventDefault();
                
                const rating = jQuery(e.target).data('rating');
                const comment = jQuery(e.target).data('comment');
                
                RedcoFeedback.submitFeedbackData({
                    type: 'quick',
                    rating: rating,
                    comment: comment,
                    timestamp: Date.now()
                });
                
                // Hide notification
                jQuery('.redco-feedback-notification').fadeOut();
                
                // Store last feedback time
                localStorage.setItem('redco_last_feedback', Date.now());
                
                // Show thank you message
                showToast('Thank you for your feedback!', 'success', 3000);
            },
            
            submitFeedback: function(e) {
                e.preventDefault();
                
                const formData = new FormData(e.target);
                const feedbackData = {};
                
                for (let [key, value] of formData.entries()) {
                    feedbackData[key] = value;
                }
                
                feedbackData.type = 'detailed';
                feedbackData.timestamp = Date.now();
                
                RedcoFeedback.submitFeedbackData(feedbackData);
                
                // Hide modal
                jQuery('#redco-feedback-modal').hide();
                
                // Store last feedback time
                localStorage.setItem('redco_last_feedback', Date.now());
                
                // Show thank you message
                showToast('Thank you for your detailed feedback!', 'success', 4000);
            },
            
            submitFeedbackData: function(data) {
                jQuery.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'redco_submit_feedback',
                        nonce: '" . wp_create_nonce('redco_feedback_nonce') . "',
                        feedback: data
                    },
                    success: function(response) {
                        if (response.success) {
                            console.log('Feedback submitted successfully');
                        }
                    },
                    error: function() {
                        console.log('Failed to submit feedback');
                    }
                });
            }
        };
        
        // Initialize feedback system
        jQuery(document).ready(function() {
            if (window.location.href.includes('redco-optimizer')) {
                RedcoFeedback.init();
            }
        });
        ";
    }
    
    /**
     * Add feedback modal to admin footer
     */
    public static function add_feedback_modal() {
        // Only show on Redco Optimizer pages
        if (!isset($_GET['page']) || strpos($_GET['page'], 'redco-optimizer') === false) {
            return;
        }
        ?>
        
        <div id="redco-feedback-modal" class="redco-modal" style="display: none;">
            <div class="redco-modal-content">
                <div class="redco-modal-header">
                    <h3><?php _e('Performance Feedback', 'redco-optimizer'); ?></h3>
                    <button class="redco-modal-close" onclick="jQuery('#redco-feedback-modal').hide()">×</button>
                </div>
                
                <form id="redco-feedback-form">
                    <div class="feedback-section">
                        <label><?php _e('How would you rate the performance?', 'redco-optimizer'); ?></label>
                        <div class="performance-rating">
                            <span class="star" data-rating="1">★</span>
                            <span class="star" data-rating="2">★</span>
                            <span class="star" data-rating="3">★</span>
                            <span class="star" data-rating="4">★</span>
                            <span class="star" data-rating="5">★</span>
                        </div>
                        <input type="hidden" id="feedback-performance-rating" name="performance_rating" value="">
                    </div>
                    
                    <div class="feedback-section">
                        <label for="feedback-experience"><?php _e('Overall Experience', 'redco-optimizer'); ?></label>
                        <select id="feedback-experience" name="experience" required>
                            <option value=""><?php _e('Select...', 'redco-optimizer'); ?></option>
                            <option value="excellent"><?php _e('Excellent', 'redco-optimizer'); ?></option>
                            <option value="good"><?php _e('Good', 'redco-optimizer'); ?></option>
                            <option value="fair"><?php _e('Fair', 'redco-optimizer'); ?></option>
                            <option value="poor"><?php _e('Poor', 'redco-optimizer'); ?></option>
                        </select>
                    </div>
                    
                    <div class="feedback-section">
                        <label for="feedback-load-speed"><?php _e('How fast did the tab load?', 'redco-optimizer'); ?></label>
                        <select id="feedback-load-speed" name="load_speed" required>
                            <option value=""><?php _e('Select...', 'redco-optimizer'); ?></option>
                            <option value="very-fast"><?php _e('Very Fast (< 1s)', 'redco-optimizer'); ?></option>
                            <option value="fast"><?php _e('Fast (1-2s)', 'redco-optimizer'); ?></option>
                            <option value="moderate"><?php _e('Moderate (2-5s)', 'redco-optimizer'); ?></option>
                            <option value="slow"><?php _e('Slow (5-10s)', 'redco-optimizer'); ?></option>
                            <option value="very-slow"><?php _e('Very Slow (>10s)', 'redco-optimizer'); ?></option>
                        </select>
                    </div>
                    
                    <div class="feedback-section">
                        <label for="feedback-issues"><?php _e('Any issues encountered?', 'redco-optimizer'); ?></label>
                        <div class="checkbox-group">
                            <label><input type="checkbox" name="issues[]" value="slow-loading"> <?php _e('Slow loading', 'redco-optimizer'); ?></label>
                            <label><input type="checkbox" name="issues[]" value="javascript-errors"> <?php _e('JavaScript errors', 'redco-optimizer'); ?></label>
                            <label><input type="checkbox" name="issues[]" value="ui-glitches"> <?php _e('UI glitches', 'redco-optimizer'); ?></label>
                            <label><input type="checkbox" name="issues[]" value="unresponsive"> <?php _e('Unresponsive interface', 'redco-optimizer'); ?></label>
                            <label><input type="checkbox" name="issues[]" value="other"> <?php _e('Other', 'redco-optimizer'); ?></label>
                        </div>
                    </div>
                    
                    <div class="feedback-section">
                        <label for="feedback-comments"><?php _e('Additional Comments', 'redco-optimizer'); ?></label>
                        <textarea id="feedback-comments" name="comments" rows="4" placeholder="<?php _e('Tell us about your experience...', 'redco-optimizer'); ?>"></textarea>
                    </div>
                    
                    <div class="feedback-section">
                        <label for="feedback-suggestions"><?php _e('Suggestions for Improvement', 'redco-optimizer'); ?></label>
                        <textarea id="feedback-suggestions" name="suggestions" rows="3" placeholder="<?php _e('How can we make it better?', 'redco-optimizer'); ?>"></textarea>
                    </div>
                    
                    <!-- Hidden fields for technical data -->
                    <input type="hidden" id="feedback-load-time" name="load_time" value="">
                    <input type="hidden" id="feedback-user-agent" name="user_agent" value="">
                    <input type="hidden" id="feedback-screen-size" name="screen_size" value="">
                    <input type="hidden" id="feedback-timestamp" name="timestamp" value="">
                    
                    <div class="feedback-actions">
                        <button type="button" class="button" onclick="jQuery('#redco-feedback-modal').hide()">
                            <?php _e('Cancel', 'redco-optimizer'); ?>
                        </button>
                        <button type="submit" class="button button-primary">
                            <?php _e('Submit Feedback', 'redco-optimizer'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <style>
        .redco-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            z-index: 100000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .redco-modal-content {
            background: white;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
        }
        
        .redco-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .redco-modal-header h3 {
            margin: 0;
        }
        
        .redco-modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }
        
        .feedback-section {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .feedback-section:last-child {
            border-bottom: none;
        }
        
        .feedback-section label {
            display: block;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .feedback-section select,
        .feedback-section textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .performance-rating {
            display: flex;
            gap: 5px;
            margin: 10px 0;
        }
        
        .performance-rating .star {
            font-size: 24px;
            color: #ddd;
            cursor: pointer;
            transition: color 0.2s;
        }
        
        .performance-rating .star:hover,
        .performance-rating .star.active {
            color: #ffc107;
        }
        
        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        
        .checkbox-group label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: normal;
            margin: 0;
        }
        
        .feedback-actions {
            padding: 20px;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            border-top: 1px solid #e0e0e0;
        }
        
        .redco-feedback-notification {
            position: fixed;
            top: 32px;
            right: 20px;
            background: white;
            border: 1px solid #4CAF50;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 99999;
            max-width: 400px;
            animation: slideIn 0.3s ease-out;
        }
        
        .feedback-content {
            padding: 20px;
            position: relative;
        }
        
        .feedback-content h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .feedback-content p {
            margin: 0 0 15px 0;
            color: #666;
            font-size: 14px;
        }
        
        .feedback-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .feedback-close {
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #999;
        }
        
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        </style>
        <?php
    }
    
    /**
     * AJAX handler for feedback submission
     */
    public static function ajax_submit_feedback() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_feedback_nonce')) {
            wp_die('Security check failed');
        }
        
        $feedback = isset($_POST['feedback']) ? $_POST['feedback'] : array();
        
        // Sanitize feedback data
        $sanitized_feedback = array(
            'type' => sanitize_text_field($feedback['type'] ?? 'unknown'),
            'timestamp' => intval($feedback['timestamp'] ?? time() * 1000),
            'performance_rating' => intval($feedback['performance_rating'] ?? 0),
            'experience' => sanitize_text_field($feedback['experience'] ?? ''),
            'load_speed' => sanitize_text_field($feedback['load_speed'] ?? ''),
            'issues' => isset($feedback['issues']) ? array_map('sanitize_text_field', $feedback['issues']) : array(),
            'comments' => sanitize_textarea_field($feedback['comments'] ?? ''),
            'suggestions' => sanitize_textarea_field($feedback['suggestions'] ?? ''),
            'load_time' => intval($feedback['load_time'] ?? 0),
            'user_agent' => sanitize_text_field($feedback['user_agent'] ?? ''),
            'screen_size' => sanitize_text_field($feedback['screen_size'] ?? ''),
            'user_id' => get_current_user_id(),
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'page_url' => $_SERVER['HTTP_REFERER'] ?? ''
        );
        
        // Store feedback
        self::store_feedback($sanitized_feedback);
        
        // Send response
        wp_send_json_success(array(
            'message' => 'Feedback submitted successfully',
            'feedback_id' => $sanitized_feedback['timestamp']
        ));
    }
    
    /**
     * Store feedback in database
     */
    private static function store_feedback($feedback) {
        // Store in daily aggregated format
        $date = date('Y-m-d', $feedback['timestamp'] / 1000);
        $option_key = 'redco_diagnostic_feedback_' . $date;
        
        $daily_feedback = get_option($option_key, array(
            'date' => $date,
            'total_submissions' => 0,
            'avg_performance_rating' => 0,
            'experience_distribution' => array(),
            'load_speed_distribution' => array(),
            'common_issues' => array(),
            'feedback_entries' => array()
        ));
        
        // Update aggregated data
        $daily_feedback['total_submissions']++;
        
        if ($feedback['performance_rating'] > 0) {
            $daily_feedback['avg_performance_rating'] = (($daily_feedback['avg_performance_rating'] * ($daily_feedback['total_submissions'] - 1)) + $feedback['performance_rating']) / $daily_feedback['total_submissions'];
        }
        
        // Track experience distribution
        if (!empty($feedback['experience'])) {
            if (!isset($daily_feedback['experience_distribution'][$feedback['experience']])) {
                $daily_feedback['experience_distribution'][$feedback['experience']] = 0;
            }
            $daily_feedback['experience_distribution'][$feedback['experience']]++;
        }
        
        // Track load speed distribution
        if (!empty($feedback['load_speed'])) {
            if (!isset($daily_feedback['load_speed_distribution'][$feedback['load_speed']])) {
                $daily_feedback['load_speed_distribution'][$feedback['load_speed']] = 0;
            }
            $daily_feedback['load_speed_distribution'][$feedback['load_speed']]++;
        }
        
        // Track common issues
        foreach ($feedback['issues'] as $issue) {
            if (!isset($daily_feedback['common_issues'][$issue])) {
                $daily_feedback['common_issues'][$issue] = 0;
            }
            $daily_feedback['common_issues'][$issue]++;
        }
        
        // Store raw feedback (keep last 100 entries)
        $daily_feedback['feedback_entries'][] = $feedback;
        if (count($daily_feedback['feedback_entries']) > 100) {
            $daily_feedback['feedback_entries'] = array_slice($daily_feedback['feedback_entries'], -100);
        }
        
        update_option($option_key, $daily_feedback, false); // Don't autoload
    }
    
    /**
     * Get feedback statistics
     */
    public static function get_feedback_stats($days = 7) {
        $stats = array();
        
        for ($i = 0; $i < $days; $i++) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $option_key = 'redco_diagnostic_feedback_' . $date;
            $daily_data = get_option($option_key);
            
            if ($daily_data) {
                $stats[] = $daily_data;
            }
        }
        
        return array_reverse($stats); // Oldest first
    }
    
    /**
     * Cleanup old feedback data
     */
    public static function cleanup_old_feedback() {
        global $wpdb;
        
        // Keep 90 days of feedback data
        $cutoff_date = date('Y-m-d', strtotime('-90 days'));
        
        $old_options = $wpdb->get_col($wpdb->prepare("
            SELECT option_name 
            FROM {$wpdb->options} 
            WHERE option_name LIKE 'redco_diagnostic_feedback_%' 
            AND option_name < %s
        ", 'redco_diagnostic_feedback_' . $cutoff_date));
        
        foreach ($old_options as $option_name) {
            delete_option($option_name);
        }
    }
}

// Initialize feedback system
Redco_Diagnostic_User_Feedback::init();
