<?php
/**
 * WebP Data Inspector
 * 
 * This script inspects the actual WebP conversion data in your database
 * to understand the exact format and help create proper recovery queries.
 */

// WordPress bootstrap
require_once('wp-config.php');

// Security check
if (!current_user_can('manage_options')) {
    die('Access denied. Administrator privileges required.');
}

echo "<h1>WebP Data Inspector</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; background: #f9f9f9; }
    .data { background: #fff; padding: 10px; border-left: 3px solid #0073aa; margin: 10px 0; }
    .error { color: red; }
    .success { color: green; }
    .warning { color: orange; }
    pre { background: #f0f0f0; padding: 10px; overflow-x: auto; }
</style>";

global $wpdb;

// Check 1: WebP conversion metadata
echo "<div class='section'>";
echo "<h2>1. WebP Conversion Metadata Check</h2>";

$webp_meta_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->postmeta} WHERE meta_key = '_webp_conversion_data'");
echo "<p><strong>Total WebP conversion records:</strong> " . ($webp_meta_count ?: 0) . "</p>";

if ($webp_meta_count > 0) {
    echo "<p class='success'>✅ WebP conversion data found!</p>";
    
    // Get sample data
    $sample_data = $wpdb->get_results("
        SELECT post_id, meta_value 
        FROM {$wpdb->postmeta} 
        WHERE meta_key = '_webp_conversion_data' 
        LIMIT 3
    ");
    
    foreach ($sample_data as $sample) {
        echo "<div class='data'>";
        echo "<h4>Post ID: {$sample->post_id}</h4>";
        echo "<p><strong>Raw data:</strong></p>";
        echo "<pre>" . htmlspecialchars(substr($sample->meta_value, 0, 500)) . "...</pre>";
        
        // Try to unserialize
        $unserialized = maybe_unserialize($sample->meta_value);
        if (is_array($unserialized)) {
            echo "<p><strong>Unserialized data:</strong></p>";
            echo "<pre>" . htmlspecialchars(print_r($unserialized, true)) . "</pre>";
            
            // Check for file paths
            if (isset($unserialized['file_path'])) {
                $file_path = $unserialized['file_path'];
                $extension = pathinfo($file_path, PATHINFO_EXTENSION);
                echo "<p><strong>Original file path:</strong> {$file_path}</p>";
                echo "<p><strong>Original extension:</strong> {$extension}</p>";
                echo "<p><strong>File exists:</strong> " . (file_exists($file_path) ? "Yes" : "No") . "</p>";
            }
            
            if (isset($unserialized['webp_path'])) {
                $webp_path = $unserialized['webp_path'];
                echo "<p><strong>WebP file path:</strong> {$webp_path}</p>";
                echo "<p><strong>WebP exists:</strong> " . (file_exists($webp_path) ? "Yes" : "No") . "</p>";
            }
        } else {
            echo "<p class='warning'>⚠️ Could not unserialize data</p>";
        }
        echo "</div>";
    }
} else {
    echo "<p class='error'>❌ No WebP conversion data found</p>";
}
echo "</div>";

// Check 2: WebP images in posts table
echo "<div class='section'>";
echo "<h2>2. WebP Images in Posts Table</h2>";

$webp_posts = $wpdb->get_results("
    SELECT ID, post_title, post_mime_type, post_date
    FROM {$wpdb->posts} 
    WHERE post_type = 'attachment' 
    AND post_mime_type = 'image/webp'
    LIMIT 5
");

if ($webp_posts) {
    echo "<p class='success'>✅ Found " . count($webp_posts) . " WebP images in posts table</p>";
    foreach ($webp_posts as $post) {
        echo "<div class='data'>";
        echo "<p><strong>ID:</strong> {$post->ID}</p>";
        echo "<p><strong>Title:</strong> {$post->post_title}</p>";
        echo "<p><strong>MIME Type:</strong> {$post->post_mime_type}</p>";
        echo "<p><strong>Date:</strong> {$post->post_date}</p>";
        
        // Get attached file
        $attached_file = get_attached_file($post->ID);
        echo "<p><strong>Attached file:</strong> {$attached_file}</p>";
        echo "<p><strong>File exists:</strong> " . (file_exists($attached_file) ? "Yes" : "No") . "</p>";
        echo "</div>";
    }
} else {
    echo "<p class='warning'>⚠️ No WebP images found in posts table</p>";
}
echo "</div>";

// Check 3: Attachment metadata
echo "<div class='section'>";
echo "<h2>3. Attachment Metadata Check</h2>";

if ($webp_posts) {
    $first_webp = $webp_posts[0];
    $metadata = wp_get_attachment_metadata($first_webp->ID);
    
    echo "<div class='data'>";
    echo "<h4>Sample attachment metadata for ID: {$first_webp->ID}</h4>";
    echo "<pre>" . htmlspecialchars(print_r($metadata, true)) . "</pre>";
    echo "</div>";
}
echo "</div>";

// Check 4: Generate recovery SQL
echo "<div class='section'>";
echo "<h2>4. Generated Recovery SQL</h2>";

if ($webp_meta_count > 0) {
    echo "<p class='success'>✅ Based on the data found, here are the correct SQL queries for your setup:</p>";
    
    echo "<div class='data'>";
    echo "<h4>Step 1: Identify converted images</h4>";
    echo "<pre>";
    echo "SELECT \n";
    echo "    pm.post_id,\n";
    echo "    p.post_title,\n";
    echo "    p.post_mime_type as current_mime_type\n";
    echo "FROM wp_postmeta pm\n";
    echo "INNER JOIN wp_posts p ON pm.post_id = p.ID\n";
    echo "WHERE pm.meta_key = '_webp_conversion_data'\n";
    echo "AND p.post_type = 'attachment'\n";
    echo "ORDER BY pm.post_id;";
    echo "</pre>";
    echo "</div>";
    
    echo "<div class='data'>";
    echo "<h4>Step 2: Check for original format information</h4>";
    echo "<pre>";
    echo "SELECT \n";
    echo "    pm.post_id,\n";
    echo "    p.post_title,\n";
    echo "    CASE \n";
    echo "        WHEN pm.meta_value LIKE '%.jpg%' OR pm.meta_value LIKE '%.jpeg%' THEN 'image/jpeg'\n";
    echo "        WHEN pm.meta_value LIKE '%.png%' THEN 'image/png'\n";
    echo "        WHEN pm.meta_value LIKE '%.gif%' THEN 'image/gif'\n";
    echo "        ELSE 'unknown'\n";
    echo "    END as detected_original_format,\n";
    echo "    LEFT(pm.meta_value, 200) as sample_data\n";
    echo "FROM wp_postmeta pm\n";
    echo "INNER JOIN wp_posts p ON pm.post_id = p.ID\n";
    echo "WHERE pm.meta_key = '_webp_conversion_data'\n";
    echo "AND p.post_type = 'attachment'\n";
    echo "ORDER BY pm.post_id;";
    echo "</pre>";
    echo "</div>";
    
} else {
    echo "<p class='error'>❌ Cannot generate recovery SQL - no WebP conversion data found</p>";
    echo "<p>This could mean:</p>";
    echo "<ul>";
    echo "<li>WebP conversion was never performed</li>";
    echo "<li>Conversion data was already cleaned up</li>";
    echo "<li>Different plugin was used for WebP conversion</li>";
    echo "<li>Manual WebP conversion was done</li>";
    echo "</ul>";
}
echo "</div>";

// Check 5: Redco Optimizer settings
echo "<div class='section'>";
echo "<h2>5. Redco Optimizer Settings</h2>";

$redco_options = $wpdb->get_results("
    SELECT option_name, option_value 
    FROM {$wpdb->options} 
    WHERE option_name LIKE '%redco%'
");

if ($redco_options) {
    echo "<p class='success'>✅ Found Redco Optimizer settings</p>";
    foreach ($redco_options as $option) {
        echo "<div class='data'>";
        echo "<p><strong>{$option->option_name}:</strong></p>";
        $value = maybe_unserialize($option->option_value);
        if (is_array($value)) {
            echo "<pre>" . htmlspecialchars(print_r($value, true)) . "</pre>";
        } else {
            echo "<pre>" . htmlspecialchars(substr($option->option_value, 0, 300)) . "</pre>";
        }
        echo "</div>";
    }
} else {
    echo "<p class='warning'>⚠️ No Redco Optimizer settings found</p>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>6. Next Steps</h2>";
if ($webp_meta_count > 0) {
    echo "<p class='success'>✅ Recovery is possible! You have WebP conversion data.</p>";
    echo "<ol>";
    echo "<li>Use the generated SQL queries above to identify your converted images</li>";
    echo "<li>Run the webp-file-recovery.php script to convert WebP files back to originals</li>";
    echo "<li>Update the database references using the recovery SQL script</li>";
    echo "</ol>";
} else {
    echo "<p class='warning'>⚠️ No WebP conversion data found.</p>";
    echo "<p>Try these alternative approaches:</p>";
    echo "<ol>";
    echo "<li>Check if original files still exist alongside WebP files</li>";
    echo "<li>Look for backup directories in your uploads folder</li>";
    echo "<li>Check if another WebP plugin was used</li>";
    echo "<li>Restore from a backup if available</li>";
    echo "</ol>";
}
echo "</div>";
?>
