<?php
/**
 * Diagnostic & Auto-Fix Module Settings Page
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Handle form submission
if (isset($_POST['submit']) && wp_verify_nonce($_POST['redco_nonce'], 'redco_optimizer_nonce')) {
    $settings = array(
        'auto_scan_frequency' => sanitize_text_field($_POST['settings']['auto_scan_frequency'] ?? 'weekly'),
        'auto_fix_enabled' => isset($_POST['settings']['auto_fix_enabled']) ? 1 : 0,
        'backup_before_fix' => isset($_POST['settings']['backup_before_fix']) ? 1 : 0,
        'emergency_mode_threshold' => intval($_POST['settings']['emergency_mode_threshold'] ?? 40),
        'scan_types' => isset($_POST['settings']['scan_types']) ? array_map('sanitize_text_field', $_POST['settings']['scan_types']) : array(),
        'pagespeed_api_key' => sanitize_text_field($_POST['settings']['pagespeed_api_key'] ?? ''),
        'notification_email' => sanitize_email($_POST['settings']['notification_email'] ?? get_option('admin_email')),
        'max_fix_batch_size' => intval($_POST['settings']['max_fix_batch_size'] ?? 5),

    );

    redco_update_module_option('diagnostic-autofix', 'settings', $settings);
    redco_add_admin_notice(__('Diagnostic & Auto-Fix settings saved successfully!', 'redco-optimizer'), 'success');
}

// Get current settings
$auto_scan_frequency = redco_get_module_option('diagnostic-autofix', 'auto_scan_frequency', 'weekly');
$auto_fix_enabled = redco_get_module_option('diagnostic-autofix', 'auto_fix_enabled', false);
$backup_before_fix = redco_get_module_option('diagnostic-autofix', 'backup_before_fix', true);
$emergency_mode_threshold = redco_get_module_option('diagnostic-autofix', 'emergency_mode_threshold', 40);
$scan_types = redco_get_module_option('diagnostic-autofix', 'scan_types', array('wordpress', 'database', 'frontend', 'security'));
$pagespeed_api_key = redco_get_module_option('diagnostic-autofix', 'pagespeed_api_key', '');
$notification_email = redco_get_module_option('diagnostic-autofix', 'notification_email', get_option('admin_email'));

$max_fix_batch_size = redco_get_module_option('diagnostic-autofix', 'max_fix_batch_size', 5);
?>

<div class="redco-module-settings">
    <form method="post" action="">
        <?php wp_nonce_field('redco_optimizer_nonce', 'redco_nonce'); ?>

        <div class="redco-settings-section">
            <h3>
                <?php _e('Scan Configuration', 'redco-optimizer'); ?>
                <?php echo redco_help_icon('diagnostic-autofix', 'scan', '', 'Configure how and when diagnostic scans are performed'); ?>
            </h3>

            <div class="redco-form-row">
                <label for="auto_scan_frequency">
                    <?php _e('Automatic Scan Frequency', 'redco-optimizer'); ?>
                    <?php echo redco_help_icon('diagnostic-autofix', 'scan', 'frequency', 'How often to automatically scan for performance issues'); ?>
                </label>
                <div class="form-control">
                    <select name="settings[auto_scan_frequency]" id="auto_scan_frequency">
                        <option value="disabled" <?php selected($auto_scan_frequency, 'disabled'); ?>><?php _e('Disabled', 'redco-optimizer'); ?></option>
                        <option value="daily" <?php selected($auto_scan_frequency, 'daily'); ?>><?php _e('Daily', 'redco-optimizer'); ?></option>
                        <option value="weekly" <?php selected($auto_scan_frequency, 'weekly'); ?>><?php _e('Weekly', 'redco-optimizer'); ?></option>
                        <option value="monthly" <?php selected($auto_scan_frequency, 'monthly'); ?>><?php _e('Monthly', 'redco-optimizer'); ?></option>
                    </select>
                    <div class="description">
                        <?php _e('Recommended: Weekly for most sites, Daily for high-traffic sites.', 'redco-optimizer'); ?>
                    </div>
                </div>
            </div>

            <div class="redco-form-row">
                <label for="scan_types">
                    <?php _e('Scan Categories', 'redco-optimizer'); ?>
                    <?php echo redco_help_icon('diagnostic-autofix', 'scan', 'types', 'Select which types of issues to scan for'); ?>
                </label>
                <div class="form-control">
                    <div class="checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="settings[scan_types][]" value="wordpress" <?php checked(in_array('wordpress', $scan_types)); ?>>
                            <span><?php _e('WordPress Core & Plugins', 'redco-optimizer'); ?></span>
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" name="settings[scan_types][]" value="database" <?php checked(in_array('database', $scan_types)); ?>>
                            <span><?php _e('Database Performance', 'redco-optimizer'); ?></span>
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" name="settings[scan_types][]" value="frontend" <?php checked(in_array('frontend', $scan_types)); ?>>
                            <span><?php _e('Frontend Performance', 'redco-optimizer'); ?></span>
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" name="settings[scan_types][]" value="server" <?php checked(in_array('server', $scan_types)); ?>>
                            <span><?php _e('Server Configuration', 'redco-optimizer'); ?></span>
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" name="settings[scan_types][]" value="security" <?php checked(in_array('security', $scan_types)); ?>>
                            <span><?php _e('Security & Performance', 'redco-optimizer'); ?></span>
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" name="settings[scan_types][]" value="modules" <?php checked(in_array('modules', $scan_types)); ?>>
                            <span><?php _e('Redco Optimizer Modules', 'redco-optimizer'); ?></span>
                        </label>
                    </div>
                    <div class="description">
                        <?php _e('Select all categories for comprehensive scanning. Uncheck categories you want to skip.', 'redco-optimizer'); ?>
                    </div>
                </div>
            </div>

            <div class="redco-form-row">
                <label for="pagespeed_api_key">
                    <?php _e('PageSpeed Insights API Key', 'redco-optimizer'); ?>
                    <?php echo redco_help_icon('diagnostic-autofix', 'scan', 'pagespeed', 'Optional: Add PageSpeed Insights API key for comprehensive performance analysis'); ?>
                </label>
                <div class="form-control">
                    <input type="text" name="settings[pagespeed_api_key]" id="pagespeed_api_key" value="<?php echo esc_attr($pagespeed_api_key); ?>" placeholder="<?php _e('Enter PageSpeed Insights API Key (Optional)', 'redco-optimizer'); ?>">
                    <div class="description">
                        <?php _e('🔑 <strong>Optional:</strong> Get your free API key from <a href="https://developers.google.com/speed/docs/insights/v5/get-started" target="_blank">Google PageSpeed Insights API</a>. When configured, scans will include PageSpeed-specific recommendations.', 'redco-optimizer'); ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="redco-settings-section">
            <h3>
                <?php _e('Auto-Fix Configuration', 'redco-optimizer'); ?>
                <?php echo redco_help_icon('diagnostic-autofix', 'autofix', '', 'Configure automatic issue resolution'); ?>
            </h3>

            <div class="redco-form-row">
                <label for="auto_fix_enabled">
                    <?php _e('Enable Auto-Fix', 'redco-optimizer'); ?>
                    <?php echo redco_help_icon('diagnostic-autofix', 'autofix', 'enabled', 'Automatically fix detected issues that can be safely resolved'); ?>
                </label>
                <div class="form-control">
                    <label class="toggle-switch">
                        <input type="hidden" name="settings[auto_fix_enabled]" value="0">
                        <input type="checkbox" name="settings[auto_fix_enabled]" id="auto_fix_enabled" value="1" <?php checked($auto_fix_enabled); ?>>
                        <span class="toggle-slider"></span>
                    </label>
                    <div class="description">
                        <?php _e('⚠️ <strong>Use with caution:</strong> Auto-fix will automatically resolve detected issues. Always create backups first.', 'redco-optimizer'); ?>
                    </div>
                </div>
            </div>

            <div class="redco-form-row">
                <label for="backup_before_fix">
                    <?php _e('Create Backup Before Fixes', 'redco-optimizer'); ?>
                    <?php echo redco_help_icon('diagnostic-autofix', 'autofix', 'backup', 'Automatically create backups before applying fixes'); ?>
                </label>
                <div class="form-control">
                    <label class="toggle-switch">
                        <input type="hidden" name="settings[backup_before_fix]" value="0">
                        <input type="checkbox" name="settings[backup_before_fix]" id="backup_before_fix" value="1" <?php checked($backup_before_fix); ?>>
                        <span class="toggle-slider"></span>
                    </label>
                    <div class="description">
                        <?php _e('✅ <strong>Highly recommended:</strong> Creates backups of critical files and settings before applying fixes.', 'redco-optimizer'); ?>
                    </div>
                </div>
            </div>

            <div class="redco-form-row">
                <label for="max_fix_batch_size">
                    <?php _e('Maximum Fixes Per Batch', 'redco-optimizer'); ?>
                    <?php echo redco_help_icon('diagnostic-autofix', 'autofix', 'batch_size', 'Maximum number of fixes to apply in a single operation'); ?>
                </label>
                <div class="form-control">
                    <input type="number" name="settings[max_fix_batch_size]" id="max_fix_batch_size" value="<?php echo esc_attr($max_fix_batch_size); ?>" min="1" max="20">
                    <div class="description">
                        <?php _e('Recommended: 5-10 fixes per batch to prevent timeouts and allow for testing.', 'redco-optimizer'); ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="redco-settings-section">
            <h3>
                <?php _e('Emergency Mode', 'redco-optimizer'); ?>
                <?php echo redco_help_icon('diagnostic-autofix', 'emergency', '', 'Automatic emergency performance mode when critical issues are detected'); ?>
            </h3>

            <div class="redco-form-row">
                <label for="emergency_mode_threshold">
                    <?php _e('Emergency Mode Threshold', 'redco-optimizer'); ?>
                    <?php echo redco_help_icon('diagnostic-autofix', 'emergency', 'threshold', 'PageSpeed score threshold that triggers emergency mode'); ?>
                </label>
                <div class="form-control">
                    <input type="number" name="settings[emergency_mode_threshold]" id="emergency_mode_threshold" value="<?php echo esc_attr($emergency_mode_threshold); ?>" min="10" max="90">
                    <div class="description">
                        <?php _e('🚨 When PageSpeed score drops below this threshold, emergency mode will be triggered to disable heavy features and optimize for speed.', 'redco-optimizer'); ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="redco-settings-section">
            <h3>
                <?php _e('Notifications', 'redco-optimizer'); ?>
                <?php echo redco_help_icon('diagnostic-autofix', 'notifications', '', 'Configure email notifications for diagnostic results'); ?>
            </h3>

            <div class="redco-form-row">
                <label for="notification_email">
                    <?php _e('Notification Email', 'redco-optimizer'); ?>
                    <?php echo redco_help_icon('diagnostic-autofix', 'notifications', 'email', 'Email address to receive diagnostic reports and alerts'); ?>
                </label>
                <div class="form-control">
                    <input type="email" name="settings[notification_email]" id="notification_email" value="<?php echo esc_attr($notification_email); ?>" placeholder="<?php _e('<EMAIL>', 'redco-optimizer'); ?>">
                    <div class="description">
                        <?php _e('Receive email notifications when critical issues are detected or when emergency mode is triggered.', 'redco-optimizer'); ?>
                    </div>
                </div>
            </div>


        </div>

        <div class="redco-settings-section">
            <h3>
                <?php _e('Quick Actions', 'redco-optimizer'); ?>
                <?php echo redco_help_icon('diagnostic-autofix', 'actions', '', 'Immediate diagnostic and fix actions'); ?>
            </h3>

            <div class="redco-form-row">
                <div class="form-control">
                    <div class="quick-actions-grid">
                        <button type="button" class="button button-primary" id="run-quick-scan">
                            <span class="dashicons dashicons-search"></span>
                            <?php _e('Run Quick Scan', 'redco-optimizer'); ?>
                        </button>

                        <button type="button" class="button button-secondary" id="run-comprehensive-scan">
                            <span class="dashicons dashicons-analytics"></span>
                            <?php _e('Comprehensive Scan', 'redco-optimizer'); ?>
                        </button>

                        <button type="button" class="button button-secondary" id="run-wordpress-scan">
                            <span class="dashicons dashicons-wordpress"></span>
                            <?php _e('WordPress-Only Scan', 'redco-optimizer'); ?>
                        </button>

                        <button type="button" class="button button-secondary" id="apply-auto-fixes" disabled>
                            <span class="dashicons dashicons-admin-tools"></span>
                            <?php _e('Apply Auto-Fixes', 'redco-optimizer'); ?>
                        </button>

                        <button type="button" class="button button-secondary" id="emergency-mode-toggle">
                            <span class="dashicons dashicons-warning"></span>
                            <?php _e('Emergency Mode', 'redco-optimizer'); ?>
                        </button>

                        <button type="button" class="button button-secondary" id="export-report">
                            <span class="dashicons dashicons-download"></span>
                            <?php _e('Export Report', 'redco-optimizer'); ?>
                        </button>
                    </div>
                    <div class="description">
                        <?php _e('Use these quick actions to immediately scan for issues, apply fixes, or export diagnostic reports.', 'redco-optimizer'); ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-actions">
            <input type="submit" name="submit" class="button button-primary" value="<?php _e('Save Settings', 'redco-optimizer'); ?>">
        </div>
    </form>
</div>

<style>
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin-bottom: 10px;
}

.quick-actions-grid .button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 10px 15px;
    height: auto;
}

.checkbox-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Quick scan actions
    $('#run-quick-scan').on('click', function() {
        runDiagnosticScan('quick');
    });

    $('#run-comprehensive-scan').on('click', function() {
        runDiagnosticScan('comprehensive');
    });

    $('#run-wordpress-scan').on('click', function() {
        runDiagnosticScan('wordpress');
    });

    function runDiagnosticScan(scanType) {
        const button = $(`#run-${scanType}-scan`);
        const originalText = button.html();

        button.prop('disabled', true).html('<span class="dashicons dashicons-update spin"></span> Scanning...');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_run_diagnostic_scan',
                scan_type: scanType,
                include_pagespeed: $('#pagespeed_api_key').val() !== '',
                nonce: '<?php echo wp_create_nonce('redco_optimizer_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    showScanResults(response.data);
                    if (response.data.auto_fixable > 0) {
                        $('#apply-auto-fixes').prop('disabled', false);
                    }
                } else {
                    if (typeof showToast === 'function') {
                        showToast('Scan failed: ' + response.data, 'error');
                    } else {
                        console.error('Scan failed: ' + response.data);
                    }
                }
            },
            error: function() {
                if (typeof showToast === 'function') {
                    showToast('Scan failed due to network error', 'error');
                } else {
                    console.error('Scan failed due to network error');
                }
            },
            complete: function() {
                button.prop('disabled', false).html(originalText);
            }
        });
    }

    function showScanResults(data) {
        // Implementation for showing scan results modal
    }
});
</script>
