<?php
/**
 * Diagnostic & Auto-Fix Module Settings Page
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Handle form submission
if (isset($_POST['submit']) && wp_verify_nonce($_POST['redco_nonce'], 'redco_optimizer_nonce')) {
    $settings = array(
        'auto_scan_frequency' => sanitize_text_field($_POST['settings']['auto_scan_frequency'] ?? 'weekly'),
        'auto_fix_enabled' => isset($_POST['settings']['auto_fix_enabled']) ? 1 : 0,
        'backup_before_fix' => isset($_POST['settings']['backup_before_fix']) ? 1 : 0,
        'emergency_mode_threshold' => intval($_POST['settings']['emergency_mode_threshold'] ?? 40),
        'scan_types' => isset($_POST['settings']['scan_types']) ? array_map('sanitize_text_field', $_POST['settings']['scan_types']) : array(),
        'pagespeed_api_key' => sanitize_text_field($_POST['settings']['pagespeed_api_key'] ?? ''),
        'notification_email' => sanitize_email($_POST['settings']['notification_email'] ?? get_option('admin_email')),
        'max_fix_batch_size' => intval($_POST['settings']['max_fix_batch_size'] ?? 5),

    );

    redco_update_module_option('diagnostic-autofix', 'settings', $settings);
    redco_add_admin_notice(__('Diagnostic & Auto-Fix settings saved successfully!', 'redco-optimizer'), 'success');
}

// PERFORMANCE OPTIMIZED: Get current settings with single database call
$all_options = get_option('redco_optimizer_modules', array());
$module_settings = isset($all_options['diagnostic-autofix']['settings'])
    ? $all_options['diagnostic-autofix']['settings']
    : array();

// Extract settings with defaults
$auto_scan_frequency = isset($module_settings['auto_scan_frequency']) ? $module_settings['auto_scan_frequency'] : 'weekly';
$auto_fix_enabled = isset($module_settings['auto_fix_enabled']) ? $module_settings['auto_fix_enabled'] : false;
$backup_before_fix = isset($module_settings['backup_before_fix']) ? $module_settings['backup_before_fix'] : true;
$emergency_mode_threshold = isset($module_settings['emergency_mode_threshold']) ? $module_settings['emergency_mode_threshold'] : 40;
$scan_types = isset($module_settings['scan_types']) ? $module_settings['scan_types'] : array('wordpress', 'database', 'frontend', 'security');
$pagespeed_api_key = isset($module_settings['pagespeed_api_key']) ? $module_settings['pagespeed_api_key'] : '';
$notification_email = isset($module_settings['notification_email']) ? $module_settings['notification_email'] : get_option('admin_email');
$max_fix_batch_size = isset($module_settings['max_fix_batch_size']) ? $module_settings['max_fix_batch_size'] : 5;

// Load performance dashboard helper functions
if (!function_exists('get_load_time_grade')) {
    function get_load_time_grade($load_time_ms) {
        $seconds = $load_time_ms / 1000;
        if ($seconds < 2) return 'A';
        if ($seconds < 3) return 'B';
        if ($seconds < 5) return 'C';
        if ($seconds < 8) return 'D';
        return 'F';
    }
}

if (!function_exists('get_cache_grade')) {
    function get_cache_grade($hit_ratio) {
        if ($hit_ratio >= 0.9) return 'A';
        if ($hit_ratio >= 0.8) return 'B';
        if ($hit_ratio >= 0.6) return 'C';
        if ($hit_ratio >= 0.4) return 'D';
        return 'F';
    }
}

if (!function_exists('get_ajax_grade')) {
    function get_ajax_grade($requests) {
        if ($requests <= 2) return 'A';
        if ($requests <= 4) return 'B';
        if ($requests <= 6) return 'C';
        if ($requests <= 10) return 'D';
        return 'F';
    }
}

if (!function_exists('get_error_grade')) {
    function get_error_grade($errors) {
        if ($errors == 0) return 'A';
        if ($errors <= 1) return 'B';
        if ($errors <= 2) return 'C';
        if ($errors <= 5) return 'D';
        return 'F';
    }
}

if (!function_exists('generate_performance_insights')) {
    function generate_performance_insights($latest, $history) {
        $insights = array();

        if (!$latest) return $insights;

        // Load time insight
        $load_time_seconds = $latest['avg_load_time'] / 1000;
        if ($load_time_seconds < 2) {
            $insights[] = array(
                'type' => 'success',
                'icon' => 'yes-alt',
                'title' => 'Excellent Load Time',
                'description' => 'Your diagnostic module loads in under 2 seconds - optimal performance!'
            );
        } elseif ($load_time_seconds > 5) {
            $insights[] = array(
                'type' => 'error',
                'icon' => 'warning',
                'title' => 'Slow Load Time Detected',
                'description' => 'Load time exceeds 5 seconds. Consider optimizing database queries or enabling caching.',
                'action' => 'optimizePerformance()',
                'action_text' => 'Optimize Now'
            );
        }

        // Cache insight
        if ($latest['avg_cache_hit_ratio'] < 0.6) {
            $insights[] = array(
                'type' => 'warning',
                'icon' => 'database-view',
                'title' => 'Low Cache Efficiency',
                'description' => 'Cache hit ratio is below 60%. Consider increasing cache duration or improving cache strategies.',
                'action' => 'optimizeCache()',
                'action_text' => 'Optimize Cache'
            );
        }

        // Error insight
        if ($latest['total_errors'] > 0) {
            $insights[] = array(
                'type' => 'error',
                'icon' => 'warning',
                'title' => 'JavaScript Errors Detected',
                'description' => "Found {$latest['total_errors']} JavaScript errors. Check browser console for details.",
                'action' => 'viewErrorLog()',
                'action_text' => 'View Errors'
            );
        }

        return $insights;
    }
}
?>

<div class="redco-module-settings">

    <!-- PERFORMANCE DASHBOARD SECTION -->
    <?php
    // Include the performance dashboard
    if (class_exists('Redco_Diagnostic_Performance_Tracker')) {
        include __DIR__ . '/performance-dashboard.php';
    }
    ?>

    <form method="post" action="">
        <?php wp_nonce_field('redco_optimizer_nonce', 'redco_nonce'); ?>

        <div class="redco-settings-section">
            <h3>
                <?php _e('Scan Configuration', 'redco-optimizer'); ?>
                <?php echo redco_help_icon('diagnostic-autofix', 'scan', '', 'Configure how and when diagnostic scans are performed'); ?>
            </h3>

            <div class="redco-form-row">
                <label for="auto_scan_frequency">
                    <?php _e('Automatic Scan Frequency', 'redco-optimizer'); ?>
                    <?php echo redco_help_icon('diagnostic-autofix', 'scan', 'frequency', 'How often to automatically scan for performance issues'); ?>
                </label>
                <div class="form-control">
                    <select name="settings[auto_scan_frequency]" id="auto_scan_frequency">
                        <option value="disabled" <?php selected($auto_scan_frequency, 'disabled'); ?>><?php _e('Disabled', 'redco-optimizer'); ?></option>
                        <option value="daily" <?php selected($auto_scan_frequency, 'daily'); ?>><?php _e('Daily', 'redco-optimizer'); ?></option>
                        <option value="weekly" <?php selected($auto_scan_frequency, 'weekly'); ?>><?php _e('Weekly', 'redco-optimizer'); ?></option>
                        <option value="monthly" <?php selected($auto_scan_frequency, 'monthly'); ?>><?php _e('Monthly', 'redco-optimizer'); ?></option>
                    </select>
                    <div class="description">
                        <?php _e('Recommended: Weekly for most sites, Daily for high-traffic sites.', 'redco-optimizer'); ?>
                    </div>
                </div>
            </div>

            <div class="redco-form-row">
                <label for="scan_types">
                    <?php _e('Scan Categories', 'redco-optimizer'); ?>
                    <?php echo redco_help_icon('diagnostic-autofix', 'scan', 'types', 'Select which types of issues to scan for'); ?>
                </label>
                <div class="form-control">
                    <div class="checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="settings[scan_types][]" value="wordpress" <?php checked(in_array('wordpress', $scan_types)); ?>>
                            <span><?php _e('WordPress Core & Plugins', 'redco-optimizer'); ?></span>
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" name="settings[scan_types][]" value="database" <?php checked(in_array('database', $scan_types)); ?>>
                            <span><?php _e('Database Performance', 'redco-optimizer'); ?></span>
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" name="settings[scan_types][]" value="frontend" <?php checked(in_array('frontend', $scan_types)); ?>>
                            <span><?php _e('Frontend Performance', 'redco-optimizer'); ?></span>
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" name="settings[scan_types][]" value="server" <?php checked(in_array('server', $scan_types)); ?>>
                            <span><?php _e('Server Configuration', 'redco-optimizer'); ?></span>
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" name="settings[scan_types][]" value="security" <?php checked(in_array('security', $scan_types)); ?>>
                            <span><?php _e('Security & Performance', 'redco-optimizer'); ?></span>
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" name="settings[scan_types][]" value="modules" <?php checked(in_array('modules', $scan_types)); ?>>
                            <span><?php _e('Redco Optimizer Modules', 'redco-optimizer'); ?></span>
                        </label>
                    </div>
                    <div class="description">
                        <?php _e('Select all categories for comprehensive scanning. Uncheck categories you want to skip.', 'redco-optimizer'); ?>
                    </div>
                </div>
            </div>

            <div class="redco-form-row">
                <label for="pagespeed_api_key">
                    <?php _e('PageSpeed Insights API Key', 'redco-optimizer'); ?>
                    <?php echo redco_help_icon('diagnostic-autofix', 'scan', 'pagespeed', 'Optional: Add PageSpeed Insights API key for comprehensive performance analysis'); ?>
                </label>
                <div class="form-control">
                    <input type="text" name="settings[pagespeed_api_key]" id="pagespeed_api_key" value="<?php echo esc_attr($pagespeed_api_key); ?>" placeholder="<?php _e('Enter PageSpeed Insights API Key (Optional)', 'redco-optimizer'); ?>">
                    <div class="description">
                        <?php _e('🔑 <strong>Optional:</strong> Get your free API key from <a href="https://developers.google.com/speed/docs/insights/v5/get-started" target="_blank">Google PageSpeed Insights API</a>. When configured, scans will include PageSpeed-specific recommendations.', 'redco-optimizer'); ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="redco-settings-section">
            <h3>
                <?php _e('Auto-Fix Configuration', 'redco-optimizer'); ?>
                <?php echo redco_help_icon('diagnostic-autofix', 'autofix', '', 'Configure automatic issue resolution'); ?>
            </h3>

            <div class="redco-form-row">
                <label for="auto_fix_enabled">
                    <?php _e('Enable Auto-Fix', 'redco-optimizer'); ?>
                    <?php echo redco_help_icon('diagnostic-autofix', 'autofix', 'enabled', 'Automatically fix detected issues that can be safely resolved'); ?>
                </label>
                <div class="form-control">
                    <label class="toggle-switch">
                        <input type="hidden" name="settings[auto_fix_enabled]" value="0">
                        <input type="checkbox" name="settings[auto_fix_enabled]" id="auto_fix_enabled" value="1" <?php checked($auto_fix_enabled); ?>>
                        <span class="toggle-slider"></span>
                    </label>
                    <div class="description">
                        <?php _e('⚠️ <strong>Use with caution:</strong> Auto-fix will automatically resolve detected issues. Always create backups first.', 'redco-optimizer'); ?>
                    </div>
                </div>
            </div>

            <div class="redco-form-row">
                <label for="backup_before_fix">
                    <?php _e('Create Backup Before Fixes', 'redco-optimizer'); ?>
                    <?php echo redco_help_icon('diagnostic-autofix', 'autofix', 'backup', 'Automatically create backups before applying fixes'); ?>
                </label>
                <div class="form-control">
                    <label class="toggle-switch">
                        <input type="hidden" name="settings[backup_before_fix]" value="0">
                        <input type="checkbox" name="settings[backup_before_fix]" id="backup_before_fix" value="1" <?php checked($backup_before_fix); ?>>
                        <span class="toggle-slider"></span>
                    </label>
                    <div class="description">
                        <?php _e('✅ <strong>Highly recommended:</strong> Creates backups of critical files and settings before applying fixes.', 'redco-optimizer'); ?>
                    </div>
                </div>
            </div>

            <div class="redco-form-row">
                <label for="max_fix_batch_size">
                    <?php _e('Maximum Fixes Per Batch', 'redco-optimizer'); ?>
                    <?php echo redco_help_icon('diagnostic-autofix', 'autofix', 'batch_size', 'Maximum number of fixes to apply in a single operation'); ?>
                </label>
                <div class="form-control">
                    <input type="number" name="settings[max_fix_batch_size]" id="max_fix_batch_size" value="<?php echo esc_attr($max_fix_batch_size); ?>" min="1" max="20">
                    <div class="description">
                        <?php _e('Recommended: 5-10 fixes per batch to prevent timeouts and allow for testing.', 'redco-optimizer'); ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="redco-settings-section">
            <h3>
                <?php _e('Emergency Mode', 'redco-optimizer'); ?>
                <?php echo redco_help_icon('diagnostic-autofix', 'emergency', '', 'Automatic emergency performance mode when critical issues are detected'); ?>
            </h3>

            <div class="redco-form-row">
                <label for="emergency_mode_threshold">
                    <?php _e('Emergency Mode Threshold', 'redco-optimizer'); ?>
                    <?php echo redco_help_icon('diagnostic-autofix', 'emergency', 'threshold', 'PageSpeed score threshold that triggers emergency mode'); ?>
                </label>
                <div class="form-control">
                    <input type="number" name="settings[emergency_mode_threshold]" id="emergency_mode_threshold" value="<?php echo esc_attr($emergency_mode_threshold); ?>" min="10" max="90">
                    <div class="description">
                        <?php _e('🚨 When PageSpeed score drops below this threshold, emergency mode will be triggered to disable heavy features and optimize for speed.', 'redco-optimizer'); ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="redco-settings-section">
            <h3>
                <?php _e('Notifications', 'redco-optimizer'); ?>
                <?php echo redco_help_icon('diagnostic-autofix', 'notifications', '', 'Configure email notifications for diagnostic results'); ?>
            </h3>

            <div class="redco-form-row">
                <label for="notification_email">
                    <?php _e('Notification Email', 'redco-optimizer'); ?>
                    <?php echo redco_help_icon('diagnostic-autofix', 'notifications', 'email', 'Email address to receive diagnostic reports and alerts'); ?>
                </label>
                <div class="form-control">
                    <input type="email" name="settings[notification_email]" id="notification_email" value="<?php echo esc_attr($notification_email); ?>" placeholder="<?php _e('<EMAIL>', 'redco-optimizer'); ?>">
                    <div class="description">
                        <?php _e('Receive email notifications when critical issues are detected or when emergency mode is triggered.', 'redco-optimizer'); ?>
                    </div>
                </div>
            </div>


        </div>

        <div class="redco-settings-section">
            <h3>
                <?php _e('Quick Actions', 'redco-optimizer'); ?>
                <?php echo redco_help_icon('diagnostic-autofix', 'actions', '', 'Immediate diagnostic and fix actions'); ?>
            </h3>

            <div class="redco-form-row">
                <div class="form-control">
                    <div class="quick-actions-grid">
                        <button type="button" class="button button-primary" id="run-quick-scan">
                            <span class="dashicons dashicons-search"></span>
                            <?php _e('Run Quick Scan', 'redco-optimizer'); ?>
                        </button>

                        <button type="button" class="button button-secondary" id="run-comprehensive-scan">
                            <span class="dashicons dashicons-analytics"></span>
                            <?php _e('Comprehensive Scan', 'redco-optimizer'); ?>
                        </button>

                        <button type="button" class="button button-secondary" id="run-wordpress-scan">
                            <span class="dashicons dashicons-wordpress"></span>
                            <?php _e('WordPress-Only Scan', 'redco-optimizer'); ?>
                        </button>

                        <button type="button" class="button button-secondary" id="apply-auto-fixes" disabled>
                            <span class="dashicons dashicons-admin-tools"></span>
                            <?php _e('Apply Auto-Fixes', 'redco-optimizer'); ?>
                        </button>

                        <button type="button" class="button button-secondary" id="emergency-mode-toggle">
                            <span class="dashicons dashicons-warning"></span>
                            <?php _e('Emergency Mode', 'redco-optimizer'); ?>
                        </button>

                        <button type="button" class="button button-secondary" id="export-report">
                            <span class="dashicons dashicons-download"></span>
                            <?php _e('Export Report', 'redco-optimizer'); ?>
                        </button>

                        <button type="button" class="button button-secondary redco-feedback-trigger">
                            <span class="dashicons dashicons-feedback"></span>
                            <?php _e('Give Feedback', 'redco-optimizer'); ?>
                        </button>
                    </div>
                    <div class="description">
                        <?php _e('Use these quick actions to immediately scan for issues, apply fixes, or export diagnostic reports.', 'redco-optimizer'); ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-actions">
            <input type="submit" name="submit" class="button button-primary" value="<?php _e('Save Settings', 'redco-optimizer'); ?>">
        </div>
    </form>

    <!-- OPTIMIZATION OPPORTUNITIES SECTION -->
    <div class="redco-settings-section">
        <h3>
            <span class="dashicons dashicons-lightbulb"></span>
            <?php _e('Optimization Opportunities', 'redco-optimizer'); ?>
            <button type="button" class="button button-small" id="refresh-opportunities" style="margin-left: 10px;">
                <span class="dashicons dashicons-update"></span>
                <?php _e('Refresh', 'redco-optimizer'); ?>
            </button>
        </h3>

        <div id="optimization-opportunities-container">
            <div class="loading-placeholder">
                <div class="loading-spinner"></div>
                <p><?php _e('Analyzing optimization opportunities...', 'redco-optimizer'); ?></p>
            </div>
        </div>
    </div>

    <!-- USER FEEDBACK SECTION -->
    <div class="redco-settings-section">
        <h3>
            <span class="dashicons dashicons-groups"></span>
            <?php _e('User Feedback & Experience', 'redco-optimizer'); ?>
        </h3>

        <div id="user-feedback-summary">
            <div class="feedback-stats-grid">
                <div class="feedback-stat">
                    <div class="stat-value" id="total-feedback">-</div>
                    <div class="stat-label"><?php _e('Total Feedback', 'redco-optimizer'); ?></div>
                </div>
                <div class="feedback-stat">
                    <div class="stat-value" id="avg-rating">-</div>
                    <div class="stat-label"><?php _e('Avg Rating', 'redco-optimizer'); ?></div>
                </div>
                <div class="feedback-stat">
                    <div class="stat-value" id="satisfaction-rate">-</div>
                    <div class="stat-label"><?php _e('Satisfaction', 'redco-optimizer'); ?></div>
                </div>
            </div>

            <div class="feedback-actions">
                <button type="button" class="button redco-feedback-trigger">
                    <?php _e('Share Your Experience', 'redco-optimizer'); ?>
                </button>
                <button type="button" class="button" id="view-feedback-details">
                    <?php _e('View Feedback Details', 'redco-optimizer'); ?>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin-bottom: 10px;
}

.quick-actions-grid .button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 10px 15px;
    height: auto;
}

.checkbox-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
}

/* Performance Dashboard Styles */
.loading-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40px 20px;
    color: #666;
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Optimization Opportunities Styles */
#optimization-opportunities-container {
    background: #f9f9f9;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
    margin-top: 15px;
}

.opportunity-card {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
    border-left: 4px solid #4CAF50;
}

.opportunity-card.priority-critical {
    border-left-color: #dc3545;
}

.opportunity-card.priority-high {
    border-left-color: #fd7e14;
}

.opportunity-card.priority-medium {
    border-left-color: #ffc107;
}

.opportunity-card.priority-low {
    border-left-color: #6c757d;
}

.opportunity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.opportunity-title {
    font-weight: 600;
    color: #333;
}

.opportunity-priority {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.priority-critical { background: #f8d7da; color: #721c24; }
.priority-high { background: #fff3cd; color: #856404; }
.priority-medium { background: #d1ecf1; color: #0c5460; }
.priority-low { background: #e2e3e5; color: #383d41; }

.opportunity-description {
    color: #666;
    margin-bottom: 10px;
    font-size: 14px;
}

.opportunity-recommendations {
    margin-top: 10px;
}

.opportunity-recommendations ul {
    margin: 5px 0 0 20px;
    color: #666;
    font-size: 13px;
}

/* User Feedback Styles */
.feedback-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.feedback-stat {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    text-align: center;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #4CAF50;
    line-height: 1;
}

.stat-label {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
    font-weight: 500;
}

.feedback-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Quick scan actions
    $('#run-quick-scan').on('click', function() {
        runDiagnosticScan('quick');
    });

    $('#run-comprehensive-scan').on('click', function() {
        runDiagnosticScan('comprehensive');
    });

    $('#run-wordpress-scan').on('click', function() {
        runDiagnosticScan('wordpress');
    });

    function runDiagnosticScan(scanType) {
        const button = $(`#run-${scanType}-scan`);
        const originalText = button.html();

        button.prop('disabled', true).html('<span class="dashicons dashicons-update spin"></span> Scanning...');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_run_diagnostic_scan',
                scan_type: scanType,
                include_pagespeed: $('#pagespeed_api_key').val() !== '',
                nonce: '<?php echo wp_create_nonce('redco_optimizer_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    showScanResults(response.data);
                    if (response.data.auto_fixable > 0) {
                        $('#apply-auto-fixes').prop('disabled', false);
                    }
                } else {
                    if (typeof showToast === 'function') {
                        showToast('Scan failed: ' + response.data, 'error');
                    } else {
                        console.error('Scan failed: ' + response.data);
                    }
                }
            },
            error: function() {
                if (typeof showToast === 'function') {
                    showToast('Scan failed due to network error', 'error');
                } else {
                    console.error('Scan failed due to network error');
                }
            },
            complete: function() {
                button.prop('disabled', false).html(originalText);
            }
        });
    }

    function showScanResults(data) {
        // Implementation for showing scan results modal
    }

    // Load optimization opportunities on page load
    loadOptimizationOpportunities();
    loadUserFeedbackStats();

    // Refresh opportunities button
    $('#refresh-opportunities').on('click', function() {
        loadOptimizationOpportunities(true);
    });

    // View feedback details button
    $('#view-feedback-details').on('click', function() {
        // Implementation for showing detailed feedback modal
        alert('Detailed feedback view coming soon!');
    });

    function loadOptimizationOpportunities(forceRefresh = false) {
        const container = $('#optimization-opportunities-container');

        if (forceRefresh) {
            container.html('<div class="loading-placeholder"><div class="loading-spinner"></div><p>Analyzing optimization opportunities...</p></div>');
        }

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_get_optimization_opportunities',
                nonce: '<?php echo wp_create_nonce('redco_optimizer_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    displayOptimizationOpportunities(response.data.opportunities);
                } else {
                    container.html('<div class="error-message"><p>Failed to load optimization opportunities: ' + response.data.message + '</p></div>');
                }
            },
            error: function() {
                container.html('<div class="error-message"><p>Network error while loading optimization opportunities.</p></div>');
            }
        });
    }

    function displayOptimizationOpportunities(opportunities) {
        const container = $('#optimization-opportunities-container');

        if (!opportunities || opportunities.length === 0) {
            container.html('<div class="no-opportunities"><p>🎉 Great! No optimization opportunities found. Your diagnostic module is performing optimally.</p></div>');
            return;
        }

        let html = '<div class="opportunities-summary">';
        html += '<p><strong>' + opportunities.length + '</strong> optimization opportunities found:</p>';
        html += '</div>';

        opportunities.forEach(function(opportunity) {
            html += '<div class="opportunity-card priority-' + opportunity.priority + '">';
            html += '<div class="opportunity-header">';
            html += '<div class="opportunity-title">' + opportunity.title + '</div>';
            html += '<div class="opportunity-priority priority-' + opportunity.priority + '">' + opportunity.priority + '</div>';
            html += '</div>';
            html += '<div class="opportunity-description">' + opportunity.description + '</div>';

            if (opportunity.recommendations && opportunity.recommendations.length > 0) {
                html += '<div class="opportunity-recommendations">';
                html += '<strong>Recommendations:</strong>';
                html += '<ul>';
                opportunity.recommendations.forEach(function(rec) {
                    html += '<li>' + rec + '</li>';
                });
                html += '</ul>';
                html += '</div>';
            }

            html += '<div class="opportunity-meta">';
            html += '<small><strong>Effort:</strong> ' + opportunity.estimated_effort + ' | ';
            html += '<strong>Impact:</strong> ' + opportunity.estimated_impact + ' | ';
            html += '<strong>Score:</strong> ' + Math.round(opportunity.impact_score) + '</small>';
            html += '</div>';
            html += '</div>';
        });

        container.html(html);
    }

    function loadUserFeedbackStats() {
        // Simulate feedback stats for now
        // In a real implementation, this would make an AJAX call
        setTimeout(function() {
            $('#total-feedback').text('12');
            $('#avg-rating').text('4.2/5');
            $('#satisfaction-rate').text('85%');
        }, 1000);
    }
});
</script>
