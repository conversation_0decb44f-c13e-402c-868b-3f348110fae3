<?php
/**
 * Performance Dashboard for Diagnostic & Auto-Fix Module
 * 
 * Displays real-time performance metrics and optimization insights
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get performance metrics
$performance_metrics = Redco_Diagnostic_Performance_Tracker::get_performance_metrics(7);
$latest_metrics = !empty($performance_metrics) ? end($performance_metrics) : null;

// Calculate performance trends
$performance_trend = 'stable';
if (count($performance_metrics) >= 2) {
    $recent_avg = array_slice($performance_metrics, -3);
    $older_avg = array_slice($performance_metrics, 0, 3);
    
    $recent_load_time = array_sum(array_column($recent_avg, 'avg_load_time')) / count($recent_avg);
    $older_load_time = array_sum(array_column($older_avg, 'avg_load_time')) / count($older_avg);
    
    if ($recent_load_time < $older_load_time * 0.9) {
        $performance_trend = 'improving';
    } elseif ($recent_load_time > $older_load_time * 1.1) {
        $performance_trend = 'declining';
    }
}

// Performance status
$performance_status = 'excellent';
if ($latest_metrics) {
    $avg_load_time_seconds = $latest_metrics['avg_load_time'] / 1000;
    if ($avg_load_time_seconds > 5) {
        $performance_status = 'poor';
    } elseif ($avg_load_time_seconds > 3) {
        $performance_status = 'fair';
    } elseif ($avg_load_time_seconds > 2) {
        $performance_status = 'good';
    }
}
?>

<div class="redco-performance-dashboard">
    <div class="performance-header">
        <h3>
            <span class="dashicons dashicons-performance"></span>
            <?php _e('Performance Monitoring', 'redco-optimizer'); ?>
            <span class="performance-status status-<?php echo esc_attr($performance_status); ?>">
                <?php echo ucfirst($performance_status); ?>
            </span>
        </h3>
        <div class="performance-trend trend-<?php echo esc_attr($performance_trend); ?>">
            <?php if ($performance_trend === 'improving'): ?>
                <span class="dashicons dashicons-arrow-up-alt"></span>
                <?php _e('Improving', 'redco-optimizer'); ?>
            <?php elseif ($performance_trend === 'declining'): ?>
                <span class="dashicons dashicons-arrow-down-alt"></span>
                <?php _e('Declining', 'redco-optimizer'); ?>
            <?php else: ?>
                <span class="dashicons dashicons-minus"></span>
                <?php _e('Stable', 'redco-optimizer'); ?>
            <?php endif; ?>
        </div>
    </div>

    <?php if ($latest_metrics): ?>
        <div class="performance-metrics-grid">
            <div class="metric-card load-time">
                <div class="metric-icon">
                    <span class="dashicons dashicons-clock"></span>
                </div>
                <div class="metric-content">
                    <div class="metric-value">
                        <?php echo number_format($latest_metrics['avg_load_time'] / 1000, 2); ?>s
                    </div>
                    <div class="metric-label"><?php _e('Avg Load Time', 'redco-optimizer'); ?></div>
                    <div class="metric-target">
                        <?php _e('Target: <2s', 'redco-optimizer'); ?>
                    </div>
                </div>
                <div class="metric-grade grade-<?php echo strtolower($this->get_load_time_grade($latest_metrics['avg_load_time'])); ?>">
                    <?php echo $this->get_load_time_grade($latest_metrics['avg_load_time']); ?>
                </div>
            </div>

            <div class="metric-card cache-performance">
                <div class="metric-icon">
                    <span class="dashicons dashicons-database-view"></span>
                </div>
                <div class="metric-content">
                    <div class="metric-value">
                        <?php echo number_format($latest_metrics['avg_cache_hit_ratio'] * 100, 1); ?>%
                    </div>
                    <div class="metric-label"><?php _e('Cache Hit Ratio', 'redco-optimizer'); ?></div>
                    <div class="metric-target">
                        <?php _e('Target: >80%', 'redco-optimizer'); ?>
                    </div>
                </div>
                <div class="metric-grade grade-<?php echo strtolower($this->get_cache_grade($latest_metrics['avg_cache_hit_ratio'])); ?>">
                    <?php echo $this->get_cache_grade($latest_metrics['avg_cache_hit_ratio']); ?>
                </div>
            </div>

            <div class="metric-card ajax-efficiency">
                <div class="metric-icon">
                    <span class="dashicons dashicons-networking"></span>
                </div>
                <div class="metric-content">
                    <div class="metric-value">
                        <?php echo number_format($latest_metrics['avg_ajax_requests'], 1); ?>
                    </div>
                    <div class="metric-label"><?php _e('Avg AJAX Requests', 'redco-optimizer'); ?></div>
                    <div class="metric-target">
                        <?php _e('Target: <3', 'redco-optimizer'); ?>
                    </div>
                </div>
                <div class="metric-grade grade-<?php echo strtolower($this->get_ajax_grade($latest_metrics['avg_ajax_requests'])); ?>">
                    <?php echo $this->get_ajax_grade($latest_metrics['avg_ajax_requests']); ?>
                </div>
            </div>

            <div class="metric-card error-rate">
                <div class="metric-icon">
                    <span class="dashicons dashicons-warning"></span>
                </div>
                <div class="metric-content">
                    <div class="metric-value">
                        <?php echo $latest_metrics['total_errors']; ?>
                    </div>
                    <div class="metric-label"><?php _e('Total Errors', 'redco-optimizer'); ?></div>
                    <div class="metric-target">
                        <?php _e('Target: 0', 'redco-optimizer'); ?>
                    </div>
                </div>
                <div class="metric-grade grade-<?php echo strtolower($this->get_error_grade($latest_metrics['total_errors'])); ?>">
                    <?php echo $this->get_error_grade($latest_metrics['total_errors']); ?>
                </div>
            </div>
        </div>

        <div class="performance-chart-container">
            <h4><?php _e('7-Day Performance Trend', 'redco-optimizer'); ?></h4>
            <canvas id="performance-trend-chart" width="400" height="200"></canvas>
        </div>

        <div class="performance-insights">
            <h4><?php _e('Performance Insights', 'redco-optimizer'); ?></h4>
            <div class="insights-grid">
                <?php
                $insights = $this->generate_performance_insights($latest_metrics, $performance_metrics);
                foreach ($insights as $insight):
                ?>
                    <div class="insight-card insight-<?php echo esc_attr($insight['type']); ?>">
                        <div class="insight-icon">
                            <span class="dashicons dashicons-<?php echo esc_attr($insight['icon']); ?>"></span>
                        </div>
                        <div class="insight-content">
                            <div class="insight-title"><?php echo esc_html($insight['title']); ?></div>
                            <div class="insight-description"><?php echo esc_html($insight['description']); ?></div>
                            <?php if (!empty($insight['action'])): ?>
                                <div class="insight-action">
                                    <button class="button button-small" onclick="<?php echo esc_attr($insight['action']); ?>">
                                        <?php echo esc_html($insight['action_text']); ?>
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

    <?php else: ?>
        <div class="no-performance-data">
            <div class="no-data-icon">
                <span class="dashicons dashicons-chart-line"></span>
            </div>
            <h4><?php _e('No Performance Data Available', 'redco-optimizer'); ?></h4>
            <p><?php _e('Performance tracking will begin once you start using the Diagnostic & Auto-Fix module.', 'redco-optimizer'); ?></p>
            <button class="button button-primary" onclick="location.reload()">
                <?php _e('Refresh Data', 'redco-optimizer'); ?>
            </button>
        </div>
    <?php endif; ?>
</div>

<style>
.redco-performance-dashboard {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.performance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e0e0e0;
}

.performance-header h3 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.performance-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-excellent { background: #d4edda; color: #155724; }
.status-good { background: #d1ecf1; color: #0c5460; }
.status-fair { background: #fff3cd; color: #856404; }
.status-poor { background: #f8d7da; color: #721c24; }

.performance-trend {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    font-weight: 500;
}

.trend-improving { color: #28a745; }
.trend-declining { color: #dc3545; }
.trend-stable { color: #6c757d; }

.performance-metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
}

.metric-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
    position: relative;
}

.metric-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #4CAF50;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
}

.metric-content {
    flex: 1;
}

.metric-value {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    line-height: 1;
}

.metric-label {
    font-size: 12px;
    color: #666;
    margin: 5px 0;
    font-weight: 500;
}

.metric-target {
    font-size: 11px;
    color: #999;
}

.metric-grade {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 700;
    color: white;
}

.grade-a { background: #28a745; }
.grade-b { background: #17a2b8; }
.grade-c { background: #ffc107; color: #333; }
.grade-d { background: #fd7e14; }
.grade-f { background: #dc3545; }

.performance-chart-container {
    margin: 30px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.performance-chart-container h4 {
    margin: 0 0 15px 0;
    color: #333;
}

.performance-insights {
    margin-top: 30px;
}

.insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.insight-card {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-left: 4px solid #4CAF50;
    border-radius: 4px;
    padding: 15px;
    display: flex;
    gap: 15px;
}

.insight-warning { border-left-color: #ffc107; }
.insight-error { border-left-color: #dc3545; }
.insight-success { border-left-color: #28a745; }

.insight-icon {
    color: #4CAF50;
    font-size: 20px;
}

.insight-warning .insight-icon { color: #ffc107; }
.insight-error .insight-icon { color: #dc3545; }

.insight-title {
    font-weight: 600;
    margin-bottom: 5px;
}

.insight-description {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
}

.no-performance-data {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.no-data-icon {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 20px;
}
</style>

<?php if ($latest_metrics && !empty($performance_metrics)): ?>
<script>
// Performance trend chart
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('performance-trend-chart');
    if (ctx && typeof Chart !== 'undefined') {
        const chartData = <?php echo json_encode($performance_metrics); ?>;
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: chartData.map(d => d.date),
                datasets: [{
                    label: 'Load Time (ms)',
                    data: chartData.map(d => d.avg_load_time),
                    borderColor: '#4CAF50',
                    backgroundColor: 'rgba(76, 175, 80, 0.1)',
                    tension: 0.4
                }, {
                    label: 'AJAX Requests',
                    data: chartData.map(d => d.avg_ajax_requests),
                    borderColor: '#2196F3',
                    backgroundColor: 'rgba(33, 150, 243, 0.1)',
                    tension: 0.4,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Load Time (ms)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'AJAX Requests'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                },
                plugins: {
                    legend: {
                        display: true
                    }
                }
            }
        });
    }
});
</script>
<?php endif; ?>

<?php
// Helper methods for grading
function get_load_time_grade($load_time_ms) {
    $seconds = $load_time_ms / 1000;
    if ($seconds < 2) return 'A';
    if ($seconds < 3) return 'B';
    if ($seconds < 5) return 'C';
    if ($seconds < 8) return 'D';
    return 'F';
}

function get_cache_grade($hit_ratio) {
    if ($hit_ratio >= 0.9) return 'A';
    if ($hit_ratio >= 0.8) return 'B';
    if ($hit_ratio >= 0.6) return 'C';
    if ($hit_ratio >= 0.4) return 'D';
    return 'F';
}

function get_ajax_grade($requests) {
    if ($requests <= 2) return 'A';
    if ($requests <= 4) return 'B';
    if ($requests <= 6) return 'C';
    if ($requests <= 10) return 'D';
    return 'F';
}

function get_error_grade($errors) {
    if ($errors == 0) return 'A';
    if ($errors <= 1) return 'B';
    if ($errors <= 2) return 'C';
    if ($errors <= 5) return 'D';
    return 'F';
}

function generate_performance_insights($latest, $history) {
    $insights = array();
    
    // Load time insight
    $load_time_seconds = $latest['avg_load_time'] / 1000;
    if ($load_time_seconds < 2) {
        $insights[] = array(
            'type' => 'success',
            'icon' => 'yes-alt',
            'title' => 'Excellent Load Time',
            'description' => 'Your diagnostic module loads in under 2 seconds - optimal performance!'
        );
    } elseif ($load_time_seconds > 5) {
        $insights[] = array(
            'type' => 'error',
            'icon' => 'warning',
            'title' => 'Slow Load Time Detected',
            'description' => 'Load time exceeds 5 seconds. Consider optimizing database queries or enabling caching.',
            'action' => 'optimizePerformance()',
            'action_text' => 'Optimize Now'
        );
    }
    
    // Cache insight
    if ($latest['avg_cache_hit_ratio'] < 0.6) {
        $insights[] = array(
            'type' => 'warning',
            'icon' => 'database-view',
            'title' => 'Low Cache Efficiency',
            'description' => 'Cache hit ratio is below 60%. Consider increasing cache duration or improving cache strategies.',
            'action' => 'optimizeCache()',
            'action_text' => 'Optimize Cache'
        );
    }
    
    // Error insight
    if ($latest['total_errors'] > 0) {
        $insights[] = array(
            'type' => 'error',
            'icon' => 'warning',
            'title' => 'JavaScript Errors Detected',
            'description' => "Found {$latest['total_errors']} JavaScript errors. Check browser console for details.",
            'action' => 'viewErrorLog()',
            'action_text' => 'View Errors'
        );
    }
    
    return $insights;
}
?>
