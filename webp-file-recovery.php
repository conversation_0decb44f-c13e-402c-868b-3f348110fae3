<?php
/**
 * WebP File Recovery Script
 * 
 * This script converts WebP files back to their original formats
 * based on the conversion metadata stored in WordPress database.
 * 
 * USAGE:
 * 1. Upload this file to your WordPress root directory
 * 2. Run via browser: yoursite.com/webp-file-recovery.php
 * 3. Or run via command line: php webp-file-recovery.php
 * 
 * REQUIREMENTS:
 * - GD library with WebP support
 * - Write permissions to uploads directory
 * - WordPress database access
 */

// WordPress bootstrap
require_once('wp-config.php');
require_once('wp-includes/wp-db.php');

// Security check
if (!current_user_can('manage_options') && !defined('WP_CLI')) {
    die('Access denied. Administrator privileges required.');
}

class WebPFileRecovery {
    
    private $wpdb;
    private $upload_dir;
    private $stats = array(
        'processed' => 0,
        'converted' => 0,
        'failed' => 0,
        'skipped' => 0
    );
    
    public function __construct() {
        global $wpdb;
        $this->wpdb = $wpdb;
        $this->upload_dir = wp_upload_dir();
        
        // Check requirements
        $this->check_requirements();
    }
    
    /**
     * Check system requirements
     */
    private function check_requirements() {
        $errors = array();
        
        if (!extension_loaded('gd')) {
            $errors[] = 'GD library is not installed';
        }
        
        if (!function_exists('imagecreatefromwebp')) {
            $errors[] = 'WebP support is not available in GD library';
        }
        
        if (!is_writable($this->upload_dir['basedir'])) {
            $errors[] = 'Uploads directory is not writable: ' . $this->upload_dir['basedir'];
        }
        
        if (!empty($errors)) {
            die('Requirements check failed:' . "\n" . implode("\n", $errors));
        }
        
        echo "✅ Requirements check passed\n";
    }
    
    /**
     * Main recovery process
     */
    public function run_recovery() {
        echo "🔄 Starting WebP file recovery process...\n\n";
        
        // Get all converted WebP images
        $converted_images = $this->get_converted_images();
        
        echo "📊 Found " . count($converted_images) . " converted images to process\n\n";
        
        foreach ($converted_images as $image) {
            $this->process_image($image);
        }
        
        $this->print_summary();
    }
    
    /**
     * Get all converted WebP images from database
     */
    private function get_converted_images() {
        $query = "
            SELECT 
                pm.post_id,
                p.post_title,
                p.post_mime_type,
                pm.meta_value as conversion_data
            FROM {$this->wpdb->postmeta} pm
            INNER JOIN {$this->wpdb->posts} p ON pm.post_id = p.ID
            WHERE pm.meta_key = 'webp_conversion_data'
            AND pm.meta_value LIKE '%converted%'
            AND p.post_type = 'attachment'
            ORDER BY pm.post_id
        ";
        
        return $this->wpdb->get_results($query);
    }
    
    /**
     * Process individual image
     */
    private function process_image($image) {
        $this->stats['processed']++;
        
        echo "🖼️  Processing: {$image->post_title} (ID: {$image->post_id})\n";
        
        // Parse conversion data
        $conversion_data = maybe_unserialize($image->conversion_data);
        if (!$conversion_data || !isset($conversion_data['file_path']) || !isset($conversion_data['webp_path'])) {
            echo "   ❌ Missing conversion data\n";
            $this->stats['failed']++;
            return;
        }
        
        $original_path = $conversion_data['file_path'];
        $webp_path = $conversion_data['webp_path'];
        
        // Determine original format from file path
        $original_extension = strtolower(pathinfo($original_path, PATHINFO_EXTENSION));
        $original_format = $this->get_format_from_extension($original_extension);
        
        if (!$original_format) {
            echo "   ❌ Unknown original format: {$original_extension}\n";
            $this->stats['failed']++;
            return;
        }
        
        // Check if WebP file exists
        if (!file_exists($webp_path)) {
            echo "   ⚠️  WebP file not found: {$webp_path}\n";
            $this->stats['skipped']++;
            return;
        }
        
        // Check if original already exists
        if (file_exists($original_path)) {
            echo "   ✅ Original already exists, skipping\n";
            $this->stats['skipped']++;
            return;
        }
        
        // Convert WebP back to original format
        $success = $this->convert_webp_to_original($webp_path, $original_path, $original_format);
        
        if ($success) {
            echo "   ✅ Converted to {$original_extension}\n";
            $this->stats['converted']++;
            
            // Update WordPress database
            $this->update_wordpress_references($image->post_id, $original_path, $original_format);
            
        } else {
            echo "   ❌ Conversion failed\n";
            $this->stats['failed']++;
        }
        
        echo "\n";
    }
    
    /**
     * Get image format from file extension
     */
    private function get_format_from_extension($extension) {
        switch ($extension) {
            case 'jpg':
            case 'jpeg':
                return 'jpeg';
            case 'png':
                return 'png';
            case 'gif':
                return 'gif';
            default:
                return false;
        }
    }
    
    /**
     * Convert WebP file back to original format
     */
    private function convert_webp_to_original($webp_path, $original_path, $format) {
        try {
            // Create image from WebP
            $webp_image = imagecreatefromwebp($webp_path);
            if (!$webp_image) {
                return false;
            }
            
            // Ensure directory exists
            $dir = dirname($original_path);
            if (!is_dir($dir)) {
                wp_mkdir_p($dir);
            }
            
            // Convert based on format
            $success = false;
            switch ($format) {
                case 'jpeg':
                    $success = imagejpeg($webp_image, $original_path, 90);
                    break;
                case 'png':
                    // Preserve transparency for PNG
                    imagealphablending($webp_image, false);
                    imagesavealpha($webp_image, true);
                    $success = imagepng($webp_image, $original_path, 6);
                    break;
                case 'gif':
                    $success = imagegif($webp_image, $original_path);
                    break;
            }
            
            // Clean up memory
            imagedestroy($webp_image);
            
            return $success;
            
        } catch (Exception $e) {
            echo "   ❌ Error: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * Update WordPress database references
     */
    private function update_wordpress_references($post_id, $original_path, $format) {
        // Update post mime type
        $mime_type = 'image/' . $format;
        wp_update_post(array(
            'ID' => $post_id,
            'post_mime_type' => $mime_type
        ));
        
        // Update attached file path
        update_attached_file($post_id, $original_path);
        
        // Update attachment metadata if needed
        $metadata = wp_get_attachment_metadata($post_id);
        if ($metadata && isset($metadata['webp_version'])) {
            unset($metadata['webp_version']);
            wp_update_attachment_metadata($post_id, $metadata);
        }
        
        echo "   📝 Updated WordPress references\n";
    }
    
    /**
     * Print recovery summary
     */
    private function print_summary() {
        echo "\n" . str_repeat("=", 50) . "\n";
        echo "🎉 RECOVERY COMPLETE\n";
        echo str_repeat("=", 50) . "\n";
        echo "📊 STATISTICS:\n";
        echo "   • Processed: {$this->stats['processed']}\n";
        echo "   • Converted: {$this->stats['converted']}\n";
        echo "   • Failed: {$this->stats['failed']}\n";
        echo "   • Skipped: {$this->stats['skipped']}\n";
        echo "\n";
        
        if ($this->stats['converted'] > 0) {
            echo "✅ Successfully recovered {$this->stats['converted']} images!\n";
        }
        
        if ($this->stats['failed'] > 0) {
            echo "⚠️  {$this->stats['failed']} images could not be recovered.\n";
            echo "   Check file permissions and GD library support.\n";
        }
        
        echo "\n🔧 NEXT STEPS:\n";
        echo "1. Run the SQL recovery script to update database references\n";
        echo "2. Clear any caching plugins\n";
        echo "3. Test your website to ensure images are loading correctly\n";
        echo "4. Consider re-enabling WebP with backup originals option\n";
    }
}

// Run the recovery
if (php_sapi_name() === 'cli' || isset($_GET['run'])) {
    $recovery = new WebPFileRecovery();
    $recovery->run_recovery();
} else {
    echo '<h1>WebP File Recovery</h1>';
    echo '<p>This script will convert WebP files back to their original formats.</p>';
    echo '<p><strong>Warning:</strong> This process cannot be undone. Make sure you have backups!</p>';
    echo '<p><a href="?run=1" style="background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;">Start Recovery Process</a></p>';
}
?>
