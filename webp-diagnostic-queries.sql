-- =====================================================
-- WebP Diagnostic Queries
-- =====================================================
-- Run these queries first to understand your WebP situation
-- These are safe read-only queries that won't change anything
-- =====================================================

-- Query 1: Check if WebP conversion metadata exists at all
-- =====================================================
SELECT 
    'WebP Metadata Check' as query_type,
    COUNT(*) as total_records
FROM wp_postmeta 
WHERE meta_key = '_webp_conversion_data';

-- Query 2: Check for any WebP-related data
-- =====================================================
SELECT 
    'WebP Related Data' as query_type,
    meta_key,
    COUNT(*) as count
FROM wp_postmeta 
WHERE meta_key LIKE '%webp%' 
   OR meta_key LIKE '%_webp_%'
   OR meta_value LIKE '%webp%'
GROUP BY meta_key
ORDER BY count DESC;

-- Query 3: Check attachment MIME types
-- =====================================================
SELECT 
    'Attachment MIME Types' as query_type,
    post_mime_type,
    COUNT(*) as count
FROM wp_posts 
WHERE post_type = 'attachment'
  AND post_mime_type LIKE 'image/%'
GROUP BY post_mime_type
ORDER BY count DESC;

-- Query 4: Look for any conversion-related metadata
-- =====================================================
SELECT 
    'Conversion Metadata Search' as query_type,
    meta_key,
    COUNT(*) as count
FROM wp_postmeta 
WHERE meta_key LIKE '%conversion%' 
   OR meta_key LIKE '%convert%'
   OR meta_value LIKE '%convert%'
GROUP BY meta_key
ORDER BY count DESC;

-- Query 5: Sample WebP conversion data (if it exists)
-- =====================================================
SELECT 
    'Sample WebP Data' as query_type,
    post_id,
    LEFT(meta_value, 500) as sample_data
FROM wp_postmeta 
WHERE meta_key = '_webp_conversion_data'
LIMIT 3;

-- Query 6: Check for WebP images in posts table
-- =====================================================
SELECT 
    'WebP Images in Posts' as query_type,
    p.ID,
    p.post_title,
    p.post_mime_type,
    p.post_date
FROM wp_posts p
WHERE p.post_type = 'attachment'
  AND p.post_mime_type = 'image/webp'
ORDER BY p.ID DESC
LIMIT 10;

-- Query 7: Check attached files for WebP extensions
-- =====================================================
SELECT 
    'WebP File Paths' as query_type,
    pm.post_id,
    pm.meta_value as file_path
FROM wp_postmeta pm
WHERE pm.meta_key = '_wp_attached_file'
  AND pm.meta_value LIKE '%.webp'
LIMIT 10;

-- Query 8: Look for Redco Optimizer options
-- =====================================================
SELECT 
    'Redco Options' as query_type,
    option_name,
    LEFT(option_value, 200) as option_value_sample
FROM wp_options 
WHERE option_name LIKE '%redco%'
ORDER BY option_name;

-- Query 9: Check for any backup or original file references
-- =====================================================
SELECT 
    'Backup/Original References' as query_type,
    meta_key,
    COUNT(*) as count
FROM wp_postmeta 
WHERE meta_key LIKE '%backup%' 
   OR meta_key LIKE '%original%'
   OR meta_value LIKE '%backup%'
   OR meta_value LIKE '%original%'
GROUP BY meta_key
ORDER BY count DESC;

-- Query 10: Final summary
-- =====================================================
SELECT 
    'SUMMARY' as summary_type,
    'Total Attachments' as metric,
    COUNT(*) as value
FROM wp_posts 
WHERE post_type = 'attachment'

UNION ALL

SELECT 
    'SUMMARY' as summary_type,
    'Image Attachments' as metric,
    COUNT(*) as value
FROM wp_posts 
WHERE post_type = 'attachment'
  AND post_mime_type LIKE 'image/%'

UNION ALL

SELECT 
    'SUMMARY' as summary_type,
    'WebP Images' as metric,
    COUNT(*) as value
FROM wp_posts 
WHERE post_type = 'attachment'
  AND post_mime_type = 'image/webp'

UNION ALL

SELECT 
    'SUMMARY' as summary_type,
    'WebP Conversion Records' as metric,
    COUNT(*) as value
FROM wp_postmeta 
WHERE meta_key = '_webp_conversion_data'

UNION ALL

SELECT 
    'SUMMARY' as summary_type,
    'Redco Options' as metric,
    COUNT(*) as value
FROM wp_options 
WHERE option_name LIKE '%redco%';

-- =====================================================
-- Instructions:
-- =====================================================
-- 1. Run these queries in phpMyAdmin
-- 2. Look at the results to understand your situation
-- 3. If you see WebP conversion data, proceed with recovery
-- 4. If no data is found, the issue might be different
-- 
-- Key things to look for:
-- - WebP conversion metadata records
-- - WebP images in posts table
-- - Original vs current MIME types
-- - File path patterns
-- =====================================================
