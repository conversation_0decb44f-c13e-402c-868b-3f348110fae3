<?php
/**
 * Performance Debugger for Redco Optimizer
 * 
 * Identifies and fixes performance issues causing PageSpeed degradation
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Performance_Debugger {
    
    /**
     * Critical performance issues found
     */
    private static $critical_issues = array();
    
    /**
     * Performance impact analysis
     */
    private static $performance_impact = array();
    
    /**
     * Initialize debugger
     */
    public static function init() {
        // Only run analysis if explicitly requested
        if (isset($_GET['redco_debug_performance']) && current_user_can('manage_options')) {
            add_action('wp_footer', array(__CLASS__, 'analyze_performance_impact'), 999);
        }
        
        // Add admin notice for performance issues
        add_action('admin_notices', array(__CLASS__, 'show_performance_warnings'));
    }
    
    /**
     * Analyze performance impact
     */
    public static function analyze_performance_impact() {
        $issues = array();
        
        // 1. Check for frontend admin assets loading
        $frontend_admin_assets = self::check_frontend_admin_assets();
        if (!empty($frontend_admin_assets)) {
            $issues[] = array(
                'type' => 'critical',
                'title' => 'Admin Assets Loading on Frontend',
                'description' => 'Admin CSS/JS files are loading on frontend pages',
                'impact' => 'High - Adds unnecessary 200-500KB to page size',
                'assets' => $frontend_admin_assets
            );
        }
        
        // 2. Check for excessive database queries
        $query_issues = self::check_database_queries();
        if (!empty($query_issues)) {
            $issues = array_merge($issues, $query_issues);
        }
        
        // 3. Check for render-blocking resources
        $render_blocking = self::check_render_blocking_resources();
        if (!empty($render_blocking)) {
            $issues[] = array(
                'type' => 'critical',
                'title' => 'Render-Blocking Resources',
                'description' => 'Scripts/styles blocking page rendering',
                'impact' => 'High - Delays First Contentful Paint',
                'resources' => $render_blocking
            );
        }
        
        // 4. Check for external CDN resources
        $external_resources = self::check_external_resources();
        if (!empty($external_resources)) {
            $issues[] = array(
                'type' => 'warning',
                'title' => 'External CDN Dependencies',
                'description' => 'Loading resources from external CDNs',
                'impact' => 'Medium - Adds DNS lookup time',
                'resources' => $external_resources
            );
        }
        
        // 5. Check for performance monitoring overhead
        $monitoring_overhead = self::check_monitoring_overhead();
        if (!empty($monitoring_overhead)) {
            $issues[] = array(
                'type' => 'warning',
                'title' => 'Performance Monitoring Overhead',
                'description' => 'Performance tracking adding overhead',
                'impact' => 'Medium - Adds processing time',
                'details' => $monitoring_overhead
            );
        }
        
        // 6. Check for security filtering overhead
        $security_overhead = self::check_security_overhead();
        if (!empty($security_overhead)) {
            $issues[] = array(
                'type' => 'warning',
                'title' => 'Security Filtering Overhead',
                'description' => 'Security checks running on every request',
                'impact' => 'Medium - Adds processing time',
                'details' => $security_overhead
            );
        }
        
        // Store issues for admin display
        self::$critical_issues = $issues;
        
        // Output debug information
        if (!empty($issues)) {
            echo "\n<!-- REDCO PERFORMANCE DEBUG -->\n";
            echo "<!-- CRITICAL ISSUES FOUND: " . count($issues) . " -->\n";
            foreach ($issues as $issue) {
                echo "<!-- {$issue['type']}: {$issue['title']} - {$issue['impact']} -->\n";
            }
            echo "<!-- END REDCO PERFORMANCE DEBUG -->\n";
        }
    }
    
    /**
     * Check for admin assets loading on frontend
     */
    private static function check_frontend_admin_assets() {
        global $wp_scripts, $wp_styles;
        $admin_assets = array();
        
        if (!is_admin()) {
            // Check scripts
            if (isset($wp_scripts->queue)) {
                foreach ($wp_scripts->queue as $handle) {
                    if (isset($wp_scripts->registered[$handle])) {
                        $script = $wp_scripts->registered[$handle];
                        if (strpos($script->src, 'redco-optimizer') !== false && 
                            (strpos($handle, 'admin') !== false || strpos($script->src, 'admin') !== false)) {
                            $admin_assets['scripts'][] = array(
                                'handle' => $handle,
                                'src' => $script->src
                            );
                        }
                    }
                }
            }
            
            // Check styles
            if (isset($wp_styles->queue)) {
                foreach ($wp_styles->queue as $handle) {
                    if (isset($wp_styles->registered[$handle])) {
                        $style = $wp_styles->registered[$handle];
                        if (strpos($style->src, 'redco-optimizer') !== false && 
                            (strpos($handle, 'admin') !== false || strpos($style->src, 'admin') !== false)) {
                            $admin_assets['styles'][] = array(
                                'handle' => $handle,
                                'src' => $style->src
                            );
                        }
                    }
                }
            }
        }
        
        return $admin_assets;
    }
    
    /**
     * Check database queries
     */
    private static function check_database_queries() {
        $issues = array();
        
        if (defined('SAVEQUERIES') && SAVEQUERIES) {
            global $wpdb;
            $query_count = count($wpdb->queries);
            
            if ($query_count > 50) {
                $issues[] = array(
                    'type' => 'warning',
                    'title' => 'Excessive Database Queries',
                    'description' => "Page executed {$query_count} database queries",
                    'impact' => 'High - Slows page load time',
                    'query_count' => $query_count
                );
            }
        }
        
        return $issues;
    }
    
    /**
     * Check render-blocking resources
     */
    private static function check_render_blocking_resources() {
        global $wp_scripts, $wp_styles;
        $render_blocking = array();
        
        // Check for scripts in head without defer/async
        if (isset($wp_scripts->queue)) {
            foreach ($wp_scripts->queue as $handle) {
                if (isset($wp_scripts->registered[$handle])) {
                    $script = $wp_scripts->registered[$handle];
                    if (strpos($script->src, 'redco-optimizer') !== false && !$script->extra) {
                        $render_blocking[] = "Script: {$handle}";
                    }
                }
            }
        }
        
        return $render_blocking;
    }
    
    /**
     * Check external resources
     */
    private static function check_external_resources() {
        global $wp_scripts;
        $external = array();
        
        if (isset($wp_scripts->queue)) {
            foreach ($wp_scripts->queue as $handle) {
                if (isset($wp_scripts->registered[$handle])) {
                    $script = $wp_scripts->registered[$handle];
                    if (!empty($script->src) && strpos($script->src, 'http') === 0 && 
                        strpos($script->src, home_url()) === false) {
                        $external[] = array(
                            'handle' => $handle,
                            'src' => $script->src
                        );
                    }
                }
            }
        }
        
        return $external;
    }
    
    /**
     * Check monitoring overhead
     */
    private static function check_monitoring_overhead() {
        $overhead = array();
        
        if (class_exists('Redco_Performance_Monitor')) {
            $overhead[] = 'Performance monitoring active on frontend';
        }
        
        if (defined('REDCO_PAGE_START_TIME')) {
            $overhead[] = 'Page timing tracking active';
        }
        
        return $overhead;
    }
    
    /**
     * Check security overhead
     */
    private static function check_security_overhead() {
        $overhead = array();
        
        if (class_exists('Redco_Security_Manager')) {
            $config = get_option('redco_optimizer_security_config', array());
            
            if (!empty($config['enable_request_filtering'])) {
                $overhead[] = 'Request filtering active on every page load';
            }
            
            if (!empty($config['enable_admin_protection'])) {
                $overhead[] = 'Admin protection checks active';
            }
        }
        
        return $overhead;
    }
    
    /**
     * Show performance warnings in admin
     */
    public static function show_performance_warnings() {
        if (!current_user_can('manage_options') || empty(self::$critical_issues)) {
            return;
        }
        
        $critical_count = 0;
        foreach (self::$critical_issues as $issue) {
            if ($issue['type'] === 'critical') {
                $critical_count++;
            }
        }
        
        if ($critical_count > 0) {
            echo '<div class="notice notice-error"><p>';
            echo '<strong>Redco Optimizer Performance Alert:</strong> ';
            echo $critical_count . ' critical performance issues detected that may be affecting PageSpeed scores. ';
            echo '<a href="' . admin_url('admin.php?page=redco-optimizer&redco_debug_performance=1') . '">View Details</a>';
            echo '</p></div>';
        }
    }
    
    /**
     * Get performance issues
     */
    public static function get_issues() {
        return self::$critical_issues;
    }
}

// Initialize the debugger
Redco_Performance_Debugger::init();
