/**
 * Redco Optimizer Settings Auto-Save
 * Consolidated settings functionality with comprehensive auto-save
 */

jQuery(document).ready(function($) {
    'use strict';

    // Auto-save functionality
    let saveTimeout;
    const SAVE_DELAY = 1000; // 1 second delay

    /**
     * Initialize all settings functionality
     */
    function initSettings() {
        initAutoSave();
        initFormControls();
    }

    /**
     * Initialize form controls and UI enhancements
     */
    function initFormControls() {
        // Prevent form submission since we auto-save
        $('.redco-settings-form').on('submit', function(e) {
            e.preventDefault();
            return false;
        });

        // Hide any save buttons since we have auto-save
        $('.redco-save-button, .settings-form-footer').hide();

        // Basic form validation for required fields
        $('.redco-settings-form').on('submit', function(e) {
            let isValid = true;

            // Simple required field validation
            $(this).find('[required]').each(function() {
                if (!$(this).val()) {
                    isValid = false;
                    $(this).css('border-color', '#e74c3c');
                } else {
                    $(this).css('border-color', '');
                }
            });

            if (!isValid) {
                e.preventDefault();
                if (typeof showToast === 'function') {
                    showToast('Please fill in all required fields', 'error', 4000);
                } else {
                    console.error('Please fill in all required fields');
                }
            }
        });
    }

    /**
     * Initialize comprehensive auto-save functionality for ALL form field types
     */
    function initAutoSave() {
        // COMPREHENSIVE AUTO-SAVE: Bind to ALL form field types
        $(document).on('change input keyup', '.redco-settings-form input, .redco-settings-form select, .redco-settings-form textarea', function() {
        const $element = $(this);
        const elementType = $element.prop('tagName').toLowerCase();
        const inputType = $element.attr('type');

        // Skip if element doesn't have proper name attribute
        if (!$element.attr('name')) {
            return;
        }

        // Extract setting group and name from name attribute
        let settingGroup, settingName, value;

        // Handle different name formats
        const nameAttr = $element.attr('name');
        if (nameAttr.includes('[') && nameAttr.includes(']')) {
            // Format: group[setting] or group[setting][sub]
            const matches = nameAttr.match(/^([^[]+)\[([^\]]+)\](?:\[([^\]]+)\])?$/);
            if (matches) {
                settingGroup = matches[1];
                settingName = matches[2];

                // Handle sub-settings (like arrays)
                if (matches[3]) {
                    settingName = matches[2] + '[' + matches[3] + ']';
                }
            }
        } else if ($element.data('setting-group') && $element.data('setting-name')) {
            // Use data attributes if available
            settingGroup = $element.data('setting-group');
            settingName = $element.data('setting-name');
        } else {
            // Skip if we can't determine the setting structure
            return;
        }

        // Get value based on element type
        if (inputType === 'checkbox') {
            value = $element.is(':checked') ? 1 : 0;
        } else if (inputType === 'radio') {
            // Only save if this radio button is checked
            if (!$element.is(':checked')) {
                return;
            }
            value = $element.val();
        } else if (elementType === 'select') {
            value = $element.val();
        } else if (elementType === 'textarea') {
            value = $element.val();
        } else {
            // text, number, email, url, etc.
            value = $element.val();
        }

        // Clear existing timeout
        clearTimeout(saveTimeout);

        // Save after delay (no visual feedback - using toast notifications only)
        saveTimeout = setTimeout(function() {
            autoSaveSetting(settingGroup, settingName, value, $element);
        }, SAVE_DELAY);
        });
    }

    // Auto-save function
    function autoSaveSetting(group, name, value, $element) {
        // Check if required variables are available
        if (typeof redco_settings === 'undefined') {
            return;
        }

        // Use redco_settings.ajaxurl if available, fallback to global ajaxurl
        const ajaxUrl = redco_settings.ajaxurl || (typeof ajaxurl !== 'undefined' ? ajaxurl : null);
        if (!ajaxUrl) {
            return;
        }

        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: {
                action: 'redco_auto_save_setting',
                nonce: redco_settings.nonce,
                group: group,
                name: name,
                value: value
            },
            success: function(response) {
                if (response.success) {
                    // Use global toast notification system only
                    if (typeof showToast === 'function') {
                        showToast(response.data.message || 'Setting saved successfully', 'success', 2000);
                    }
                } else {
                    // Use global toast notification system for errors
                    const errorMessage = response.data && response.data.message ? response.data.message : 'Failed to save setting';
                    if (typeof showToast === 'function') {
                        showToast(errorMessage, 'error', 4000);
                    }
                }
            },
            error: function(xhr, status, error) {
                // Use global toast notification system for AJAX errors
                if (typeof showToast === 'function') {
                    showToast('Network error: Unable to save setting', 'error', 4000);
                }
            }
        });
    }

    // Initialize all settings functionality
    initSettings();

    // Settings page uses server-side navigation, not client-side tabs
    // Remove the tab switching JavaScript that was preventing normal navigation
});
