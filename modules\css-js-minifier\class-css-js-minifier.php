<?php
/**
 * CSS/JS Minifier Module for Redco Optimizer
 *
 * Minifies CSS and JavaScript files to reduce file sizes and improve load times.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_CSS_JS_Minifier {

    /**
     * Module settings
     */
    private $settings = array();

    /**
     * Constructor
     */
    public function __construct() {
        if (redco_is_module_enabled('css-js-minifier')) {
            $this->init();
        }
    }

    /**
     * Initialize the module
     */
    private function init() {
        // Load settings
        $this->load_settings();

        // Initialize hooks
        $this->init_hooks();
    }

    /**
     * Load module settings
     */
    private function load_settings() {
        $this->settings = redco_get_module_option('css-js-minifier', 'settings', array(
            'minify_css' => true,
            'minify_js' => true,
            'exclude_css' => array(), // Minimal exclusions for optimal performance
            'exclude_js' => array('jquery-core', 'jquery-migrate'), // Only critical scripts excluded
            'minify_inline' => true
        ));
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // AJAX handlers for admin
        if (is_admin()) {
            add_action('wp_ajax_redco_clear_minified_cache', array($this, 'ajax_clear_cache'));
            return;
        }

        // CRITICAL FIX: Disable minification temporarily to fix JavaScript errors
        // The minification is causing syntax errors that break the site
        return;

        // TODO: Re-enable after fixing minification algorithm
        /*
        // Minify enqueued files
        if ($this->settings['minify_css']) {
            add_filter('style_loader_src', array($this, 'minify_css_file'), 10, 2);
        }

        if ($this->settings['minify_js']) {
            add_filter('script_loader_src', array($this, 'minify_js_file'), 10, 2);
        }

        // Minify inline styles and scripts
        if ($this->settings['minify_inline']) {
            add_action('wp_print_styles', array($this, 'start_css_buffer'));
            add_action('wp_print_footer_scripts', array($this, 'start_js_buffer'));
        }
        */
    }

    /**
     * Minify CSS file
     */
    public function minify_css_file($src, $handle) {
        // CRITICAL FIX: Ensure src is valid to prevent null parameter warnings
        if (!$src || !is_string($src) || empty($src)) {
            return $src;
        }

        // Skip if handle is excluded
        if (in_array($handle, $this->settings['exclude_css'])) {
            return $src;
        }

        // CRITICAL FIX: Use safe strpos wrapper
        $home_url = home_url();
        if (!$home_url || !is_string($home_url) || redco_safe_strpos($src, $home_url) === false) {
            return $src;
        }

        // Skip already minified files
        if (redco_safe_strpos($src, '.min.') !== false) {
            return $src;
        }

        return $this->get_minified_file_url($src, 'css');
    }

    /**
     * Minify JS file
     */
    public function minify_js_file($src, $handle) {
        // CRITICAL FIX: Ensure src is valid to prevent null parameter warnings
        if (!$src || !is_string($src) || empty($src)) {
            return $src;
        }

        // Skip if handle is excluded
        if (in_array($handle, $this->settings['exclude_js'])) {
            return $src;
        }

        // CRITICAL FIX: Use safe strpos wrapper
        $home_url = home_url();
        if (!$home_url || !is_string($home_url) || redco_safe_strpos($src, $home_url) === false) {
            return $src;
        }

        // Skip already minified files
        if (redco_safe_strpos($src, '.min.') !== false) {
            return $src;
        }

        return $this->get_minified_file_url($src, 'js');
    }

    /**
     * Get minified file URL
     */
    private function get_minified_file_url($src, $type) {
        // Remove query string for cache key
        $clean_src = strtok($src, '?');
        $cache_key = md5($clean_src);

        // Use transient caching to reduce file system operations
        $transient_key = 'redco_minified_' . $cache_key;
        $cached_url = get_transient($transient_key);

        if ($cached_url !== false) {
            return $cached_url;
        }

        // Get cache directory
        $cache_dir = redco_get_cache_dir() . 'minified/';
        if (!file_exists($cache_dir)) {
            wp_mkdir_p($cache_dir);
        }

        $minified_file = $cache_dir . $cache_key . '.' . $type;
        $minified_url = str_replace(ABSPATH, home_url('/'), $minified_file);

        // Check if minified file exists and is newer than source
        if (file_exists($minified_file)) {
            $source_file = $this->url_to_path($clean_src);
            if (file_exists($source_file) && filemtime($minified_file) >= filemtime($source_file)) {
                // Cache the URL for 1 hour to reduce file system checks
                set_transient($transient_key, $minified_url, HOUR_IN_SECONDS);
                return $minified_url;
            }
        }

        // Create minified file only if source exists
        $source_file = $this->url_to_path($clean_src);
        if (file_exists($source_file)) {
            $content = file_get_contents($source_file);
            $minified_content = $this->minify_content($content, $type);

            if ($minified_content !== false) {
                file_put_contents($minified_file, $minified_content);

                // Update statistics (less frequently)
                if (rand(1, 10) === 1) { // Only update stats 10% of the time
                    $original_size = strlen($content);
                    $minified_size = strlen($minified_content);
                    $this->update_stats($original_size, $minified_size);
                }

                // Cache the URL for 1 hour
                set_transient($transient_key, $minified_url, HOUR_IN_SECONDS);
                return $minified_url;
            }
        }

        return $src;
    }

    /**
     * Convert URL to file path
     */
    private function url_to_path($url) {
        // CRITICAL FIX: Ensure url is valid to prevent null parameter warnings
        if (!$url || !is_string($url) || empty($url)) {
            return '';
        }

        // CRITICAL FIX: Ensure strtok() gets valid string parameter
        $url = strtok($url, '?'); // Remove query string
        if (!$url || !is_string($url)) {
            return '';
        }

        $home_url = home_url('/');
        if (!$home_url || !is_string($home_url) || empty($home_url)) {
            return '';
        }

        // CRITICAL FIX: Use safe str_replace wrapper
        return redco_safe_str_replace($home_url, ABSPATH, $url);
    }

    /**
     * Minify content based on type
     */
    private function minify_content($content, $type) {
        switch ($type) {
            case 'css':
                return $this->minify_css($content);
            case 'js':
                return $this->minify_js($content);
            default:
                return $content;
        }
    }

    /**
     * Minify CSS content
     */
    private function minify_css($css) {
        // Remove comments
        $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);

        // Remove unnecessary whitespace
        $css = str_replace(array("\r\n", "\r", "\n", "\t"), '', $css);
        $css = preg_replace('/\s+/', ' ', $css);

        // Remove whitespace around specific characters
        $css = str_replace(array(' {', '{ ', ' }', '} ', ' :', ': ', ' ;', '; ', ' ,', ', '),
                          array('{', '{', '}', '}', ':', ':', ';', ';', ',', ','), $css);

        // Remove trailing semicolon before closing brace
        $css = str_replace(';}', '}', $css);

        // Remove leading and trailing whitespace
        $css = trim($css);

        return $css;
    }

    /**
     * Minify JavaScript content (safer approach)
     */
    private function minify_js($js) {
        // More conservative JS minification to prevent errors

        // Skip minification for complex JavaScript to prevent errors
        if (strpos($js, 'eval(') !== false ||
            strpos($js, 'new Function') !== false ||
            strpos($js, 'document.write') !== false) {
            return $js;
        }

        // Remove single-line comments (but preserve URLs and important comments)
        $js = preg_replace('/(?<!:)\/\/(?![\/\*]).*$/m', '', $js);

        // Remove multi-line comments (but preserve license comments)
        $js = preg_replace('/\/\*(?!\!)[\s\S]*?\*\//', '', $js);

        // Remove unnecessary whitespace (more conservative)
        $js = preg_replace('/\s+/', ' ', $js);

        // Remove whitespace around specific operators only
        $js = preg_replace('/\s*([{}();,])\s*/', '$1', $js);

        // Remove leading and trailing whitespace
        $js = trim($js);

        return $js;
    }

    /**
     * Start CSS output buffering
     */
    public function start_css_buffer() {
        ob_start(array($this, 'minify_inline_css'));
    }

    /**
     * Start JS output buffering
     */
    public function start_js_buffer() {
        ob_start(array($this, 'minify_inline_js'));
    }

    /**
     * Minify inline CSS
     */
    public function minify_inline_css($content) {
        // Find and minify inline CSS
        $content = preg_replace_callback(
            '/<style[^>]*>(.*?)<\/style>/is',
            function($matches) {
                $minified = $this->minify_css($matches[1]);
                return '<style>' . $minified . '</style>';
            },
            $content
        );

        return $content;
    }

    /**
     * Minify inline JavaScript
     */
    public function minify_inline_js($content) {
        // Find and minify inline JavaScript
        $content = preg_replace_callback(
            '/<script[^>]*>(.*?)<\/script>/is',
            function($matches) {
                // Skip if script has src attribute
                if (strpos($matches[0], 'src=') !== false) {
                    return $matches[0];
                }

                $minified = $this->minify_js($matches[1]);
                return str_replace($matches[1], $minified, $matches[0]);
            },
            $content
        );

        return $content;
    }





    /**
     * Update minification statistics
     */
    public function update_stats($original_size, $minified_size) {
        $current_stats = get_option('redco_minifier_stats', array(
            'original_size' => 0,
            'minified_size' => 0,
            'bytes_saved' => 0,
            'files_processed' => 0,
            'css_files' => 0,
            'js_files' => 0,
            'last_minified' => 0,
            'compression_ratio' => 0
        ));

        $current_stats['original_size'] += $original_size;
        $current_stats['minified_size'] += $minified_size;
        $current_stats['bytes_saved'] = max(0, $current_stats['original_size'] - $current_stats['minified_size']);
        $current_stats['files_processed'] = isset($current_stats['files_processed']) ? $current_stats['files_processed'] + 1 : 1;
        $current_stats['last_minified'] = time();

        // Calculate compression ratio
        if ($current_stats['original_size'] > 0) {
            $current_stats['compression_ratio'] = round((1 - ($current_stats['minified_size'] / $current_stats['original_size'])) * 100, 1);
        }

        update_option('redco_minifier_stats', $current_stats);
    }

    /**
     * Get comprehensive minification statistics
     */
    public function get_stats() {
        $stats = get_option('redco_minifier_stats', array(
            'original_size' => 0,
            'minified_size' => 0,
            'bytes_saved' => 0,
            'files_processed' => 0,
            'css_files' => 0,
            'js_files' => 0,
            'last_minified' => 0,
            'compression_ratio' => 0
        ));

        // Get real minified cache directory size
        $cache_dir = redco_get_cache_dir() . 'minified/';
        $cache_size_bytes = 0;
        $cached_files = 0;

        if (is_dir($cache_dir)) {
            $cache_size_bytes = $this->calculate_directory_size($cache_dir);
            $cached_files = $this->count_minified_files($cache_dir);
        }

        return array(
            'original_size' => $stats['original_size'],
            'original_size_formatted' => redco_format_bytes($stats['original_size']),
            'minified_size' => $stats['minified_size'],
            'minified_size_formatted' => redco_format_bytes($stats['minified_size']),
            'bytes_saved' => $stats['bytes_saved'],
            'bytes_saved_formatted' => redco_format_bytes($stats['bytes_saved']),
            'files_processed' => max($stats['files_processed'], $cached_files),
            'files_minified' => $cached_files, // Backward compatibility
            'compression_ratio' => $stats['compression_ratio'],
            'cache_size' => redco_format_bytes($cache_size_bytes),
            'cache_size_bytes' => $cache_size_bytes,
            'cached_files' => $cached_files,
            'last_minified' => $stats['last_minified'],
            'enabled' => redco_is_module_enabled('css-js-minifier'),
            'css_enabled' => $this->settings['minify_css'],
            'js_enabled' => $this->settings['minify_js'],
            'performance_impact' => $this->calculate_performance_impact($stats)
        );
    }

    /**
     * Calculate directory size recursively
     */
    private function calculate_directory_size($directory) {
        $size = 0;

        if (!is_dir($directory)) {
            return 0;
        }

        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $size += $file->getSize();
            }
        }

        return $size;
    }

    /**
     * Count minified files
     */
    private function count_minified_files($directory) {
        $count = 0;

        if (!is_dir($directory)) {
            return 0;
        }

        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && in_array($file->getExtension(), array('css', 'js'))) {
                $count++;
            }
        }

        return $count;
    }

    /**
     * Calculate performance impact
     */
    private function calculate_performance_impact($stats) {
        if ($stats['files_processed'] === 0) {
            return array(
                'status' => 'no_data',
                'message' => 'No files minified yet',
                'bandwidth_saved' => 0
            );
        }

        $bandwidth_saved = $stats['bytes_saved'];
        $compression_ratio = $stats['compression_ratio'];

        $status = 'good';
        if ($compression_ratio < 10) {
            $status = 'poor';
        } elseif ($compression_ratio < 20) {
            $status = 'fair';
        }

        return array(
            'status' => $status,
            'message' => "Minification is working well with {$compression_ratio}% size reduction",
            'bandwidth_saved' => $bandwidth_saved,
            'bandwidth_saved_formatted' => redco_format_bytes($bandwidth_saved),
            'estimated_requests_faster' => $stats['files_processed']
        );
    }

    /**
     * Clear minified cache
     */
    public function clear_cache() {
        $cache_dir = redco_get_cache_dir() . 'minified/';
        $success = true;

        if (is_dir($cache_dir)) {
            $success = redco_clear_directory_recursive($cache_dir, false); // Don't remove the main directory
        }

        // Reset minification statistics
        update_option('redco_minifier_stats', array(
            'original_size' => 0,
            'minified_size' => 0,
            'bytes_saved' => 0,
            'files_processed' => 0
        ));

        return $success;
    }

    /**
     * AJAX handler for clearing minified cache
     */
    public function ajax_clear_cache() {
        check_ajax_referer('redco_optimizer_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'redco-optimizer'));
        }

        $result = $this->clear_cache();

        if ($result) {
            wp_send_json_success(array(
                'message' => __('Minified cache cleared successfully', 'redco-optimizer')
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to clear minified cache', 'redco-optimizer')
            ));
        }
    }
}

// Initialize the module only if enabled and after init hook
function redco_init_css_js_minifier() {
    if (redco_is_module_enabled('css-js-minifier')) {
        new Redco_CSS_JS_Minifier();
    }
}
add_action('init', 'redco_init_css_js_minifier', 10);
