/**
 * Frontend Diagnostics for Redco Optimizer
 * Detects and reports frontend performance issues
 */

(function() {
    'use strict';
    
    // Only run diagnostics if explicitly enabled
    if (!window.redcoDebugMode) {
        return;
    }
    
    const RedcoFrontendDiagnostics = {
        
        issues: [],
        startTime: performance.now(),
        
        init: function() {
            console.log('🔍 Redco Frontend Diagnostics Started');
            
            // Check for admin assets on frontend
            this.checkAdminAssets();
            
            // Check for external dependencies
            this.checkExternalDependencies();
            
            // Monitor performance
            this.monitorPerformance();
            
            // Check for JavaScript errors
            this.monitorErrors();
            
            // Report findings after page load
            window.addEventListener('load', () => {
                setTimeout(() => this.generateReport(), 1000);
            });
        },
        
        checkAdminAssets: function() {
            console.log('🔍 Checking for admin assets on frontend...');
            
            // Check stylesheets
            const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
            stylesheets.forEach(link => {
                const href = link.href || '';
                if (href.includes('redco-optimizer') && 
                    (href.includes('admin') || href.includes('enhanced-ui') || href.includes('modules'))) {
                    this.addIssue('critical', 'Admin CSS on Frontend', {
                        file: href,
                        element: link
                    });
                    console.error('❌ Admin CSS loading on frontend:', href);
                }
            });
            
            // Check scripts
            const scripts = document.querySelectorAll('script[src]');
            scripts.forEach(script => {
                const src = script.src || '';
                if (src.includes('redco-optimizer') && 
                    (src.includes('admin') || src.includes('chart') || src.includes('modules'))) {
                    this.addIssue('critical', 'Admin JS on Frontend', {
                        file: src,
                        element: script
                    });
                    console.error('❌ Admin JS loading on frontend:', src);
                }
            });
        },
        
        checkExternalDependencies: function() {
            console.log('🔍 Checking for external dependencies...');
            
            const scripts = document.querySelectorAll('script[src]');
            scripts.forEach(script => {
                const src = script.src || '';
                if (src.includes('cdnjs.cloudflare.com') || 
                    src.includes('googleapis.com') ||
                    src.includes('chart.js')) {
                    this.addIssue('warning', 'External CDN Dependency', {
                        file: src,
                        element: script
                    });
                    console.warn('⚠️ External dependency found:', src);
                }
            });
        },
        
        monitorPerformance: function() {
            console.log('🔍 Monitoring performance...');
            
            // Check for slow loading resources
            window.addEventListener('load', () => {
                if (performance.getEntriesByType) {
                    const resources = performance.getEntriesByType('resource');
                    resources.forEach(resource => {
                        if (resource.name.includes('redco-optimizer') && resource.duration > 500) {
                            this.addIssue('warning', 'Slow Loading Resource', {
                                file: resource.name,
                                duration: resource.duration + 'ms'
                            });
                            console.warn('⚠️ Slow resource:', resource.name, resource.duration + 'ms');
                        }
                    });
                }
                
                // Check total page load time
                const loadTime = performance.now() - this.startTime;
                if (loadTime > 3000) {
                    this.addIssue('warning', 'Slow Page Load', {
                        duration: loadTime + 'ms'
                    });
                    console.warn('⚠️ Slow page load:', loadTime + 'ms');
                }
            });
        },
        
        monitorErrors: function() {
            console.log('🔍 Monitoring JavaScript errors...');
            
            window.addEventListener('error', (event) => {
                if (event.filename && event.filename.includes('redco-optimizer')) {
                    this.addIssue('critical', 'JavaScript Error', {
                        message: event.message,
                        file: event.filename,
                        line: event.lineno,
                        column: event.colno
                    });
                    console.error('❌ Redco JS Error:', event.message, 'at', event.filename + ':' + event.lineno);
                }
            });
            
            // Monitor unhandled promise rejections
            window.addEventListener('unhandledrejection', (event) => {
                this.addIssue('warning', 'Unhandled Promise Rejection', {
                    reason: event.reason
                });
                console.warn('⚠️ Unhandled promise rejection:', event.reason);
            });
        },
        
        addIssue: function(severity, title, details) {
            this.issues.push({
                severity: severity,
                title: title,
                details: details,
                timestamp: Date.now()
            });
        },
        
        generateReport: function() {
            console.log('📊 Generating Redco Frontend Diagnostics Report...');
            
            if (this.issues.length === 0) {
                console.log('✅ No frontend issues detected!');
                return;
            }
            
            console.group('🚨 Redco Frontend Issues Detected (' + this.issues.length + ')');
            
            const criticalIssues = this.issues.filter(issue => issue.severity === 'critical');
            const warningIssues = this.issues.filter(issue => issue.severity === 'warning');
            
            if (criticalIssues.length > 0) {
                console.group('❌ Critical Issues (' + criticalIssues.length + ')');
                criticalIssues.forEach(issue => {
                    console.error(issue.title, issue.details);
                });
                console.groupEnd();
            }
            
            if (warningIssues.length > 0) {
                console.group('⚠️ Warning Issues (' + warningIssues.length + ')');
                warningIssues.forEach(issue => {
                    console.warn(issue.title, issue.details);
                });
                console.groupEnd();
            }
            
            console.groupEnd();
            
            // Send report to admin if AJAX is available
            if (window.jQuery && window.ajaxurl) {
                this.sendReportToAdmin();
            }
            
            // Display visual notification
            this.showVisualReport();
        },
        
        sendReportToAdmin: function() {
            jQuery.post(ajaxurl, {
                action: 'redco_frontend_diagnostics_report',
                issues: JSON.stringify(this.issues),
                url: window.location.href,
                user_agent: navigator.userAgent
            });
        },
        
        showVisualReport: function() {
            if (this.issues.length === 0) return;
            
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #ff4444;
                color: white;
                padding: 15px;
                border-radius: 5px;
                z-index: 999999;
                font-family: Arial, sans-serif;
                font-size: 14px;
                max-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            `;
            
            notification.innerHTML = `
                <strong>🚨 Redco Performance Issues</strong><br>
                ${this.issues.length} issues detected<br>
                <small>Check browser console for details</small>
                <button onclick="this.parentNode.remove()" style="float: right; background: none; border: none; color: white; cursor: pointer;">×</button>
            `;
            
            document.body.appendChild(notification);
            
            // Auto-remove after 10 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 10000);
        }
    };
    
    // Initialize diagnostics
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => RedcoFrontendDiagnostics.init());
    } else {
        RedcoFrontendDiagnostics.init();
    }
    
    // Make available globally for manual testing
    window.RedcoFrontendDiagnostics = RedcoFrontendDiagnostics;
    
})();
