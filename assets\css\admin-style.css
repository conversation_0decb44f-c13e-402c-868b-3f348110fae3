/**
 * Admin styles for Redco Optimizer
 * PERFORMANCE OPTIMIZED: Professional, modern responsive grid layout with enhanced UI/UX
 * Optimized for Core Web Vitals and reduced render-blocking
 */

/* CSS Variables for consistent theming */
:root {
    --redco-primary: #4CAF50;
    --redco-primary-dark: #388E3C;
    --redco-primary-light: #66BB6A;
    --redco-secondary: #64748b;
    --redco-success: #4CAF50;
    --redco-warning: #f59e0b;
    --redco-danger: #ef4444;
    --redco-info: #4CAF50;
    --redco-light: #f8fafc;
    --redco-dark: #1e293b;
    --redco-border: #e2e8f0;
    --redco-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --redco-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --redco-radius: 0px;
    --redco-radius-lg: 0px;
    --redco-transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    /* WordPress Admin Bar Height - Standard WordPress CSS Custom Property */
    --wp-admin--admin-bar--height: 32px;
}

/* WordPress Admin Notice Spacing */
.wrap {
    position: relative;
}

/* Ensure plugin content starts after WordPress notices */
.wrap .redco-optimizer-admin,
.wrap .redco-optimizer-modules,
.wrap .redco-optimizer-settings {
    clear: both;
    margin-top: 20px;
}

/* Additional spacing when notices are present */
.wrap .notice + .redco-optimizer-admin,
.wrap .notice + .redco-optimizer-modules,
.wrap .notice + .redco-optimizer-settings,
.wrap .updated + .redco-optimizer-admin,
.wrap .updated + .redco-optimizer-modules,
.wrap .updated + .redco-optimizer-settings,
.wrap .error + .redco-optimizer-admin,
.wrap .error + .redco-optimizer-modules,
.wrap .error + .redco-optimizer-settings {
    margin-top: 10px;
}

/* Main container with responsive grid - adjusted for no header */
.redco-optimizer-admin {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    border-radius: 0;
    overflow: hidden;
    box-shadow: none;
}

/* Admin container - full height without header */
.redco-admin-container {
    display: flex;
    min-height: 100vh;
    height: 100%;
}

/* Enhanced Professional Sidebar Header Section */
.redco-sidebar-header {
    background: linear-gradient(135deg, var(--redco-primary) 0%, var(--redco-primary-dark) 100%);
    color: #fff;
    padding: 20px 20px 20px 20px;
    margin: 0;
    border-radius: 0 0 0 16px;
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex-shrink: 0;
    min-height: 182px;
}

/* Header Background Pattern - Same as Dashboard */
.redco-sidebar-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
}

.redco-sidebar-header::after {
    content: "";
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
}

/* Enhanced Logo and Branding Section */
.sidebar-logo-section {
    width: 100%;
    margin-bottom: 12px;
    margin-top: 0;
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.logo-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 6px;
    padding: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.logo-container:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.sidebar-plugin-logo {
    width: 140px;
    height: 56px;
    border-radius: 0;
    background: none;
    padding: 0;
    box-shadow: none;
    transition: all 0.3s ease;
    border: none;
    display: block;
}

.sidebar-plugin-logo:hover {
    transform: scale(1.01);
}

.logo-overlay {
    position: absolute;
    top: -8px;
    right: -8px;
}

.version-badge {
    background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
    color: #fff;
    font-size: 10px;
    font-weight: 700;
    padding: 4px 8px;
    border-radius: 12px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.9);
    line-height: 1;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}



/* Enhanced Compact Stats & Actions Row */
.sidebar-compact-row {
    display: flex;
    gap: 6px;
    margin-bottom: 0;
    position: relative;
    z-index: 1;
    width: 100%;
    justify-content: center;
}

/* Enhanced Compact Modules Stat */
.compact-modules-stat {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.25);
    border-radius: 10px;
    padding: 8px 10px;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    flex: 1;
    min-width: 0;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.compact-modules-stat::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.compact-modules-stat:hover::before {
    left: 100%;
}

.compact-modules-stat:hover {
    background: rgba(255, 255, 255, 0.22);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    border-color: rgba(255, 255, 255, 0.35);
}

.compact-modules-stat:active {
    transform: translateY(-1px);
}

.compact-stat-icon {
    width: 24px;
    height: 24px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.25);
    flex-shrink: 0;
    position: relative;
    z-index: 1;
}

.compact-stat-icon .dashicons {
    font-size: 12px;
    color: #fff;
    width: 12px;
    height: 12px;
}

.compact-stat-content {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    position: relative;
    z-index: 1;
}

.compact-stat-value {
    display: block;
    font-size: 12px;
    font-weight: 700;
    color: #fff;
    line-height: 1.1;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.compact-stat-label {
    display: block;
    font-size: 8px;
    color: rgba(255, 255, 255, 0.85);
    font-weight: 500;
    margin-top: 1px;
    text-transform: uppercase;
    letter-spacing: 0.2px;
}



/* Enhanced Professional Help & Support Button */
.enhanced-help-trigger {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.25);
    color: #fff;
    padding: 8px 10px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    text-align: left;
    flex: 1;
    min-width: 0;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.enhanced-help-trigger::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.enhanced-help-trigger:hover::before {
    left: 100%;
}

.enhanced-help-trigger:hover {
    background: rgba(255, 255, 255, 0.22);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    border-color: rgba(255, 255, 255, 0.35);
}

.enhanced-help-trigger:active {
    transform: translateY(-1px);
}

.help-icon-wrapper {
    width: 24px;
    height: 24px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.25);
    flex-shrink: 0;
    position: relative;
    z-index: 1;
}

.help-icon-wrapper .dashicons {
    font-size: 12px;
    color: #fff;
    width: 12px;
    height: 12px;
}

.help-content {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    position: relative;
    z-index: 1;
}

.help-title {
    font-size: 12px;
    font-weight: 700;
    color: #fff;
    line-height: 1.1;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.help-subtitle {
    font-size: 8px;
    color: rgba(255, 255, 255, 0.85);
    font-weight: 500;
    margin-top: 1px;
    text-transform: uppercase;
    letter-spacing: 0.2px;
}



/* Enhanced Sidebar Header Animations */
@keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 8px rgba(76, 175, 80, 0.5); }
    50% { box-shadow: 0 0 16px rgba(76, 175, 80, 0.8); }
}

@keyframes stat-update {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); background: rgba(255, 255, 255, 0.3); }
    100% { transform: scale(1); }
}

/* Focus States for Accessibility */
.compact-modules-stat:focus,
.enhanced-help-trigger:focus {
    outline: 2px solid rgba(255, 255, 255, 0.8);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.2);
}

.sidebar-plugin-logo:focus {
    outline: 2px solid rgba(255, 255, 255, 0.8);
    outline-offset: 4px;
}

/* Remove blue focus outline from navigation tabs */
.redco-nav a:focus,
.redco-nav li:focus,
.redco-nav li a:focus {
    outline: none !important;
    box-shadow: none !important;
}

.status-dot.active {
    animation: pulse-glow 2s infinite;
}

.sidebar-stat-card .stat-value.updated {
    animation: stat-update 0.6s ease;
}

/* Hover effects for interactive elements */
.sidebar-plugin-logo {
    cursor: pointer;
}

.sidebar-stat-card {
    cursor: pointer;
}

.sidebar-stat-card:active {
    transform: translateY(1px);
}

/* Loading state for sidebar stats */
.sidebar-stat-card.loading .stat-value {
    opacity: 0.6;
    position: relative;
}

.sidebar-stat-card.loading .stat-value::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 12px;
    height: 12px;
    margin: -6px 0 0 -6px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top-color: #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Remove old header styles */
.redco-optimizer-admin .redco-header {
    display: none !important;
}

/* Global Help Button */
.redco-help-trigger {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #fff;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    transition: var(--redco-transition);
    font-size: 13px;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.redco-help-trigger:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}

.redco-help-trigger .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.redco-help-trigger .help-text {
    font-weight: 500;
}

.redco-optimizer-admin .stat-item {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    text-align: center !important;
}

/* REMOVED: White text overrides that caused white-on-white issues
   All modules now use standardized colors from module-layout-standard.css */

/* Responsive Grid Container */
.redco-admin-container {
    display: grid;
    grid-template-columns: 300px 1fr;
    grid-template-rows: 1fr;
    gap: 0;
    max-width: 1600px;
    margin: 0 auto;
    padding: 0;
    min-height: calc(100vh - var(--wp-admin--admin-bar--height) - 200px);
    align-items: stretch;
}

/* Responsive behavior for smaller screens */
@media (max-width: 1200px) {
    .redco-admin-container {
        grid-template-columns: 280px 1fr;
        gap: 0;
        padding: 0;
    }
}

@media (max-width: 768px) {
    :root {
        /* WordPress Admin Bar Height on Mobile */
        --wp-admin--admin-bar--height: 46px;
    }

    .redco-admin-container {
        grid-template-columns: 1fr;
        gap: 0;
        padding: 0;
        min-height: auto;
    }

    .redco-sidebar {
        position: relative;
        top: auto;
        min-height: auto;
        height: auto;
        margin-top: 0;
    }

    /* Mobile responsive for enhanced sidebar header */
    .redco-sidebar-header {
        padding: 20px 16px 16px 16px;
        margin: 0 0 16px 0;
        border-radius: var(--redco-radius) var(--redco-radius) 0 0;
    }

    .sidebar-logo-section {
        margin-bottom: 16px;
    }

    .logo-container {
        margin-bottom: 12px;
    }

    .sidebar-plugin-logo {
        width: 85px;
        height: 54px;
        padding: 6px 10px;
    }



    .sidebar-compact-row {
        gap: 6px;
        flex-direction: column;
        align-items: center;
    }

    .compact-modules-stat,
    .enhanced-help-trigger {
        padding: 10px 12px;
        max-width: 200px;
        width: 100%;
    }

    .compact-stat-icon,
    .help-icon-wrapper {
        width: 28px;
        height: 28px;
    }

    .compact-stat-icon .dashicons,
    .help-icon-wrapper .dashicons {
        font-size: 14px;
        width: 14px;
        height: 14px;
    }

    .compact-stat-value,
    .help-title {
        font-size: 13px;
    }

    .compact-stat-label,
    .help-subtitle {
        font-size: 9px;
    }

    .version-badge {
        font-size: 8px;
        padding: 2px 5px;
        top: -4px;
        right: -4px;
    }
}

/* Enhanced Professional Navigation Sidebar */
.redco-sidebar {
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid var(--redco-border);
    border-radius: var(--redco-radius-lg) 0 var(--redco-radius-lg) var(--redco-radius-lg);
    position: sticky;
    top: 0;
    height: fit-content;
    min-height: calc(100vh - var(--wp-admin--admin-bar--height) - 40px);
    overflow: hidden;
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
    align-self: stretch;
    margin-top: 0;
    padding: 0;
    box-shadow: var(--redco-shadow-lg);
}

/* Universal Loading Splash Screen */
.redco-universal-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.95), rgba(67, 160, 71, 0.95));
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 99999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.5s ease;
}

.redco-universal-loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.redco-universal-loading-content {
    text-align: center;
    color: white;
    max-width: 500px;
    padding: 40px;
}

.redco-universal-loading-spinner {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto 30px;
}

.redco-universal-spinner-ring {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 4px solid transparent;
    border-radius: 50%;
    animation: spinRing 2s linear infinite;
}

.redco-universal-spinner-ring:nth-child(1) {
    border-top-color: rgba(255, 255, 255, 0.9);
    animation-delay: 0s;
}

.redco-universal-spinner-ring:nth-child(2) {
    border-right-color: rgba(255, 255, 255, 0.7);
    animation-delay: -0.5s;
    width: 80%;
    height: 80%;
    top: 10%;
    left: 10%;
}

.redco-universal-spinner-ring:nth-child(3) {
    border-bottom-color: rgba(255, 255, 255, 0.5);
    animation-delay: -1s;
    width: 60%;
    height: 60%;
    top: 20%;
    left: 20%;
}

.redco-universal-loading-text h3 {
    font-size: 1.8em;
    font-weight: 600;
    margin-bottom: 16px;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.redco-universal-loading-text p {
    font-size: 1.1em;
    line-height: 1.6;
    margin-bottom: 24px;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.redco-universal-loading-dots {
    display: flex;
    justify-content: center;
    gap: 8px;
}

.redco-universal-loading-dots span {
    width: 12px;
    height: 12px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: loadingDots 1.5s ease-in-out infinite;
}

.redco-universal-loading-dots span:nth-child(1) {
    animation-delay: 0s;
}

.redco-universal-loading-dots span:nth-child(2) {
    animation-delay: 0.3s;
}

.redco-universal-loading-dots span:nth-child(3) {
    animation-delay: 0.6s;
}

/* Loading animations */
@keyframes spinRing {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes loadingDots {
    0%, 20% { opacity: 0; }
    50% { opacity: 1; }
    100% { opacity: 0; }
}



/* Content Area with Grid Layout */
.redco-content {
    display: grid;
    gap: 24px;
    background: transparent;
    border: none;
    border-left: none;
    box-shadow: none;
    padding: 0;
    margin: 0;
    margin-top: 0;
    min-height: 100%;
    align-self: stretch;
}

/* Enhanced Sidebar Navigation */

.redco-nav {
    padding: 0px 40px 20px 0px;
    margin: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    border-right: none;
    min-height: 100%;
    width: 100%;
}

.redco-nav ul {
    margin: 0;
    padding: 0;
    list-style: none;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.redco-nav li {
    margin: 0;
    width: 100%;
    position: relative;
}

.redco-nav li:not(:last-child)::after {
    content: "";
    position: absolute;
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 1px;
    background: rgba(0, 0, 0, 0.1);
}

/* Add spacing at the bottom of navigation to fill remaining space */
.redco-nav ul::after {
    content: "";
    flex: 1;
    min-height: 20px;
}

/* Help System Styles */
.redco-help-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    margin-left: 8px;
    cursor: pointer;
    color: var(--redco-secondary);
    transition: var(--redco-transition);
    border-radius: 50%;
    background: rgba(56, 142, 60, 0.1);
    vertical-align: middle;
}

.redco-help-icon:hover {
    color: var(--redco-primary);
    background: rgba(56, 142, 60, 0.2);
    transform: scale(1.1);
}

.redco-help-icon .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
    line-height: 1;
}

/* Help Tooltip */
.redco-tooltip {
    position: absolute;
    background: var(--redco-dark);
    color: #fff;
    padding: 8px 12px;
    border-radius: var(--redco-radius);
    font-size: 12px;
    line-height: 1.4;
    max-width: 250px;
    z-index: 10000;
    opacity: 0;
    transform: translateY(-5px);
    transition: all 0.2s ease;
    box-shadow: var(--redco-shadow-lg);
}

.redco-tooltip.visible {
    opacity: 1;
    transform: translateY(0);
}

.redco-tooltip::before {
    content: "";
    position: absolute;
    top: -5px;
    left: 50%;
    transform: translateX(-50%);
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: 5px solid var(--redco-dark);
}

/* Help Panel Overlay */
.redco-help-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.redco-help-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Help Panel */
.redco-help-panel {
    position: fixed;
    top: 0;
    right: -450px;
    width: 450px;
    height: 100vh;
    background: #fff;
    z-index: 9999;
    box-shadow: -2px 0 20px rgba(0, 0, 0, 0.1);
    transition: right 0.3s ease;
    display: flex;
    flex-direction: column;
}

.redco-help-panel.active {
    right: 0;
}

/* Help Panel Header */
.help-panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid var(--redco-border);
    background: var(--redco-primary);
    color: #fff;
}

.help-panel-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.help-panel-close {
    background: none;
    border: none;
    color: #fff;
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    transition: var(--redco-transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.help-panel-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.help-panel-close .dashicons {
    font-size: 18px;
    width: 18px;
    height: 18px;
}

/* Help Panel Search */
.help-panel-search {
    position: relative;
    padding: 16px 24px;
    border-bottom: 1px solid var(--redco-border);
    background: var(--redco-light);
}

.help-panel-search input {
    width: 100%;
    padding: 10px 40px 10px 16px;
    border: 1px solid var(--redco-border);
    border-radius: var(--redco-radius);
    font-size: 14px;
    background: #fff;
}

.help-panel-search input:focus {
    outline: none;
    border-color: var(--redco-primary);
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

.help-panel-search .dashicons {
    position: absolute;
    right: 36px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--redco-secondary);
    font-size: 16px;
}

/* Help Panel Content */
.help-panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

.help-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 60px 24px;
    color: var(--redco-secondary);
    text-align: center;
}

.help-loading .dashicons {
    font-size: 32px;
    margin-bottom: 12px;
    color: var(--redco-primary);
}

.help-loading .spinning {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* PageSpeed Scores Header Actions */
.scores-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 20px;
}

.scores-header-content {
    flex: 1;
}

.scores-header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-left: 20px;
}

.refresh-pagespeed-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: var(--redco-primary);
    border: none;
    border-radius: 50%;
    color: #fff;
    cursor: pointer;
    transition: var(--redco-transition);
    box-shadow: var(--redco-shadow);
}

.refresh-pagespeed-btn:hover:not(:disabled) {
    background: var(--redco-primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--redco-shadow-lg);
}

.refresh-pagespeed-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    opacity: 0.6;
}

.refresh-pagespeed-btn .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    transition: transform 0.3s ease;
}

.refresh-pagespeed-btn .dashicons.spin {
    animation: spin 1s linear infinite;
}

.last-updated {
    font-size: 12px;
    color: var(--redco-secondary);
    background: rgba(255, 255, 255, 0.8);
    padding: 6px 12px;
    border-radius: 16px;
    border: 1px solid var(--redco-border);
    white-space: nowrap;
}

.api-key-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: var(--redco-warning);
    border: none;
    border-radius: 50%;
    color: #fff;
    text-decoration: none;
    transition: var(--redco-transition);
    box-shadow: var(--redco-shadow);
}

.api-key-link:hover {
    background: #e68900;
    transform: translateY(-1px);
    box-shadow: var(--redco-shadow-lg);
    color: #fff;
}

.api-key-link .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Device Toggle */
.device-toggle {
    display: flex;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid var(--redco-border);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--redco-shadow);
}

.device-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    background: transparent;
    border: none;
    color: var(--redco-secondary);
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--redco-transition);
    white-space: nowrap;
}

.device-btn:hover {
    background: rgba(76, 175, 80, 0.1);
    color: var(--redco-primary);
}

.device-btn.active {
    background: var(--redco-primary);
    color: #fff;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.device-btn .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

/* Device Toggle Disabled State During Auto-Refresh */
.device-toggle.auto-refreshing {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.device-toggle.auto-refreshing::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 20px;
    cursor: not-allowed;
    z-index: 1;
}

.device-btn:disabled,
.device-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.device-btn:disabled:hover,
.device-btn.disabled:hover {
    background: transparent;
    color: var(--redco-secondary);
}

.device-btn.active:disabled,
.device-btn.active.disabled {
    background: var(--redco-primary);
    color: #fff;
    opacity: 0.8;
}

/* Ensure active state is preserved during auto-refresh */
.device-toggle.auto-refreshing .device-btn.active {
    background: var(--redco-primary);
    color: #fff;
    opacity: 0.8;
}

/* Auto-refresh tooltip */
.device-toggle-tooltip {
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--redco-dark);
    color: #fff;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
}

.device-toggle-tooltip::before {
    content: "";
    position: absolute;
    top: -4px;
    left: 50%;
    transform: translateX(-50%);
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid var(--redco-dark);
}

.device-toggle.auto-refreshing .device-toggle-tooltip {
    opacity: 1;
    visibility: visible;
}

/* Device Toggle Loading States */
.device-btn.loading {
    position: relative;
    overflow: hidden;
}

.device-btn.loading::after {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: deviceToggleLoading 1.5s infinite;
}

@keyframes deviceToggleLoading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Score Loading States */
.score-number.loading-scores {
    position: relative;
}

.score-number.loading-scores::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(76, 175, 80, 0.1) 50%, transparent 70%);
    animation: scoreLoading 2s infinite;
    border-radius: 4px;
}

@keyframes scoreLoading {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.device-btn:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.device-btn:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.help-error {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 60px 24px;
    color: var(--redco-danger);
    text-align: center;
}

.help-error .dashicons {
    font-size: 32px;
    margin-bottom: 12px;
}

/* Help Sections */
.help-section {
    padding: 24px;
    border-bottom: 1px solid var(--redco-border);
}

.help-section:last-child {
    border-bottom: none;
}

.help-section h4 {
    margin: 0 0 12px 0;
    color: var(--redco-dark);
    font-size: 16px;
    font-weight: 600;
}

.help-section h5 {
    margin: 16px 0 8px 0;
    color: var(--redco-dark);
    font-size: 14px;
    font-weight: 600;
}

.help-section p {
    margin: 0 0 16px 0;
    color: var(--redco-secondary);
    line-height: 1.5;
}

.help-section p:last-child {
    margin-bottom: 0;
}

/* Help Lists */
.help-list {
    margin: 16px 0;
}

.help-list ul {
    margin: 8px 0 0 0;
    padding-left: 20px;
}

.help-list li {
    margin-bottom: 6px;
    color: var(--redco-secondary);
    line-height: 1.4;
}

.help-list.help-warning {
    background: rgba(245, 158, 11, 0.1);
    border-left: 4px solid var(--redco-warning);
    padding: 12px 16px;
    border-radius: 0 var(--redco-radius) var(--redco-radius) 0;
}

.help-list.help-safety {
    background: rgba(239, 68, 68, 0.1);
    border-left: 4px solid var(--redco-danger);
    padding: 12px 16px;
    border-radius: 0 var(--redco-radius) var(--redco-radius) 0;
}

.help-list.help-benefits {
    background: rgba(34, 197, 94, 0.1);
    border-left: 4px solid var(--redco-success);
    padding: 12px 16px;
    border-radius: 0 var(--redco-radius) var(--redco-radius) 0;
}

.help-field-specific {
    background: var(--redco-light);
    padding: 16px;
    border-radius: var(--redco-radius);
    margin: 16px 0;
    border-left: 4px solid var(--redco-primary);
}

.help-metrics ul {
    list-style: none;
    padding-left: 0;
}

.help-metrics li {
    padding: 8px 0;
    border-bottom: 1px solid var(--redco-border);
}

.help-metrics li:last-child {
    border-bottom: none;
}

.help-no-results {
    padding: 40px 24px;
    text-align: center;
    color: var(--redco-secondary);
}

/* Body class when help panel is open */
body.help-panel-open {
    overflow: hidden;
}

/* Responsive help panel */
@media (max-width: 768px) {
    .redco-help-panel {
        width: 100%;
        right: -100%;
    }

    .help-panel-header {
        padding: 16px 20px;
    }

    .help-panel-search {
        padding: 12px 20px;
    }

    .help-section {
        padding: 20px;
    }
}

/* Enhanced Professional Navigation Links */
.redco-nav a {
    display: flex;
    align-items: center;
    padding: 16px 8px;
    text-decoration: none;
    color: var(--redco-dark);
    font-weight: 500;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
    min-height: 56px;
    flex-shrink: 0;
    width: 100%;
    margin-bottom: 4px;
    outline: none !important;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.redco-nav a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(76, 175, 80, 0.05), transparent);
    transition: left 0.5s ease;
}

.redco-nav a:hover::before {
    left: 100%;
}

.redco-nav a:focus {
    outline: none !important;
    box-shadow: none !important;
}

.redco-nav a:hover {
    background: rgba(76, 175, 80, 0.08);
    color: var(--redco-primary);
    border-left-color: var(--redco-primary);
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.15);
}

.redco-nav li.active a {
    background: linear-gradient(135deg, var(--redco-primary) 0%, var(--redco-primary-dark) 100%);
    color: #fff;
    font-weight: 600;
    border-left-color: var(--redco-primary-dark);
    position: relative;
    width: 100%;
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.25);
}

.redco-nav li.active a::after {
    content: "";
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-right: 8px solid #fff;
}

.redco-nav li.active a:hover {
    background: linear-gradient(135deg, var(--redco-primary-dark) 0%, var(--redco-primary) 100%);
    box-shadow: 0 6px 16px rgba(76, 175, 80, 0.3);
}

/* Enhanced Professional Navigation Icons */
.redco-nav .dashicons {
    margin-left: 16px;
    margin-right: 16px;
    width: 20px;
    height: 20px;
    font-size: 20px;
    opacity: 0.8;
    flex-shrink: 0;
    transition: all 0.3s ease;
    color: var(--redco-secondary);
}

.redco-nav a:hover .dashicons {
    opacity: 1;
    color: var(--redco-primary);
    transform: scale(1.1);
}

.redco-nav li.active .dashicons {
    opacity: 1;
    color: white;
    transform: scale(1.05);
}

/* Enhanced Professional Navigation Content */
.nav-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-width: 0;
    gap: 2px;
}

.nav-title {
    font-size: 20px;
    font-weight: 600;
    line-height: 1.2;
    margin: 0;
    color: inherit;
    transition: all 0.3s ease;
}

.nav-description {
    font-size: 12px;
    opacity: 0.7;
    line-height: 1.3;
    font-weight: 400;
    color: var(--redco-secondary);
    margin: 0;
    transition: all 0.3s ease;
}

.redco-nav a:hover .nav-description {
    opacity: 1;
    color: var(--redco-primary);
}

.redco-nav li.active .nav-description {
    opacity: 0.9;
    color: rgba(255, 255, 255, 0.9) !important;
}

.coming-soon-badge {
    background: linear-gradient(135deg, var(--redco-warning), #fb923c);
    color: #fff;
    font-size: 9px;
    padding: 3px 8px;
    border-radius: 12px;
    margin-left: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 6px rgba(245, 158, 11, 0.4);
    flex-shrink: 0;
    align-self: flex-start;
}

/* Dashboard Grid Layout */
.redco-dashboard {
    display: grid;
    gap: 24px;
}

.redco-dashboard h1,
.redco-dashboard h2 {
    margin-top: 0;
    color: var(--redco-dark);
    font-weight: 600;
}

/* Performance Overview */
.redco-performance-overview {
    background: #fff;
    border-radius: var(--redco-radius-lg);
    padding: 32px;
    box-shadow: var(--redco-shadow);
    border: 1px solid var(--redco-border);
    margin-bottom: 32px;
}

.redco-performance-overview h2 {
    margin: 0 0 24px 0;
    color: var(--redco-dark);
    font-size: 24px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 12px;
}

.redco-performance-overview h2::before {
    content: "\f239";
    font-family: dashicons;
    font-size: 28px;
}

/* Performance Indicator */
.performance-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: auto;
    font-size: 12px;
    color: var(--redco-secondary);
}

.indicator-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--redco-success);
    animation: pulse-dot 2s infinite;
}

.indicator-dot.loading {
    background: var(--redco-warning);
    animation: pulse-loading 1s infinite;
}

.indicator-text {
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.performance-refresh-btn {
    background: none;
    border: 1px solid var(--redco-border);
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--redco-transition);
    color: var(--redco-secondary);
}

.performance-refresh-btn:hover {
    background: var(--redco-primary);
    border-color: var(--redco-primary);
    color: white;
}

.performance-refresh-btn .dashicons {
    font-size: 14px;
}

/* Performance Timestamp */
.performance-timestamp {
    text-align: center;
    font-size: 11px;
    color: var(--redco-secondary);
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid var(--redco-border);
}

/* Pulse Animations */
@keyframes pulse-dot {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes pulse-loading {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.7; }
}

.performance-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
}

.performance-card {
    background: #ffffff;
    border: 1px solid var(--redco-border);
    border-radius: var(--redco-radius);
    padding: 24px;
    text-align: left;
    position: relative;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.performance-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border-color: var(--redco-border);
}

.performance-score {
    border-left: 3px solid var(--redco-primary);
}

.performance-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

.performance-header .dashicons {
    font-size: 18px;
    color: var(--redco-secondary);
    width: 18px;
    height: 18px;
}

.performance-header h3 {
    margin: 0;
    font-size: 13px;
    font-weight: 500;
    color: var(--redco-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.performance-value {
    margin-bottom: 16px;
}

.score-number,
.metric-number {
    font-size: 28px;
    font-weight: 600;
    color: var(--redco-dark);
    line-height: 1;
}

.score-label,
.metric-unit {
    font-size: 14px;
    color: var(--redco-secondary);
    margin-left: 2px;
    font-weight: 400;
}

.performance-status,
.performance-trend {
    font-size: 9px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    padding: 3px 6px;
    border-radius: 6px;
    background: var(--redco-light);
    color: var(--redco-secondary);
    border: 1px solid var(--redco-border);
}

.performance-status.excellent,
.performance-trend.excellent {
    background: var(--redco-primary);
    color: #ffffff;
    border-color: var(--redco-primary-dark);
    font-size: 10px;
}

.performance-status.good,
.performance-trend.good {
    background: var(--redco-primary);
    color: #ffffff;
    border-color: var(--redco-primary-dark);
    font-size: 10px;
}

.performance-status.average,
.performance-trend.average {
    background: #d97706;
    color: #ffffff;
    border-color: #b45309;
    font-size: 10px;
}

.performance-status.poor,
.performance-trend.poor {
    background: #dc2626;
    color: #ffffff;
    border-color: #b91c1c;
    font-size: 10px;
}

/* Performance Disabled State */
.redco-performance-disabled {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #e0e0e0;
    border-radius: var(--redco-radius-lg);
    padding: 40px;
    margin-bottom: 32px;
    text-align: center;
    box-shadow: var(--redco-shadow);
}

.performance-disabled-content {
    max-width: 500px;
    margin: 0 auto;
}

.performance-disabled-content .dashicons {
    font-size: 64px;
    color: #ccc;
    margin-bottom: 20px;
}

.performance-disabled-content h3 {
    margin: 0 0 16px 0;
    color: var(--redco-dark);
    font-size: 24px;
    font-weight: 600;
}

.performance-disabled-content p {
    margin: 0 0 24px 0;
    color: var(--redco-secondary);
    font-size: 16px;
    line-height: 1.5;
}

.performance-disabled-content .button {
    background: linear-gradient(135deg, var(--redco-primary-light) 0%, var(--redco-primary) 100%) !important;
    border: none !important;
    color: white !important;
    padding: 12px 24px !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    border-radius: var(--redco-radius) !important;
    box-shadow: 0 4px 16px rgba(76, 175, 80, 0.3) !important;
    transition: var(--redco-transition) !important;
    text-decoration: none !important;
}

.performance-disabled-content .button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4) !important;
}

/* Plugin Disabled State */
.redco-plugin-disabled {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 2px solid #ffc107;
    border-radius: var(--redco-radius-lg);
    padding: 40px;
    margin-bottom: 32px;
    text-align: center;
    box-shadow: var(--redco-shadow);
}

.plugin-disabled-content {
    max-width: 500px;
    margin: 0 auto;
}

.plugin-disabled-content .dashicons {
    font-size: 64px;
    color: #f59e0b;
    margin-bottom: 20px;
}

.plugin-disabled-content h3 {
    margin: 0 0 16px 0;
    color: var(--redco-dark);
    font-size: 24px;
    font-weight: 600;
}

.plugin-disabled-content p {
    margin: 0 0 24px 0;
    color: #8b5a00;
    font-size: 16px;
    line-height: 1.5;
}

.plugin-disabled-content .button {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
    border: none !important;
    color: white !important;
    padding: 12px 24px !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    border-radius: var(--redco-radius) !important;
    box-shadow: 0 4px 16px rgba(245, 158, 11, 0.3) !important;
    transition: var(--redco-transition) !important;
    text-decoration: none !important;
}

.plugin-disabled-content .button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4) !important;
}

/* Enhanced Stats Cards Grid */
.redco-stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.stat-card {
    background: linear-gradient(135deg, #ffffff 0%, var(--redco-light) 100%);
    border: 2px solid var(--redco-border);
    padding: 24px;
    border-radius: var(--redco-radius-lg);
    box-shadow: var(--redco-shadow);
    transition: var(--redco-transition);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 16px;
}

.stat-card:hover {
    box-shadow: var(--redco-shadow-lg);
    border-color: var(--redco-primary);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.modules-stat .stat-icon {
    background: linear-gradient(135deg, var(--redco-primary), var(--redco-primary-light));
    color: white;
}

.cache-stat .stat-icon {
    background: linear-gradient(135deg, var(--redco-success), #34d399);
    color: white;
}

.optimization-stat .stat-icon {
    background: linear-gradient(135deg, var(--redco-info), #22d3ee);
    color: white;
}

.stat-content {
    flex: 1;
}

.stat-content h3 {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: var(--redco-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-number {
    font-size: 28px;
    font-weight: 700;
    color: var(--redco-dark);
    margin: 0;
}

.stat-total {
    font-size: 18px;
    color: var(--redco-secondary);
    font-weight: 500;
}

.stat-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, transparent 50%);
    pointer-events: none;
}

.stat-card:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.stat-card:nth-child(2) {
    background: linear-gradient(135deg, var(--redco-success) 0%, #34d399 100%);
}

.stat-card:nth-child(3) {
    background: linear-gradient(135deg, var(--redco-info) 0%, #22d3ee 100%);
}

.stat-card:nth-child(4) {
    background: linear-gradient(135deg, var(--redco-warning) 0%, #fbbf24 100%);
}

.stat-card h3 {
    margin: 0 0 12px 0;
    font-size: 14px;
    opacity: 0.9;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-number {
    font-size: 36px;
    font-weight: 700;
    margin: 0;
    position: relative;
    z-index: 1;
}

/* Quick Actions Grid */
.redco-quick-actions {
    background: #fff;
    border-radius: var(--redco-radius-lg);
    padding: 28px;
    box-shadow: var(--redco-shadow);
    border: 1px solid var(--redco-border);
    margin-bottom: 32px;
}

.redco-quick-actions h2 {
    margin: 0 0 20px 0;
    color: var(--redco-dark);
    font-size: 20px;
    font-weight: 600;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
}

.quick-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background: linear-gradient(135deg, var(--redco-light) 0%, #ffffff 100%);
    border: 2px solid var(--redco-border);
    border-radius: var(--redco-radius);
    text-decoration: none;
    color: var(--redco-secondary);
    font-weight: 500;
    transition: var(--redco-transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.quick-action-btn:hover {
    background: linear-gradient(135deg, rgba(56, 142, 60, 0.05) 0%, #ffffff 100%);
    border-color: var(--redco-primary);
    color: var(--redco-primary);
    box-shadow: 0 4px 12px rgba(56, 142, 60, 0.15);
}

.quick-action-btn .dashicons {
    margin-right: 10px;
    font-size: 18px;
}

/* Performance & Health Dashboard */
.redco-performance-health-dashboard {
    background: #fff;
    border-radius: 0 0 var(--redco-radius-lg) var(--redco-radius-lg);
    padding: 0;
    box-shadow: var(--redco-shadow);
    border: 1px solid var(--redco-border);
    border-top: none;
    margin-bottom: 32px;
    overflow: visible;
}

/* Dashboard now uses standardized module header - no duplicate CSS needed */

/* Dashboard header styles removed - now uses standardized module header */

/* All dashboard header action and metric styles removed - using standardized module header */

/* Ensure dashboard header has same height and layout as module headers */
.redco-performance-health-dashboard .module-header-section {
    min-height: 182px !important;
    margin: 0 -20px 30px -2px !important;
    border-radius: 0 !important;
    padding: 0 !important;
    background: linear-gradient(135deg, var(--redco-primary) 0%, var(--redco-primary-dark) 100%) !important;
    color: white !important;
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3) !important;
    position: relative !important;
    overflow: hidden !important;
}

/* Ensure dashboard header has the same background pattern as module headers */
.redco-performance-health-dashboard .module-header-section::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    right: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>') !important;
    opacity: 0.3 !important;
    pointer-events: none !important;
}

/* Override any responsive constraints that might limit dashboard header height */
.redco-performance-health-dashboard .header-content {
    max-height: none !important;
    padding: 20px 40px 20px 50px !important;
}

/* Ensure dashboard header breadcrumb and content areas don't have height constraints */
.redco-performance-health-dashboard .header-breadcrumb,
.redco-performance-health-dashboard .header-main,
.redco-performance-health-dashboard .header-actions {
    max-height: none !important;
}

/* Dashboard Grid Content Spacing */
.redco-performance-health-dashboard .dashboard-grid {
    padding: 28px;
}

/* ===== PROFESSIONAL PROGRESS MODAL SYSTEM ===== */

/* Modal Overlay */
.redco-progress-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.redco-progress-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* Modal Container */
.redco-progress-modal {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 8px 16px rgba(0, 0, 0, 0.1);
    width: 480px;
    max-width: 90vw;
    max-height: 85vh;
    overflow: hidden;
    transform: translateY(20px) scale(0.95);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.redco-progress-overlay.show .redco-progress-modal {
    transform: translateY(0) scale(1);
}

/* Modal Header */
.progress-modal-header {
    display: flex;
    align-items: center;
    padding: 24px 24px 20px 24px;
    border-bottom: 1px solid #e9ecef;
    position: relative;
}

.progress-icon {
    font-size: 24px;
    margin-right: 16px;
    flex-shrink: 0;
}

.progress-title-section {
    flex: 1;
    min-width: 0;
}

.progress-title {
    margin: 0 0 4px 0;
    font-size: 18px;
    font-weight: 600;
    color: #1d2327;
    line-height: 1.3;
}

.progress-description {
    margin: 0;
    font-size: 14px;
    color: #646970;
    line-height: 1.4;
}

.progress-close-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 32px;
    height: 32px;
    border: none;
    background: #f6f7f7;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #646970;
}

.progress-close-btn:hover {
    background: #dcdcde;
    color: #1d2327;
}

.progress-close-btn .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Progress Content */
.progress-content {
    padding: 20px 24px 24px 24px;
}

/* Progress Bar Section */
.progress-bar-section {
    margin-bottom: 20px;
    position: relative;
}

.progress-bar-container {
    background: #f0f0f1;
    border-radius: 8px;
    height: 8px;
    overflow: hidden;
    margin-bottom: 8px;
    position: relative;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50 0%, #66BB6A 100%);
    border-radius: 8px;
    width: 0%;
    transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: progress-shimmer 2s infinite;
}

@keyframes progress-shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-percentage {
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    color: #1d2327;
}

/* Progress Status Section */
.progress-status-section {
    margin-bottom: 20px;
    text-align: center;
}

.current-operation {
    font-size: 16px;
    font-weight: 500;
    color: #1d2327;
    margin-bottom: 6px;
    line-height: 1.3;
}

.operation-details {
    font-size: 13px;
    color: #646970;
    line-height: 1.4;
    font-style: italic;
}

/* Progress Statistics */
.progress-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-bottom: 20px;
    padding: 16px;
    background: #f6f7f7;
    border-radius: 8px;
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 11px;
    color: #646970;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
    font-weight: 500;
}

.stat-value {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #1d2327;
    line-height: 1.2;
}

/* Progress Actions */
.progress-actions {
    text-align: center;
    padding-top: 16px;
    border-top: 1px solid #e9ecef;
}

.progress-actions .button {
    min-width: 120px;
    height: 36px;
    border-radius: 6px;
    font-weight: 500;
}

.progress-actions .button-primary {
    background: #4CAF50;
    border-color: #4CAF50;
}

.progress-actions .button-primary:hover {
    background: #45a049;
    border-color: #45a049;
}

/* Progress Error */
.progress-error {
    text-align: center;
    padding: 20px 24px 24px 24px;
}

.error-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.8;
}

.error-message {
    font-size: 15px;
    color: #d63638;
    margin-bottom: 20px;
    line-height: 1.5;
    font-weight: 500;
}

.error-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
}

.error-actions .button {
    min-width: 100px;
    height: 36px;
    border-radius: 6px;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .redco-progress-modal {
        width: 95vw;
        margin: 8px;
    }

    .progress-modal-header {
        padding: 20px 20px 16px 20px;
    }

    .progress-content {
        padding: 16px 20px 20px 20px;
    }

    .progress-title {
        font-size: 16px;
    }

    .progress-description {
        font-size: 13px;
    }

    .progress-stats {
        grid-template-columns: 1fr;
        gap: 12px;
        padding: 12px;
    }

    .stat-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        text-align: left;
    }

    .stat-label {
        margin-bottom: 0;
    }

    .error-actions {
        flex-direction: column;
    }

    .error-actions .button {
        width: 100%;
    }
}

/* Responsive design for sidebar */
@media (max-width: 1200px) {

    /* Enhanced Sidebar Responsive */
    .redco-sidebar-header {
        padding: 24px 20px 20px 20px;
    }

    .sidebar-compact-row {
        gap: 6px;
    }

    .compact-modules-stat,
    .enhanced-help-trigger {
        padding: 8px 10px;
    }

    .compact-stat-icon,
    .help-icon-wrapper {
        width: 24px;
        height: 24px;
    }

    .compact-stat-icon .dashicons,
    .help-icon-wrapper .dashicons {
        font-size: 12px;
        width: 12px;
        height: 12px;
    }

    .compact-stat-value,
    .help-title {
        font-size: 12px;
    }

    .compact-stat-label,
    .help-subtitle {
        font-size: 8px;
    }
}

@media (max-width: 768px) {
    /* Dashboard now uses standardized module header responsive styles */

    /* Enhanced Mobile Sidebar */
    .redco-sidebar-header {
        padding: 20px 16px 16px 16px;
    }

    .logo-container {
        padding: 8px;
    }

    .sidebar-plugin-logo {
        width: 140px;
        height: 56px;
    }

    .sidebar-compact-row {
        flex-direction: column;
        gap: 8px;
    }

    .compact-modules-stat,
    .enhanced-help-trigger {
        padding: 12px;
        border-radius: 8px;
    }

    .compact-stat-icon,
    .help-icon-wrapper {
        width: 32px;
        height: 32px;
    }

    .compact-stat-icon .dashicons,
    .help-icon-wrapper .dashicons {
        font-size: 16px;
        width: 16px;
        height: 16px;
    }

    .compact-stat-value,
    .help-title {
        font-size: 14px;
    }

    .compact-stat-label,
    .help-subtitle {
        font-size: 10px;
    }

    /* Mobile Navigation */
    .redco-nav a {
        padding: 12px 8px;
        min-height: 48px;
        border-radius: 8px;
        margin-bottom: 2px;
    }

    .redco-nav .dashicons {
        margin-left: 12px;
        margin-right: 12px;
        width: 18px;
        height: 18px;
        font-size: 18px;
    }

    .nav-title {
        font-size: 13px;
    }

    .nav-description {
        font-size: 11px;
    }
}

.dashboard-controls {
    display: flex;
    align-items: center;
    gap: 16px;
    justify-content: flex-end;
}

.live-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #4CAF50;
    font-weight: 500;
}

.refresh-interval {
    font-size: 12px;
    color: #666;
    font-weight: 400;
    margin-left: 4px;
}

.refresh-all-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.refresh-all-btn:hover {
    background: #45a049;
    transform: translateY(-1px);
}

.enable-monitoring-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: #2196F3;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.enable-monitoring-btn:hover {
    background: #1976D2;
    color: white;
    transform: translateY(-1px);
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 24px;
}

/* PageSpeed Insights Style Score Cards */
.pagespeed-scores-card {
    background: #fff;
    border: 2px solid #e0e0e0;
    border-radius: 16px;
    padding: 24px;
    grid-column: span 2;
}

/* Core Web Vitals Chart Card */
.core-web-vitals-chart-card {
    background: #fff;
    border: 2px solid #e0e0e0;
    border-radius: 16px;
    padding: 24px;
    grid-column: span 2;
    margin-top: 20px;
}

.vitals-header {
    margin-bottom: 24px;
    text-align: center;
}

.vitals-header h3 {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--redco-dark);
}

.vitals-subtitle {
    margin: 0;
    font-size: 14px;
    color: #666;
}

/* Chart Container */
.vitals-chart-container {
    margin: 24px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    position: relative;
}

#coreWebVitalsChart {
    width: 100% !important;
    height: 300px !important;
    max-width: 100%;
}

/* Vitals Summary */
.vitals-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 24px;
}

.vital-summary-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 12px;
    transition: all 0.2s ease;
}

.vital-summary-item:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.vital-summary-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.vital-summary-icon.lcp {
    background: #2196F3;
}

.vital-summary-icon.fid {
    background: #FF9800;
}

.vital-summary-icon.cls {
    background: #9C27B0;
}

.vital-summary-content {
    flex: 1;
}

.vital-summary-label {
    font-size: 12px;
    font-weight: 600;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.vital-summary-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--redco-dark);
    line-height: 1;
    margin-bottom: 4px;
}

.vital-summary-status {
    font-size: 11px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
}

.vital-summary-status.good {
    background: #4CAF50;
    color: white;
}

.vital-summary-status.needs-improvement {
    background: #FF9800;
    color: white;
}

.vital-summary-status.poor {
    background: #F44336;
    color: white;
}

.scores-header {
    margin-bottom: 24px;
    text-align: center;
}

.scores-header h3 {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--redco-dark);
}

.scores-subtitle {
    margin: 0;
    font-size: 14px;
    color: #666;
}

/* Subtitle Styling for Data Type */
.scores-subtitle.estimated-data {
    color: #ff6b35;
    font-weight: 500;
}

.api-key-notice {
    display: inline-block;
    margin-left: 8px;
    padding: 2px 8px;
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 12px;
    font-size: 11px;
    color: #ff6b35;
    text-decoration: none;
}

.vitals-subtitle.estimated-data {
    color: #ff6b35;
    font-weight: 500;
}

.pagespeed-scores-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.score-item {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    transition: all 0.2s ease;
    position: relative;
}

.score-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Score Data Type Indicators */
.score-indicator {
    position: absolute;
    top: -18px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 11px;
    font-weight: 500;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.score-indicator.estimated {
    background: rgba(255, 193, 7, 0.1);
    color: #ff6b35;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.score-indicator.real {
    background: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.score-indicator .dashicons {
    font-size: 12px;
    width: 12px;
    height: 12px;
}

.score-indicator .indicator-text {
    font-size: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Score Item Styling Based on Data Type */
.score-item.estimated-score {
    border: 2px solid rgba(255, 193, 7, 0.2);
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.score-item.real-score {
    border: 2px solid rgba(76, 175, 80, 0.2);
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.score-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: #e0e0e0;
}

.score-circle::before {
    content: '';
    position: absolute;
    width: 60px;
    height: 60px;
    background: #f8f9fa;
    border-radius: 50%;
}

.score-number {
    position: relative;
    z-index: 1;
    font-size: 24px;
    font-weight: 700;
    color: var(--redco-dark);
}

.score-label {
    font-size: 16px;
    font-weight: 600;
    color: var(--redco-dark);
    margin-bottom: 12px;
}

.score-factors {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    justify-content: center;
}

.factor {
    font-size: 11px;
    padding: 4px 8px;
    background: #e0e0e0;
    border-radius: 12px;
    color: #666;
}

/* Score colors based on ranges */
.score-circle[data-score] {
    background: conic-gradient(
        from 0deg,
        var(--score-color, #e0e0e0) calc(var(--score-percentage, 0) * 1%),
        #e0e0e0 calc(var(--score-percentage, 0) * 1%)
    );
    transition: all 0.8s ease;
}

.score-circle.animated {
    animation: scoreReveal 0.8s ease-out;
}

@keyframes scoreReveal {
    from {
        background: conic-gradient(
            from 0deg,
            #e0e0e0 0%,
            #e0e0e0 100%
        );
    }
    to {
        background: conic-gradient(
            from 0deg,
            var(--score-color) calc(var(--score-percentage) * 1%),
            #e0e0e0 calc(var(--score-percentage) * 1%)
        );
    }
}

/* Good scores (90-100) - Green */
.score-circle[data-score="100"], .score-circle[data-score="99"], .score-circle[data-score="98"],
.score-circle[data-score="97"], .score-circle[data-score="96"], .score-circle[data-score="95"],
.score-circle[data-score="94"], .score-circle[data-score="93"], .score-circle[data-score="92"],
.score-circle[data-score="91"], .score-circle[data-score="90"] {
    --score-color: #4CAF50;
}

/* Average scores (50-89) - Orange */
.score-circle[data-score="89"], .score-circle[data-score="88"], .score-circle[data-score="87"],
.score-circle[data-score="86"], .score-circle[data-score="85"], .score-circle[data-score="84"],
.score-circle[data-score="83"], .score-circle[data-score="82"], .score-circle[data-score="81"],
.score-circle[data-score="80"], .score-circle[data-score="79"], .score-circle[data-score="78"],
.score-circle[data-score="77"], .score-circle[data-score="76"], .score-circle[data-score="75"],
.score-circle[data-score="74"], .score-circle[data-score="73"], .score-circle[data-score="72"],
.score-circle[data-score="71"], .score-circle[data-score="70"], .score-circle[data-score="69"],
.score-circle[data-score="68"], .score-circle[data-score="67"], .score-circle[data-score="66"],
.score-circle[data-score="65"], .score-circle[data-score="64"], .score-circle[data-score="63"],
.score-circle[data-score="62"], .score-circle[data-score="61"], .score-circle[data-score="60"],
.score-circle[data-score="59"], .score-circle[data-score="58"], .score-circle[data-score="57"],
.score-circle[data-score="56"], .score-circle[data-score="55"], .score-circle[data-score="54"],
.score-circle[data-score="53"], .score-circle[data-score="52"], .score-circle[data-score="51"],
.score-circle[data-score="50"] {
    --score-color: #FF9800;
}

/* Poor scores (0-49) - Red */
.score-circle[data-score="49"], .score-circle[data-score="48"], .score-circle[data-score="47"],
.score-circle[data-score="46"], .score-circle[data-score="45"], .score-circle[data-score="44"],
.score-circle[data-score="43"], .score-circle[data-score="42"], .score-circle[data-score="41"],
.score-circle[data-score="40"], .score-circle[data-score="39"], .score-circle[data-score="38"],
.score-circle[data-score="37"], .score-circle[data-score="36"], .score-circle[data-score="35"],
.score-circle[data-score="34"], .score-circle[data-score="33"], .score-circle[data-score="32"],
.score-circle[data-score="31"], .score-circle[data-score="30"], .score-circle[data-score="29"],
.score-circle[data-score="28"], .score-circle[data-score="27"], .score-circle[data-score="26"],
.score-circle[data-score="25"], .score-circle[data-score="24"], .score-circle[data-score="23"],
.score-circle[data-score="22"], .score-circle[data-score="21"], .score-circle[data-score="20"],
.score-circle[data-score="19"], .score-circle[data-score="18"], .score-circle[data-score="17"],
.score-circle[data-score="16"], .score-circle[data-score="15"], .score-circle[data-score="14"],
.score-circle[data-score="13"], .score-circle[data-score="12"], .score-circle[data-score="11"],
.score-circle[data-score="10"], .score-circle[data-score="9"], .score-circle[data-score="8"],
.score-circle[data-score="7"], .score-circle[data-score="6"], .score-circle[data-score="5"],
.score-circle[data-score="4"], .score-circle[data-score="3"], .score-circle[data-score="2"],
.score-circle[data-score="1"], .score-circle[data-score="0"] {
    --score-color: #F44336;
}

/* Health Score Card */
.health-score-card {
    background: #fff;
    border: 2px solid #e0e0e0;
    border-radius: 16px;
    padding: 24px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.health-score-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #4CAF50 0%, #2196F3 50%, #FF9800 100%);
}

.health-score-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.health-score-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--redco-dark);
}

.health-score-live-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #4CAF50;
    font-weight: 500;
}

.live-dot {
    width: 8px;
    height: 8px;
    background: #4CAF50;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.health-score-display {
    margin-bottom: 24px;
}

.health-score-ring {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto 16px;
    border-radius: 50%;
    background: conic-gradient(#4CAF50 0deg, #4CAF50 var(--score-angle, 270deg), #e0e0e0 var(--score-angle, 270deg));
    display: flex;
    align-items: center;
    justify-content: center;
}

.health-score-ring::before {
    content: '';
    position: absolute;
    width: 80px;
    height: 80px;
    background: #fff;
    border-radius: 50%;
}

.health-score-number {
    position: relative;
    z-index: 2;
    font-size: 32px;
    font-weight: 700;
    color: var(--redco-dark);
    line-height: 1;
}

.health-score-label {
    position: relative;
    z-index: 2;
    font-size: 14px;
    color: #666;
    margin-top: -4px;
}

.health-score-status {
    font-size: 16px;
    font-weight: 600;
    padding: 8px 16px;
    border-radius: 20px;
    display: inline-block;
}

.health-score-status.excellent {
    background: #e8f5e8;
    color: #2e7d32;
}

.health-score-status.good {
    background: #e3f2fd;
    color: #1565c0;
}

.health-score-status.fair {
    background: #fff3e0;
    color: #ef6c00;
}

.health-score-status.poor {
    background: #ffebee;
    color: #c62828;
}

.health-score-breakdown {
    display: flex;
    justify-content: space-between;
    gap: 16px;
    margin-top: 24px;
}

.breakdown-item {
    flex: 1;
    text-align: center;
}

.breakdown-label {
    display: block;
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.breakdown-value {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: var(--redco-dark);
}

/* Live Metrics Card */
.live-metrics-card {
    background: #fff;
    border: 2px solid #e0e0e0;
    border-radius: 16px;
    padding: 24px;
}

.metrics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.metrics-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--redco-dark);
}

.refresh-metrics-btn {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    color: #666;
    transition: all 0.2s ease;
}

.refresh-metrics-btn:hover {
    background: #f5f5f5;
    color: #4CAF50;
    transform: rotate(180deg);
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 16px;
}

.metric-item {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 16px;
    text-align: center;
    position: relative;
    transition: all 0.2s ease;
}

.metric-item:hover {
    background: #e8f5e8;
    transform: translateY(-2px);
}

.metric-icon {
    margin-bottom: 8px;
}

.metric-icon .dashicons {
    font-size: 24px;
    color: #4CAF50;
}

.metric-value {
    font-size: 20px;
    font-weight: 700;
    color: var(--redco-dark);
    margin-bottom: 4px;
}

.metric-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
}

.metric-trend {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.metric-trend.up {
    background: #ffebee;
    color: #c62828;
}

.metric-trend.down {
    background: #e8f5e8;
    color: #2e7d32;
}

.metric-trend.neutral {
    background: #f5f5f5;
    color: #666;
}

.metric-trend .dashicons {
    font-size: 12px;
}

/* Performance Metrics Card */
.performance-metrics-card {
    background: #fff;
    border: 2px solid #e0e0e0;
    border-radius: 16px;
    padding: 24px;
    grid-column: span 2;
}

.performance-metrics-card .metrics-header {
    margin-bottom: 20px;
}

.performance-metrics-card .metrics-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--redco-dark);
}

.performance-metrics-card .performance-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 16px;
}

.performance-metrics-card .performance-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 16px;
    text-align: center;
    transition: all 0.2s ease;
}

.performance-metrics-card .performance-card:hover {
    background: #e8f5e8;
    transform: translateY(-2px);
}

.performance-metrics-card .performance-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.performance-metrics-card .performance-header .dashicons {
    font-size: 24px;
    color: #4CAF50;
}

.performance-metrics-card .performance-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--redco-dark);
}

.performance-metrics-card .performance-value {
    margin-bottom: 8px;
}

.performance-metrics-card .score-number,
.performance-metrics-card .metric-number {
    font-size: 24px;
    font-weight: 700;
    color: var(--redco-dark);
}

.performance-metrics-card .score-label,
.performance-metrics-card .metric-unit {
    font-size: 12px;
    color: #666;
    margin-left: 4px;
}

.performance-metrics-card .performance-status,
.performance-metrics-card .performance-trend {
    font-size: 9px;
    font-weight: 600;
    padding: 3px 6px;
    border-radius: 6px;
}

.performance-metrics-card .performance-status.excellent,
.performance-metrics-card .performance-trend.down {
    background: #2e7d32;
    color: #ffffff;
    border: 1px solid #1b5e20;
    font-size: 10px;
}

.performance-metrics-card .performance-status.good {
    background: #1565c0;
    color: #ffffff;
    border: 1px solid #0d47a1;
    font-size: 10px;
}

.performance-metrics-card .performance-status.average,
.performance-metrics-card .performance-trend.neutral {
    background: #ef6c00;
    color: #ffffff;
    border: 1px solid #e65100;
    font-size: 10px;
}

.performance-metrics-card .performance-status.poor,
.performance-metrics-card .performance-trend.up {
    background: #c62828;
    color: #ffffff;
    border: 1px solid #b71c1c;
    font-size: 10px;
}

/* Monitoring Disabled State */
.monitoring-disabled-section {
    background: #f8f9fa;
    border: 2px dashed #e0e0e0;
    border-radius: 16px;
    padding: 60px 40px;
    text-align: center;
}

.disabled-content {
    max-width: 500px;
    margin: 0 auto;
}

.disabled-icon {
    margin-bottom: 24px;
}

.disabled-icon .dashicons {
    font-size: 64px;
    color: #ccc;
}

.monitoring-disabled-section h3 {
    margin: 0 0 16px 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--redco-dark);
}

.monitoring-disabled-section p {
    margin: 0 0 32px 0;
    font-size: 16px;
    color: #666;
    line-height: 1.5;
}

.disabled-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 32px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.feature-item .dashicons {
    font-size: 20px;
    color: #4CAF50;
}

.feature-item span:last-child {
    font-size: 14px;
    font-weight: 500;
    color: var(--redco-dark);
}

.enable-monitoring-btn-large {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    padding: 16px 32px;
    background: #4CAF50;
    color: white;
    text-decoration: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.enable-monitoring-btn-large:hover {
    background: #45a049;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);
}

.enable-monitoring-btn-large .dashicons {
    font-size: 20px;
}

/* Loading and Animation States */
.performance-card.updated {
    animation: cardUpdate 0.5s ease;
}

@keyframes cardUpdate {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); background: #e8f5e8; }
    100% { transform: scale(1); }
}

.refresh-all-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.refresh-all-btn .dashicons.fa-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.live-dot {
    width: 8px;
    height: 8px;
    background: #4CAF50;
    border-radius: 50%;
    display: inline-block;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Optimization Opportunities Card */
.optimization-opportunities-card {
    background: #fff;
    border: 2px solid #e0e0e0;
    border-radius: 16px;
    padding: 24px;
}

.opportunities-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.opportunities-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--redco-dark);
}

.opportunities-count {
    background: #4CAF50;
    color: white;
    font-size: 12px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
    min-width: 20px;
    text-align: center;
}

.opportunities-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.opportunity-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    border-radius: 12px;
    border: 1px solid #e0e0e0;
    transition: all 0.2s ease;
}

.opportunity-item:hover {
    border-color: #4CAF50;
    background: #f8fff8;
}

.opportunity-item.high {
    border-left: 4px solid #f44336;
}

.opportunity-item.medium {
    border-left: 4px solid #ff9800;
}

.opportunity-item.low {
    border-left: 4px solid #4CAF50;
}

.opportunity-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    background: #f8f9fa;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.opportunity-icon .dashicons {
    font-size: 20px;
    color: #4CAF50;
}

.opportunity-content {
    flex: 1;
}

.opportunity-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
}

.opportunity-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--redco-dark);
    margin-bottom: 4px;
    flex: 1;
}

.opportunity-improvement {
    display: flex;
    align-items: center;
    gap: 4px;
    background: #2e7d32;
    padding: 3px 6px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 600;
    margin-left: 12px;
    flex-shrink: 0;
    border: 1px solid #1b5e20;
}

.improvement-value {
    color: #ffffff;
}

.improvement-metric {
    color: #ffffff;
    font-size: 9px;
    text-transform: uppercase;
    opacity: 0.9;
}

.opportunity-description {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

.opportunity-action {
    flex-shrink: 0;
}

.enable-module-btn,
.configure-btn {
    background: #4CAF50;
    color: white;
    border: none;
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.enable-module-btn:hover,
.configure-btn:hover {
    background: #45a049;
    transform: translateY(-1px);
    color: white;
}

.enable-module-btn .dashicons,
.configure-btn .dashicons {
    font-size: 16px;
}

/* Quick Actions Card */
.quick-actions-card {
    background: #fff;
    border: 2px solid #e0e0e0;
    border-radius: 16px;
    padding: 24px;
}

.actions-header {
    margin-bottom: 20px;
}

.actions-header h3 {
    margin: 0 0 4px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--redco-dark);
}

.actions-subtitle {
    font-size: 14px;
    color: #666;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 16px 12px;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    background: #fff;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    font-size: 12px;
    font-weight: 500;
}

.quick-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.quick-action-btn.primary {
    border-color: #4CAF50;
    color: #4CAF50;
}

.quick-action-btn.primary:hover {
    background: #4CAF50;
    color: white;
}

.quick-action-btn.secondary {
    border-color: #e0e0e0;
    color: #666;
}

.quick-action-btn.secondary:hover {
    border-color: #4CAF50;
    color: #4CAF50;
}

.quick-action-btn .dashicons {
    font-size: 24px;
}

.action-text {
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Responsive Design for Health Monitor */
@media (max-width: 768px) {
    .health-monitor-grid {
        grid-template-columns: 1fr;
    }

    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .health-score-breakdown {
        flex-direction: column;
        gap: 8px;
    }

    .breakdown-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        text-align: left;
    }
}

/* Loading States for Health Monitor */
.health-monitor-loading {
    opacity: 0.6;
    pointer-events: none;
}

.health-monitor-loading .live-dot {
    animation: pulse 0.5s infinite;
}

.metric-item.loading {
    position: relative;
}

.metric-item.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.module-overview-item.enabled {
    border-color: var(--redco-success);
    background: #f0fdf4;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
}

.module-overview-item.disabled {
    opacity: 0.6;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.module-overview-item:hover {
    box-shadow: var(--redco-shadow-lg);
}

.module-info {
    flex: 1;
    margin-bottom: 16px;
}

.module-info h4 {
    margin: 0 0 8px 0;
    color: var(--redco-dark);
    font-weight: 600;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.module-info p {
    margin: 0;
    color: var(--redco-secondary);
    font-size: 14px;
    line-height: 1.5;
}

.module-toggle {
    align-self: flex-end;
}



/* Modules Page Grid Layout */
.redco-optimizer-modules h1 {
    margin-bottom: 32px;
    text-align: center;
}

.redco-modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.module-card {
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border: 2px solid var(--redco-border);
    border-radius: var(--redco-radius-lg);
    padding: 28px;
    transition: var(--redco-transition);
    position: relative;
    overflow: hidden;
}

.module-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--redco-border), var(--redco-border));
    transition: var(--redco-transition);
}

.module-card:hover {
    box-shadow: var(--redco-shadow-lg);
    border-color: var(--redco-primary);
}

.module-card:hover::before {
    background: linear-gradient(90deg, var(--redco-primary), var(--redco-primary-light));
}

.module-card.enabled {
    border-color: var(--redco-success);
    background: linear-gradient(145deg, #f0fdf4 0%, #dcfce7 100%);
}

.module-card.enabled::before {
    background: linear-gradient(90deg, var(--redco-success), #34d399);
}

.module-card.coming-soon {
    opacity: 0.8;
    background: linear-gradient(145deg, #fef3c7 0%, #fde68a 100%);
    border-color: var(--redco-warning);
}

.module-card.coming-soon::before {
    background: linear-gradient(90deg, var(--redco-warning), #fbbf24);
}

.module-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.module-header h3 {
    margin: 0;
    color: var(--redco-dark);
    font-weight: 600;
    font-size: 18px;
}

.module-description p {
    margin: 0 0 20px 0;
    color: var(--redco-secondary);
    line-height: 1.6;
    font-size: 14px;
}

.module-actions {
    text-align: right;
}

/* Coming Soon Modules Section */
.redco-coming-soon-modules {
    background: #fff;
    border-radius: var(--redco-radius-lg);
    padding: 32px;
    box-shadow: var(--redco-shadow);
    border: 1px solid var(--redco-border);
    margin-top: 32px;
}

.redco-coming-soon-modules h2 {
    margin: 0 0 24px 0;
    color: var(--redco-dark);
    font-size: 24px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 12px;
}

.redco-coming-soon-modules h2::before {
    content: "\f504";
    font-family: dashicons;
    font-size: 28px;
}

.coming-soon-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
}

.coming-soon-card {
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border: 2px solid var(--redco-border);
    border-radius: var(--redco-radius-lg);
    padding: 24px;
    transition: var(--redco-transition);
    position: relative;
    overflow: hidden;
}

.coming-soon-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--redco-border), var(--redco-border));
    transition: var(--redco-transition);
}

.coming-soon-card:hover {
    box-shadow: var(--redco-shadow-lg);
    border-color: var(--redco-primary);
    transform: translateY(-2px);
}

.coming-soon-card:hover::before {
    background: linear-gradient(90deg, var(--redco-primary), var(--redco-primary-light));
}

.coming-soon-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    flex-wrap: wrap;
}

.coming-soon-header .dashicons {
    font-size: 20px;
    color: var(--redco-primary);
}

.coming-soon-header h4 {
    margin: 0;
    color: var(--redco-dark);
    font-weight: 600;
    font-size: 16px;
    flex: 1;
}

.coming-soon-card p {
    margin: 0 0 16px 0;
    color: var(--redco-secondary);
    line-height: 1.5;
    font-size: 14px;
}

.coming-soon-type {
    display: flex;
    justify-content: flex-end;
}

.type-badge {
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.type-badge.free {
    background: var(--redco-success);
    color: white;
}

.type-badge.premium {
    background: var(--redco-primary);
    color: white;
}

/* Coming Soon Page Styles */
.redco-coming-soon-page {
    max-width: 1200px;
    margin: 0 auto;
}

.coming-soon-header-section {
    text-align: center;
    padding: 40px 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: var(--redco-radius-lg);
    margin-bottom: 32px;
    border: 1px solid var(--redco-border);
}

.coming-soon-header-section h1 {
    margin: 0 0 16px 0;
    color: var(--redco-dark);
    font-size: 32px;
    font-weight: 700;
}

.coming-soon-intro {
    font-size: 18px;
    color: var(--redco-secondary);
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

/* Development Roadmap */
.coming-soon-roadmap {
    background: #fff;
    border-radius: var(--redco-radius-lg);
    padding: 32px;
    box-shadow: var(--redco-shadow);
    border: 1px solid var(--redco-border);
    margin-top: 32px;
}

.coming-soon-roadmap h2 {
    margin: 0 0 24px 0;
    color: var(--redco-dark);
    font-size: 24px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 12px;
}

.coming-soon-roadmap h2::before {
    content: "\f508";
    font-family: dashicons;
    font-size: 28px;
}

.roadmap-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 24px;
}

.roadmap-card {
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border: 2px solid var(--redco-border);
    border-radius: var(--redco-radius-lg);
    padding: 28px;
    transition: var(--redco-transition);
    position: relative;
    overflow: hidden;
}

.roadmap-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--redco-border), var(--redco-border));
    transition: var(--redco-transition);
}

.roadmap-card:hover {
    box-shadow: var(--redco-shadow-lg);
    border-color: var(--redco-primary);
    transform: translateY(-2px);
}

.roadmap-card:hover::before {
    background: linear-gradient(90deg, var(--redco-primary), var(--redco-primary-light));
}

.roadmap-card.in-progress {
    border-color: var(--redco-warning);
    background: linear-gradient(145deg, #fef3c7 0%, #fde68a 100%);
}

.roadmap-card.in-progress::before {
    background: linear-gradient(90deg, var(--redco-warning), #fbbf24);
}

.roadmap-card.planned {
    border-color: var(--redco-primary);
    background: linear-gradient(145deg, #dbeafe 0%, #bfdbfe 100%);
}

.roadmap-card.planned::before {
    background: linear-gradient(90deg, var(--redco-primary), var(--redco-primary-light));
}

.roadmap-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    flex-wrap: wrap;
    gap: 12px;
}

.roadmap-title {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.roadmap-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--redco-primary), var(--redco-primary-light));
    border-radius: var(--redco-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.roadmap-card.in-progress .roadmap-icon {
    background: linear-gradient(135deg, var(--redco-warning), #fbbf24);
}

.roadmap-card.planned .roadmap-icon {
    background: linear-gradient(135deg, var(--redco-primary), var(--redco-primary-light));
}

.roadmap-icon .dashicons {
    color: white;
    font-size: 20px;
}

.roadmap-title h3 {
    margin: 0;
    color: var(--redco-dark);
    font-size: 18px;
    font-weight: 600;
}

.roadmap-description {
    margin: 0 0 20px 0;
    color: var(--redco-secondary);
    line-height: 1.6;
    font-size: 14px;
}

.roadmap-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 12px;
}

.roadmap-status {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.roadmap-status.in-progress {
    background: linear-gradient(135deg, var(--redco-warning), #fbbf24);
    color: white;
}

.roadmap-status.in-progress::before {
    content: "\f463";
    font-family: dashicons;
    font-size: 14px;
}

.roadmap-status.planned {
    background: linear-gradient(135deg, var(--redco-primary), var(--redco-primary-light));
    color: white;
}

.roadmap-status.planned::before {
    content: "\f145";
    font-family: dashicons;
    font-size: 14px;
}

.roadmap-progress {
    font-size: 12px;
    color: var(--redco-secondary);
    font-weight: 500;
}

/* Call to Action Section */
.coming-soon-cta {
    background: linear-gradient(135deg, var(--redco-primary), var(--redco-primary-light));
    border-radius: var(--redco-radius-lg);
    padding: 40px;
    text-align: center;
    margin-top: 32px;
    color: white;
}

.coming-soon-cta h2 {
    margin: 0 0 16px 0;
    font-size: 28px;
    font-weight: 700;
}

.coming-soon-cta p {
    margin: 0 0 24px 0;
    font-size: 16px;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: var(--redco-radius);
    text-decoration: none;
    font-weight: 600;
    transition: var(--redco-transition);
    border: 2px solid transparent;
}

.cta-button.primary {
    background: white;
    color: var(--redco-primary);
}

.cta-button.primary:hover {
    background: var(--redco-light);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.cta-button.secondary {
    background: transparent;
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
}

.cta-button.secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: white;
    transform: translateY(-2px);
}

.cta-button .dashicons {
    font-size: 16px;
}

/* Coming soon styles */
.redco-coming-soon {
    text-align: center;
    padding: 60px 20px;
}

.coming-soon-content h2 {
    color: #32373c;
    margin-bottom: 15px;
}

.coming-soon-message {
    font-size: 18px;
    color: #0073aa;
    margin-bottom: 20px;
    font-weight: 500;
}

/* License section */
.redco-license-section {
    margin-top: 30px;
    padding-top: 30px;
    border-top: 1px solid #e9ecef;
}

.license-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 12px;
}

.license-status.license-active {
    background: #d4edda;
    color: #155724;
}

.license-status.license-inactive {
    background: #f8d7da;
    color: #721c24;
}

.license-status.license-expired {
    background: #fff3cd;
    color: #856404;
}

/* Addons section */
.redco-addons-section {
    margin-top: 30px;
}

.addons-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.addons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.addon-card {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.2s ease;
}

.addon-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.addon-card.installed {
    border-color: #0073aa;
}

.addon-card.active {
    border-color: #28a745;
    background: #f8fff9;
}

.addon-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
}

.addon-header h3 {
    margin: 0;
    color: #32373c;
}

.addon-version {
    background: #e9ecef;
    color: #495057;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.addon-description p {
    margin: 0 0 15px 0;
    color: #666;
    line-height: 1.5;
}

.addon-actions {
    text-align: right;
}

/* Form styles */
.redco-form-section {
    margin-bottom: 30px;
}

.redco-form-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #32373c;
}

.redco-form-row {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.redco-form-row label {
    width: 200px;
    font-weight: 500;
    color: #32373c;
}

.redco-form-row input,
.redco-form-row select,
.redco-form-row textarea {
    flex: 1;
    max-width: 400px;
}

.redco-form-row .description {
    margin-left: 10px;
    color: #666;
    font-style: italic;
}

/* Responsive Grid Design */
@media (max-width: 1200px) {
    .redco-admin-container {
        grid-template-columns: 280px 1fr;
        gap: 24px;
    }

    .redco-modules-grid {
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    }
}

@media (max-width: 968px) {
    .redco-admin-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .redco-sidebar {
        position: static;
        width: 100%;
        order: 2;
    }

    .redco-content {
        order: 1;
        padding: 0;
    }

    .redco-stats-cards {
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    }

    .quick-actions-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .redco-modules-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }

    .modules-overview-list {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .coming-soon-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }

    .roadmap-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}

@media (max-width: 768px) {
    .redco-header-main {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }

    .plugin-stats {
        gap: 16px;
    }

    .performance-cards {
        grid-template-columns: 1fr;
    }

    .redco-stats-cards {
        grid-template-columns: 1fr;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .stat-icon {
        align-self: center;
    }

    /* Coming Soon Page Mobile */
    .coming-soon-header-section {
        padding: 24px 16px;
    }

    .coming-soon-header-section h1 {
        font-size: 24px;
    }

    .coming-soon-intro {
        font-size: 16px;
    }

    .roadmap-grid {
        grid-template-columns: 1fr;
    }

    .roadmap-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }

    .roadmap-title {
        width: 100%;
    }

    .roadmap-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .cta-button {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }
}

@media (max-width: 640px) {
    .redco-optimizer-admin {
        margin: 0;
    }

    .redco-header {
        padding: 12px 24px;
    }

    .redco-header h1 {
        font-size: 24px;
        flex-direction: column;
        gap: 8px;
    }

    .redco-optimizer-admin .plugin-logo {
        width: 150px !important;
        height: 45px !important;
        padding: 0 !important;
    }

    .redco-admin-container {
        padding: 0;
    }

    .redco-stats-cards,
    .quick-actions-grid,
    .redco-modules-grid,
    .performance-cards,
    .modules-overview-list,
    .coming-soon-grid {
        grid-template-columns: 1fr;
    }

    .redco-nav li {
        margin: 0;
        width: 100%;
    }

    .redco-nav a {
        padding: 14px 0;
        min-height: 50px;
        margin-bottom: 4px;
    }

    .redco-nav .dashicons {
        width: 18px;
        height: 18px;
        font-size: 18px;
        margin-left: 16px;
        margin-right: 12px;
    }

    .nav-title {
        font-size: 13px;
    }

    .nav-description {
        font-size: 10px;
    }

    .coming-soon-badge {
        font-size: 8px;
        padding: 2px 6px;
        margin-left: 8px;
    }

    .module-card,
    .redco-quick-actions,
    .modules-overview-section,
    .redco-performance-overview {
        padding: 20px;
    }

    .stat-card {
        padding: 20px;
    }

    .stat-number {
        font-size: 24px;
    }

    .score-number,
    .metric-number {
        font-size: 28px;
    }

    .performance-header h3 {
        font-size: 12px;
    }
}

/* Enhanced Loading States and Animations */
.redco-loading {
    opacity: 0.7;
    pointer-events: none;
    position: relative;
}

.redco-loading::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(2px);
    border-radius: inherit;
    z-index: 10;
}

/* Loading Overlay */
.redco-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(30, 41, 59, 0.8);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: var(--redco-transition);
}

.redco-loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* Enhanced Spinners */
.redco-spinner-large {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid var(--redco-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.redco-inline-spinner {
    position: absolute;
    top: 50%;
    right: 16px;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    border: 2px solid rgba(37, 99, 235, 0.3);
    border-top: 2px solid var(--redco-primary);
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
    z-index: 11;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Toast Notification System */
.redco-toast-container {
    position: fixed;
    top: 32px;
    right: 32px;
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-width: 400px;
}

.redco-toast {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    background: #ffffff;
    border-radius: var(--redco-radius);
    box-shadow: var(--redco-shadow-lg);
    border-left: 4px solid;
    transform: translateX(100%);
    opacity: 0;
    transition: var(--redco-transition);
    position: relative;
    overflow: hidden;
}

.redco-toast.show {
    transform: translateX(0);
    opacity: 1;
}

.redco-toast-success {
    border-left-color: var(--redco-success);
    background: linear-gradient(135deg, #f0fdf4 0%, #ffffff 100%);
}

.redco-toast-error {
    border-left-color: var(--redco-danger);
    background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
}

.redco-toast-warning {
    border-left-color: var(--redco-warning);
    background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
}

.redco-toast-info {
    border-left-color: var(--redco-info);
    background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 100%);
}

.redco-toast-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    margin-right: 12px;
    flex-shrink: 0;
}

.redco-toast-success .redco-toast-icon {
    background: var(--redco-success);
    color: white;
}

.redco-toast-error .redco-toast-icon {
    background: var(--redco-danger);
    color: white;
}

.redco-toast-warning .redco-toast-icon {
    background: var(--redco-warning);
    color: white;
}

.redco-toast-info .redco-toast-icon {
    background: var(--redco-info);
    color: white;
}

.redco-toast-message {
    flex: 1;
    color: var(--redco-dark);
    font-weight: 500;
    line-height: 1.4;
}

.redco-toast-close {
    background: none;
    border: none;
    font-size: 20px;
    color: var(--redco-secondary);
    cursor: pointer;
    padding: 0;
    margin-left: 12px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--redco-transition);
}

.redco-toast-close:hover {
    background: rgba(0, 0, 0, 0.1);
    color: var(--redco-dark);
}



/* Tooltip System */
.redco-tooltip {
    position: absolute;
    background: var(--redco-dark);
    color: white;
    padding: 8px 12px;
    border-radius: var(--redco-radius);
    font-size: 12px;
    font-weight: 500;
    z-index: 10001;
    opacity: 0;
    transform: translateY(4px);
    transition: var(--redco-transition);
    pointer-events: none;
    white-space: nowrap;
}

.redco-tooltip.show {
    opacity: 1;
    transform: translateY(0);
}

.redco-tooltip::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: var(--redco-dark);
}

/* Enhanced Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-4px); }
    20%, 40%, 60%, 80% { transform: translateX(4px); }
}

@keyframes flash {
    0%, 100% { background-color: transparent; }
    50% { background-color: rgba(16, 185, 129, 0.2); }
}

/* Removed excessive animations */

/* Enhanced Form Styling */
.redco-form-section {
    background: #ffffff;
    border-radius: var(--redco-radius-lg);
    padding: 28px;
    margin-bottom: 24px;
    box-shadow: var(--redco-shadow);
    border: 1px solid var(--redco-border);
    transition: var(--redco-transition);
}

.redco-form-section:hover {
    box-shadow: var(--redco-shadow-lg);
}

.redco-form-section h3 {
    margin: 0 0 20px 0;
    color: var(--redco-dark);
    font-weight: 600;
    font-size: 18px;
    padding-bottom: 12px;
    border-bottom: 2px solid var(--redco-border);
}

/* Enhanced Buttons */
.button-primary {
    background: linear-gradient(135deg, var(--redco-primary), var(--redco-primary-light)) !important;
    border-color: var(--redco-primary) !important;
    color: #fff !important;
    padding: 12px 24px !important;
    border-radius: var(--redco-radius) !important;
    font-weight: 600 !important;
    text-transform: none !important;
    transition: var(--redco-transition) !important;
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2) !important;
}

.button-primary:hover {
    background: linear-gradient(135deg, var(--redco-primary-dark), var(--redco-primary)) !important;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3) !important;
}

.button-secondary {
    background: linear-gradient(135deg, #ffffff, var(--redco-light)) !important;
    border-color: var(--redco-border) !important;
    color: var(--redco-secondary) !important;
    padding: 12px 24px !important;
    border-radius: var(--redco-radius) !important;
    font-weight: 500 !important;
    transition: var(--redco-transition) !important;
}

.button-secondary:hover {
    background: linear-gradient(135deg, var(--redco-light), #e2e8f0) !important;
    border-color: var(--redco-primary) !important;
    color: var(--redco-primary) !important;
}

/* Enhanced Exclude Sections */
.redco-exclude-section {
    background: #ffffff;
    border: 1px solid var(--redco-border);
    border-radius: var(--redco-radius);
    margin-top: 16px;
    overflow: hidden;
}

.redco-exclude-header {
    background: linear-gradient(135deg, var(--redco-light), #ffffff);
    padding: 16px 20px;
    border-bottom: 1px solid var(--redco-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: var(--redco-transition);
}

.redco-exclude-header:hover {
    background: linear-gradient(135deg, #e2e8f0, var(--redco-light));
}

.redco-exclude-header h4 {
    margin: 0;
    color: var(--redco-dark);
    font-weight: 600;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.redco-exclude-toggle {
    background: none;
    border: none;
    color: var(--redco-secondary);
    font-size: 18px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: var(--redco-transition);
}

.redco-exclude-toggle:hover {
    background: rgba(37, 99, 235, 0.1);
    color: var(--redco-primary);
}

.redco-exclude-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
}

.redco-exclude-content.expanded {
    max-height: 600px;
    overflow-y: auto;
}

.redco-exclude-search {
    padding: 16px 20px;
    border-bottom: 1px solid var(--redco-border);
    background: var(--redco-light);
}

.redco-exclude-search input {
    width: 100%;
    padding: 10px 16px;
    border: 1px solid var(--redco-border);
    border-radius: var(--redco-radius);
    font-size: 14px;
    transition: var(--redco-transition);
}

.redco-exclude-search input:focus {
    outline: none;
    border-color: var(--redco-primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.redco-exclude-list {
    padding: 16px 20px;
    max-height: 400px;
    overflow-y: auto;
}

/* Multi-column checkbox layout */
.checkbox-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 12px 16px;
    margin: 0;
}

.checkbox-item {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    padding: 12px;
    border: 1px solid transparent;
    border-radius: var(--redco-radius);
    transition: var(--redco-transition);
    background: linear-gradient(135deg, #ffffff, var(--redco-light));
}

.checkbox-item:hover {
    border-color: var(--redco-border);
    background: linear-gradient(135deg, var(--redco-light), #e2e8f0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.checkbox-item input[type="checkbox"] {
    margin: 0;
    flex-shrink: 0;
    margin-top: 2px;
}

.checkbox-item label {
    margin: 0;
    font-weight: normal;
    cursor: pointer;
    line-height: 1.4;
    flex: 1;
    font-size: 14px;
}

.checkbox-item label strong {
    color: var(--redco-dark);
    font-weight: 600;
    display: block;
    margin-bottom: 2px;
}

.checkbox-item label small {
    color: var(--redco-secondary);
    font-size: 12px;
    opacity: 0.8;
}

.version-info {
    background: var(--redco-primary);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    margin-left: 8px;
    display: inline-block;
}

/* Lazy loading placeholder */
.checkbox-item.loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Bulk actions */
.redco-bulk-actions {
    padding: 12px 20px;
    background: var(--redco-light);
    border-top: 1px solid var(--redco-border);
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px;
}

.redco-bulk-actions button {
    padding: 6px 12px;
    border: 1px solid var(--redco-border);
    background: white;
    border-radius: var(--redco-radius);
    cursor: pointer;
    font-size: 12px;
    transition: var(--redco-transition);
}

.redco-bulk-actions button:hover {
    background: var(--redco-primary);
    color: white;
    border-color: var(--redco-primary);
}

/* Stats display */
.redco-exclude-stats {
    color: var(--redco-secondary);
    font-size: 12px;
    margin-left: auto;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Load More button */
.load-more-btn {
    width: 100%;
    padding: 12px;
    margin-top: 16px;
    border: 2px dashed var(--redco-border);
    background: linear-gradient(135deg, #ffffff, var(--redco-light));
    color: var(--redco-secondary);
    cursor: pointer;
    border-radius: var(--redco-radius);
    font-weight: 500;
    transition: var(--redco-transition);
}

.load-more-btn:hover {
    border-color: var(--redco-primary);
    color: var(--redco-primary);
    background: linear-gradient(135deg, var(--redco-light), #e2e8f0);
}

/* No results message */
.no-results {
    text-align: center;
    padding: 40px 20px;
    color: var(--redco-secondary);
    font-style: italic;
    background: linear-gradient(135deg, var(--redco-light), #ffffff);
    border-radius: var(--redco-radius);
    border: 2px dashed var(--redco-border);
}

/* Enhanced scrollbar for exclude lists */
.redco-exclude-list::-webkit-scrollbar {
    width: 8px;
}

.redco-exclude-list::-webkit-scrollbar-track {
    background: var(--redco-light);
    border-radius: 4px;
}

.redco-exclude-list::-webkit-scrollbar-thumb {
    background: var(--redco-border);
    border-radius: 4px;
    transition: var(--redco-transition);
}

.redco-exclude-list::-webkit-scrollbar-thumb:hover {
    background: var(--redco-secondary);
}

/* Lazy hidden items */
.checkbox-item.lazy-hidden {
    display: none;
}

/* Simple exclude section transitions */
.redco-exclude-content {
    transition: max-height 0.3s ease;
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .checkbox-list {
        grid-template-columns: 1fr;
    }

    .redco-exclude-content.expanded {
        max-height: 300px;
    }

    .checkbox-item {
        padding: 10px;
    }

    .redco-bulk-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }
}

/* Mobile Toast Adjustments */
@media (max-width: 640px) {
    .redco-toast-container {
        top: 16px;
        right: 16px;
        left: 16px;
        max-width: none;
    }

    .redco-toast {
        padding: 12px 16px;
    }

    .redco-tooltip {
        font-size: 11px;
        padding: 6px 10px;
    }
}



/* ===== HELP SYSTEM STYLES ===== */

/* Help Icons */
.redco-help-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    margin-left: 6px;
    cursor: pointer;
    color: #6b7280;
    transition: var(--redco-transition);
    border-radius: 50%;
    background: rgba(107, 114, 128, 0.1);
    vertical-align: middle;
}

.redco-help-icon:hover {
    color: var(--redco-primary);
    background: rgba(37, 99, 235, 0.1);
    transform: scale(1.1);
}

.redco-help-icon .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

/* Help Panel */
.redco-help-panel {
    position: fixed;
    top: 0;
    right: -450px;
    width: 450px;
    height: 100vh;
    background: #ffffff;
    box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);
    z-index: 999999;
    transition: right 0.3s ease-in-out;
    display: flex;
    flex-direction: column;
    border-left: 1px solid #e5e7eb;
}

.redco-help-panel.active {
    right: 0;
}

.help-panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    background: linear-gradient(135deg, var(--redco-primary) 0%, #1e40af 100%);
    color: white;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.help-panel-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.help-panel-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--redco-transition);
}

.help-panel-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.help-panel-close .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Help Panel Search */
.help-panel-search {
    padding: 16px 24px;
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
    position: relative;
}

.help-panel-search input {
    width: 100%;
    padding: 10px 16px 10px 40px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    transition: var(--redco-transition);
    background: white;
}

.help-panel-search input:focus {
    outline: none;
    border-color: var(--redco-primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.help-panel-search .dashicons {
    position: absolute;
    left: 36px;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Help Panel Content */
.help-panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
}

.help-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 40px 20px;
    color: #6b7280;
    font-size: 14px;
}

.help-loading .dashicons {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Help Content Styling */
.help-content h3 {
    margin: 0 0 15px 0;
    color: #1e3a8a;
    font-size: 20px;
    font-weight: 600;
}

.help-content h4 {
    margin: 20px 0 10px 0;
    color: #1e40af;
    font-size: 16px;
    font-weight: 600;
}

.help-content h5 {
    margin: 15px 0 8px 0;
    color: #3730a3;
    font-size: 14px;
    font-weight: 600;
}

.help-content h6 {
    margin: 12px 0 6px 0;
    color: #4338ca;
    font-size: 13px;
    font-weight: 600;
}

.help-content p {
    margin: 0 0 12px 0;
    line-height: 1.6;
    color: #374151;
}

.help-content ul {
    margin: 8px 0 12px 20px;
    padding: 0;
}

.help-content li {
    margin-bottom: 6px;
    color: #4b5563;
    line-height: 1.5;
}

/* Help Subsections */
.help-subsection {
    margin: 15px 0;
    padding: 12px;
    background: #f8fafc;
    border-left: 3px solid #3b82f6;
    border-radius: 0 4px 4px 0;
}

.help-subsection.performance-impact {
    background: #f0fdf4;
    border-left-color: #10b981;
}

.help-detail {
    margin: 10px 0;
    padding: 8px 12px;
    background: #fafbfc;
    border-radius: 4px;
    border: 1px solid #e5e7eb;
}

.help-detail.performance-impact {
    background: #ecfdf5;
    border-color: #a7f3d0;
}

/* Pros and Cons Styling */
.pros-cons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin: 15px 0;
}

.pros, .cons {
    padding: 12px;
    border-radius: 6px;
}

.pros {
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
}

.pros h6 {
    color: #059669;
    margin-top: 0;
}

.pros ul {
    margin-left: 16px;
}

.pros li {
    color: #065f46;
}

.cons {
    background: #fef2f2;
    border: 1px solid #fecaca;
}

.cons h6 {
    color: #dc2626;
    margin-top: 0;
}

.cons ul {
    margin-left: 16px;
}

.cons li {
    color: #991b1b;
}

/* Recommendations Styling */
.help-recommendations {
    margin: 15px 0;
    padding: 12px;
    background: #fffbeb;
    border: 1px solid #fed7aa;
    border-radius: 6px;
}

.help-recommendations h5 {
    color: #d97706;
    margin-top: 0;
}

.help-recommendations ul {
    margin-left: 16px;
}

.help-recommendations li {
    color: #92400e;
}

/* Warnings Styling */
.help-warnings {
    margin: 15px 0;
    padding: 12px;
    background: #fef2f2;
    border: 1px solid #fca5a5;
    border-radius: 6px;
}

.help-warnings h5, .help-warnings h6 {
    color: #dc2626;
    margin-top: 0;
}

.help-warnings ul {
    margin-left: 16px;
}

.help-warnings li {
    color: #991b1b;
}

/* Technical Details Styling */
.help-detail.technical-details {
    background: #f0f9ff;
    border-color: #bae6fd;
}

.help-detail.technical-details h6 {
    color: #0369a1;
}

.help-detail.technical-details li {
    color: #0c4a6e;
}

/* Value Options Analysis */
.help-detail.value-options {
    background: #fefce8;
    border-color: #fde047;
}

.help-detail.value-options h6 {
    color: #a16207;
}

.value-option {
    margin: 10px 0;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 4px;
    border-left: 3px solid #eab308;
}

.value-option h7 {
    display: block;
    font-size: 13px;
    font-weight: 600;
    color: #92400e;
    margin: 0 0 6px 0;
}

.value-option p {
    margin: 4px 0;
    font-size: 12px;
    color: #78716c;
}

/* Value Analysis Styling */
.help-detail.value-analysis {
    background: #f3f4f6;
    border-color: #d1d5db;
}

.analysis-item {
    margin: 8px 0;
    padding: 8px;
    background: white;
    border-radius: 4px;
    border-left: 3px solid #6b7280;
}

.analysis-item h7 {
    display: block;
    font-size: 13px;
    font-weight: 600;
    color: #374151;
    margin: 0 0 4px 0;
}

.analysis-item p {
    margin: 2px 0;
    font-size: 12px;
    color: #6b7280;
}

/* Browser Support Styling */
.help-detail.browser-support {
    background: #f0fdf4;
    border-color: #bbf7d0;
}

.help-detail.browser-support h6 {
    color: #059669;
}

.help-detail.browser-support li {
    color: #065f46;
    font-family: monospace;
    font-size: 12px;
}

/* Enhanced Detail Sections */
.help-detail h6 {
    font-size: 12px;
    font-weight: 600;
    margin: 0 0 8px 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.help-detail ul {
    margin: 8px 0 0 16px;
}

.help-detail li {
    margin-bottom: 4px;
    font-size: 13px;
    line-height: 1.4;
}

/* Help Overlay */
.redco-help-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999998;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
}

.redco-help-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .redco-help-panel {
        width: 100%;
        right: -100%;
    }

    .pros-cons {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .help-subsection {
        padding: 10px;
    }

    .help-detail {
        padding: 8px 10px;
    }

    .help-panel-content {
        padding: 16px;
    }

    /* Responsive adjustments for detailed content */
    .value-option, .analysis-item {
        padding: 6px 8px;
    }

    .value-option h7, .analysis-item h7 {
        font-size: 12px;
    }

    .value-option p, .analysis-item p {
        font-size: 11px;
    }

    .help-detail h6 {
        font-size: 11px;
    }
}

/* Enhanced Loading States for Dashboard Sections */
.redco-loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.redco-loading .redco-card {
    position: relative;
}

.redco-loading .redco-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* Loading overlay for dashboard sections */
.redco-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
    border-radius: 8px;
    backdrop-filter: blur(2px);
}

/* Spinner animation */
.redco-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #4CAF50;
    border-radius: 50%;
    animation: redco-spin 1s linear infinite;
}

@keyframes redco-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Section loading states */
.redco-pagespeed-scores.redco-loading,
.redco-core-web-vitals-chart.redco-loading,
.redco-health-metrics.redco-loading {
    position: relative;
    opacity: 0.8;
}

.redco-pagespeed-scores.redco-loading .redco-loading-overlay,
.redco-core-web-vitals-chart.redco-loading .redco-loading-overlay,
.redco-health-metrics.redco-loading .redco-loading-overlay {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(1px);
}

/* Performance indicator loading animation */
.performance-indicator .indicator-dot.loading {
    background: #4CAF50;
    animation: redco-pulse 1.5s ease-in-out infinite;
}

/* CSS Debug Notice */
.redco-css-debug-notice {
    position: fixed;
    top: 32px;
    right: 20px;
    background: #ff6b6b;
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    z-index: 999998;
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
    border: 2px solid #ff5252;
    max-width: 300px;
    display: none;
}

.redco-css-debug-notice.show {
    display: block;
    animation: slideInRight 0.3s ease-out;
}

.redco-css-debug-notice .debug-close {
    position: absolute;
    top: 4px;
    right: 8px;
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.redco-css-debug-notice .debug-close:hover {
    opacity: 0.8;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* PageSpeed API Status Indicator */
.pagespeed-api-status {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
    margin-left: 8px;
}

.pagespeed-api-status.status-good {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.pagespeed-api-status.status-warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.pagespeed-api-status.status-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.pagespeed-api-status .status-icon {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.pagespeed-api-status.status-good .status-icon {
    background: #28a745;
}

.pagespeed-api-status.status-warning .status-icon {
    background: #ffc107;
}

.pagespeed-api-status.status-error .status-icon {
    background: #dc3545;
}

/* API Debug Panel */
.pagespeed-debug-panel {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 16px;
    margin-top: 16px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    display: none;
}

.pagespeed-debug-panel.show {
    display: block;
}

.pagespeed-debug-panel h4 {
    margin: 0 0 12px 0;
    font-family: inherit;
    font-size: 14px;
    color: #495057;
}

.pagespeed-debug-panel .debug-section {
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e9ecef;
}

.pagespeed-debug-panel .debug-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.pagespeed-debug-panel .debug-label {
    font-weight: bold;
    color: #6c757d;
    margin-bottom: 4px;
}

.pagespeed-debug-panel .debug-value {
    color: #212529;
    word-break: break-all;
}

.pagespeed-debug-panel .debug-error {
    color: #dc3545;
    background: #f8d7da;
    padding: 4px 8px;
    border-radius: 4px;
    margin: 2px 0;
}

.pagespeed-debug-panel .debug-success {
    color: #155724;
    background: #d4edda;
    padding: 4px 8px;
    border-radius: 4px;
    margin: 2px 0;
}

/* Debug Toggle Button */
.pagespeed-debug-toggle {
    background: none;
    border: 1px solid #6c757d;
    color: #6c757d;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    cursor: pointer;
    margin-left: 8px;
    transition: all 0.2s ease;
}

.pagespeed-debug-toggle:hover {
    background: #6c757d;
    color: white;
}

.pagespeed-debug-toggle.active {
    background: #007cba;
    border-color: #007cba;
    color: white;
}

@keyframes redco-pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Loading text animation */
.redco-loading-text {
    color: #4CAF50;
    font-size: 14px;
    font-weight: 500;
    margin-top: 8px;
    animation: redco-fade 1.5s ease-in-out infinite;
}

@keyframes redco-fade {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* Chart loading animation */
.chart-loading {
    position: relative;
    opacity: 0.7;
}

.chart-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #4CAF50;
    border-radius: 50%;
    animation: redco-spin 1s linear infinite;
    z-index: 10;
}

/* Score animation states */
.score-number.animating,
.metric-value.animating,
.stat-number.animating {
    color: #4CAF50;
    font-weight: bold;
    transition: color 0.3s ease;
}

/* Statistics Update Animation */
.updated {
    animation: statsUpdate 1s ease-in-out;
    color: #4CAF50 !important;
    font-weight: bold;
}

@keyframes statsUpdate {
    0% {
        background-color: #4CAF50;
        color: white !important;
        transform: scale(1.05);
        border-radius: 3px;
        padding: 2px 4px;
    }
    50% {
        background-color: #4CAF50;
        color: white !important;
        transform: scale(1.1);
        border-radius: 3px;
        padding: 2px 4px;
    }
    100% {
        background-color: transparent;
        color: #4CAF50 !important;
        transform: scale(1);
        border-radius: 0;
        padding: 0;
    }
}

/* ===== MODULE ACTION BUTTON SYSTEM ===== */

/* Module Action Button Styles (replaces toggle switches) */
.module-action-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f7f7f7;
    color: #555;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    min-width: 90px;
    justify-content: center;
}

.module-action-btn:hover {
    background: #fff;
    border-color: #999;
    color: #333;
    text-decoration: none;
}

.module-action-btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
    border-color: #4CAF50;
}

.module-action-btn.enabled {
    background: #4CAF50;
    border-color: #4CAF50;
    color: white;
}

.module-action-btn.enabled:hover {
    background: #45a049;
    border-color: #45a049;
    color: white;
}

.module-action-btn.disabled {
    background: #f7f7f7;
    border-color: #ddd;
    color: #666;
}

.module-action-btn.disabled:hover {
    background: #fff;
    border-color: #999;
    color: #333;
}

.module-action-btn.premium-disabled {
    background: #f0f0f0;
    border-color: #ccc;
    color: #999;
    cursor: not-allowed;
    opacity: 0.7;
}

.module-action-btn.premium-disabled:hover {
    background: #f0f0f0;
    border-color: #ccc;
    color: #999;
}

.module-action-btn.loading {
    opacity: 0.7;
    cursor: wait;
}

.module-action-btn .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    line-height: 1;
}

.module-action-btn .dashicons.spin {
    animation: spin 1s linear infinite;
}

/* Module card loading state */
.module-card.redco-loading {
    opacity: 0.7;
    pointer-events: none;
    position: relative;
}

.module-card.redco-loading::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    border-radius: inherit;
    z-index: 1;
}

/* Performance Audit & Optimization Wizard Styles - CRITICAL STYLES */
#performance-audit-wizard.redco-performance-wizard-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.8) !important;
    z-index: 999999 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
}

#performance-audit-wizard.redco-performance-wizard-overlay.show {
    opacity: 1 !important;
    visibility: visible !important;
}

#performance-audit-wizard .redco-performance-wizard-modal {
    background: #fff !important;
    border-radius: 8px !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
    width: 90% !important;
    max-width: 800px !important;
    max-height: 90vh !important;
    overflow: hidden !important;
    transform: scale(0.9) translateY(20px) !important;
    transition: all 0.3s ease !important;
    position: relative !important;
}

#performance-audit-wizard.show .redco-performance-wizard-modal {
    transform: scale(1) translateY(0) !important;
}

.redco-performance-wizard-modal .wizard-header {
    background: linear-gradient(135deg, #4CAF50, #45a049) !important;
    color: white !important;
    padding: 20px 30px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    margin: 0 !important;
    border: none !important;
}

.redco-performance-wizard-modal .wizard-header h2 {
    margin: 0 !important;
    font-size: 20px !important;
    font-weight: 600 !important;
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
    color: white !important;
}

.redco-performance-wizard-modal .wizard-close {
    background: none !important;
    border: none !important;
    color: white !important;
    font-size: 18px !important;
    cursor: pointer !important;
    padding: 5px !important;
    border-radius: 4px !important;
    transition: background-color 0.2s ease !important;
}

.redco-performance-wizard-modal .wizard-close:hover {
    background: rgba(255, 255, 255, 0.2) !important;
}

.redco-performance-wizard-modal .wizard-progress {
    background: #f8f9fa !important;
    padding: 20px 30px !important;
    border-bottom: 1px solid #e9ecef !important;
    margin: 0 !important;
}

.redco-performance-wizard-modal .progress-steps {
    display: flex !important;
    justify-content: space-between !important;
    margin-bottom: 15px !important;
}

.redco-performance-wizard-modal .step {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    gap: 8px !important;
    flex: 1 !important;
    position: relative !important;
}

.redco-performance-wizard-modal .step:not(:last-child)::after {
    content: '' !important;
    position: absolute !important;
    top: 15px !important;
    left: 60% !important;
    right: -40% !important;
    height: 2px !important;
    background: #e9ecef !important;
    z-index: 1 !important;
}

.redco-performance-wizard-modal .step.completed:not(:last-child)::after {
    background: #4CAF50 !important;
}

.redco-performance-wizard-modal .step-number {
    width: 30px !important;
    height: 30px !important;
    border-radius: 50% !important;
    background: #e9ecef !important;
    color: #6c757d !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    position: relative !important;
    z-index: 2 !important;
    transition: all 0.3s ease !important;
}

.redco-performance-wizard-modal .step.active .step-number {
    background: #4CAF50 !important;
    color: white !important;
}

.redco-performance-wizard-modal .step.completed .step-number {
    background: #4CAF50 !important;
    color: white !important;
}

.redco-performance-wizard-modal .step-label {
    font-size: 12px !important;
    color: #6c757d !important;
    font-weight: 500 !important;
    text-align: center !important;
}

.redco-performance-wizard-modal .step.active .step-label {
    color: #4CAF50 !important;
    font-weight: 600 !important;
}

.redco-performance-wizard-modal .progress-bar {
    width: 100% !important;
    height: 4px !important;
    background: #e9ecef !important;
    border-radius: 2px !important;
    overflow: hidden !important;
}

.redco-performance-wizard-modal .progress-fill {
    height: 100% !important;
    background: linear-gradient(90deg, #4CAF50, #45a049) !important;
    border-radius: 2px !important;
    transition: width 0.5s ease !important;
}

.redco-performance-wizard-modal .wizard-content {
    padding: 30px !important;
    max-height: 60vh !important;
    overflow-y: auto !important;
}

.redco-performance-wizard-modal .wizard-step {
    display: none !important;
}

.redco-performance-wizard-modal .wizard-step.active {
    display: block !important;
}

.redco-performance-wizard-modal .step-content h3 {
    margin: 0 0 15px 0 !important;
    color: #2c3e50 !important;
    font-size: 18px !important;
    font-weight: 600 !important;
}

.redco-performance-wizard-modal .step-content p {
    margin: 0 0 20px 0 !important;
    color: #6c757d !important;
    line-height: 1.6 !important;
}

.redco-performance-wizard-modal .analysis-status,
.redco-performance-wizard-modal .application-progress {
    display: flex !important;
    flex-direction: column !important;
    gap: 15px !important;
    margin: 20px 0 !important;
}

.redco-performance-wizard-modal .status-item,
.redco-performance-wizard-modal .progress-item {
    display: flex !important;
    align-items: center !important;
    gap: 15px !important;
    padding: 15px !important;
    background: #f8f9fa !important;
    border-radius: 6px !important;
    border-left: 4px solid #e9ecef !important;
    transition: all 0.3s ease !important;
}

.redco-performance-wizard-modal .status-item .dashicons,
.redco-performance-wizard-modal .progress-item .dashicons {
    font-size: 18px !important;
    color: #6c757d !important;
}

.redco-performance-wizard-modal .status-text,
.redco-performance-wizard-modal .progress-text {
    flex: 1 !important;
    font-weight: 500 !important;
    color: #2c3e50 !important;
}

.redco-performance-wizard-modal .status-indicator,
.redco-performance-wizard-modal .progress-indicator {
    width: 20px !important;
    height: 20px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.redco-performance-wizard-modal .status-indicator.pending,
.redco-performance-wizard-modal .progress-indicator.pending {
    background: #e9ecef !important;
    border: 2px solid #dee2e6 !important;
}

.redco-performance-wizard-modal .status-indicator.loading,
.redco-performance-wizard-modal .progress-indicator.loading {
    background: #fff3cd !important;
    border: 2px solid #ffc107 !important;
    animation: pulse 1.5s infinite !important;
}

.redco-performance-wizard-modal .status-indicator.success,
.redco-performance-wizard-modal .progress-indicator.success {
    background: #d4edda !important;
    border: 2px solid #4CAF50 !important;
}

.redco-performance-wizard-modal .status-indicator.success::after,
.redco-performance-wizard-modal .progress-indicator.success::after {
    content: '✓' !important;
    color: #4CAF50 !important;
    font-weight: bold !important;
    font-size: 12px !important;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.redco-performance-wizard-modal .step-actions {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-top: 30px !important;
    margin-bottom: 20px !important;
    padding-top: 20px !important;
    padding-bottom: 20px !important;
    border-top: 1px solid #e9ecef !important;
}

.redco-performance-wizard-modal .wizard-btn {
    padding: 12px 24px !important;
    border: none !important;
    border-radius: 6px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    transition: all 0.2s ease !important;
    text-decoration: none !important;
    font-size: 14px !important;
}

.redco-performance-wizard-modal .wizard-btn.primary {
    background: #4CAF50 !important;
    color: white !important;
}

.redco-performance-wizard-modal .wizard-btn.primary:hover {
    background: #45a049 !important;
    transform: translateY(-1px) !important;
}

.redco-performance-wizard-modal .wizard-btn.secondary {
    background: #6c757d !important;
    color: white !important;
}

.redco-performance-wizard-modal .wizard-btn.secondary:hover {
    background: #5a6268 !important;
}

.redco-performance-wizard-modal .wizard-btn.success {
    background: #28a745 !important;
    color: white !important;
}

.redco-performance-wizard-modal .wizard-btn:disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    transform: none !important;
}

.verification-summary {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.verification-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;
}

.verification-item.success .dashicons {
    color: #4CAF50;
    font-size: 24px;
}

.verification-content h4 {
    margin: 0 0 8px 0;
    color: #2c3e50;
    font-size: 16px;
}

.verification-content p {
    margin: 0;
    color: #6c757d;
}

.expected-improvements {
    margin: 20px 0;
}

.expected-improvements h4 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 16px;
}

.improvement-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.improvement-item {
    background: white;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #4CAF50;
    text-align: center;
}

.improvement-label {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 5px;
}

.improvement-value {
    font-size: 18px;
    font-weight: 600;
    color: #4CAF50;
}

.next-steps {
    margin-top: 20px;
}

.next-steps h4 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 16px;
}

.next-steps ul {
    margin: 0;
    padding-left: 20px;
}

.next-steps li {
    margin-bottom: 8px;
    color: #6c757d;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .redco-performance-wizard-modal {
        width: 95%;
        margin: 20px;
    }

    .wizard-header,
    .wizard-progress,
    .wizard-content {
        padding: 20px;
    }

    .progress-steps {
        flex-wrap: wrap;
        gap: 10px;
    }

    .step {
        min-width: 80px;
    }

    .step:not(:last-child)::after {
        display: none;
    }

    .improvement-grid {
        grid-template-columns: 1fr;
    }

    .step-actions {
        flex-direction: column;
        gap: 10px;
    }

    .wizard-btn {
        width: 100%;
        justify-content: center;
    }
}
