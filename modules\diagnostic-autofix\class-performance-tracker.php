<?php
/**
 * Performance Tracker for Diagnostic & Auto-Fix Module
 * 
 * Monitors and tracks performance metrics for optimization verification
 * 
 * @package RedcoOptimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Diagnostic_Performance_Tracker {
    
    /**
     * Performance metrics storage
     */
    private static $metrics = array();
    
    /**
     * Performance thresholds
     */
    const TARGET_LOAD_TIME = 2.0; // 2 seconds
    const TARGET_CACHE_HIT_RATIO = 0.8; // 80%
    const MAX_AJAX_REQUESTS = 3; // per page load
    const MAX_DATABASE_QUERIES = 2; // per page load
    
    /**
     * Initialize performance tracking
     */
    public static function init() {
        // Track page load performance
        add_action('wp_footer', array(__CLASS__, 'track_page_performance'));
        add_action('admin_footer', array(__CLASS__, 'track_admin_performance'));
        
        // Track AJAX performance
        add_action('wp_ajax_redco_track_performance', array(__CLASS__, 'ajax_track_performance'));
        
        // Schedule performance cleanup
        if (!wp_next_scheduled('redco_diagnostic_performance_cleanup')) {
            wp_schedule_event(time(), 'daily', 'redco_diagnostic_performance_cleanup');
        }
        add_action('redco_diagnostic_performance_cleanup', array(__CLASS__, 'cleanup_old_metrics'));
    }
    
    /**
     * Track admin page performance
     */
    public static function track_admin_performance() {
        // Only track on diagnostic pages
        if (!isset($_GET['page']) || strpos($_GET['page'], 'redco-optimizer') === false) {
            return;
        }
        
        $is_diagnostic_tab = isset($_GET['tab']) && $_GET['tab'] === 'diagnostic-autofix';
        $is_main_dashboard = !isset($_GET['tab']);
        
        if (!$is_diagnostic_tab && !$is_main_dashboard) {
            return;
        }
        
        // Output performance tracking script
        ?>
        <script>
        (function() {
            // Performance tracking for Diagnostic & Auto-Fix module
            const performanceTracker = {
                startTime: performance.now(),
                metrics: {
                    loadTime: 0,
                    ajaxRequests: 0,
                    cacheHits: 0,
                    cacheMisses: 0,
                    errors: 0
                },
                
                init: function() {
                    this.trackLoadTime();
                    this.trackAjaxRequests();
                    this.trackCachePerformance();
                    this.trackErrors();
                    
                    // Send metrics after page is fully loaded
                    window.addEventListener('load', () => {
                        setTimeout(() => {
                            this.sendMetrics();
                        }, 1000);
                    });
                },
                
                trackLoadTime: function() {
                    // Track when diagnostic content becomes interactive
                    const observer = new MutationObserver((mutations) => {
                        mutations.forEach((mutation) => {
                            if (mutation.type === 'childList') {
                                const diagnosticContent = document.querySelector('.redco-module-content, .diagnostic-autofix-content');
                                if (diagnosticContent && !diagnosticContent.querySelector('.redco-tab-loading')) {
                                    this.metrics.loadTime = performance.now() - this.startTime;
                                    observer.disconnect();
                                }
                            }
                        });
                    });
                    
                    observer.observe(document.body, {
                        childList: true,
                        subtree: true
                    });
                },
                
                trackAjaxRequests: function() {
                    // Override jQuery AJAX to track requests
                    if (typeof jQuery !== 'undefined') {
                        const originalAjax = jQuery.ajax;
                        const self = this;
                        
                        jQuery.ajax = function(options) {
                            if (options.url && options.url.includes('redco_')) {
                                self.metrics.ajaxRequests++;
                            }
                            return originalAjax.apply(this, arguments);
                        };
                    }
                },
                
                trackCachePerformance: function() {
                    // Track cache hits/misses
                    if (window.RedcoPerformanceCache) {
                        const originalGet = window.RedcoPerformanceCache.get;
                        const self = this;
                        
                        window.RedcoPerformanceCache.get = function(key) {
                            const result = originalGet.call(this, key);
                            if (result !== null) {
                                self.metrics.cacheHits++;
                            } else {
                                self.metrics.cacheMisses++;
                            }
                            return result;
                        };
                    }
                },
                
                trackErrors: function() {
                    // Track JavaScript errors
                    window.addEventListener('error', (e) => {
                        if (e.filename && e.filename.includes('diagnostic-autofix')) {
                            this.metrics.errors++;
                        }
                    });
                },
                
                sendMetrics: function() {
                    // Only send if we have meaningful data
                    if (this.metrics.loadTime === 0) {
                        this.metrics.loadTime = performance.now() - this.startTime;
                    }
                    
                    // Add performance grades
                    const grades = this.calculateGrades();
                    
                    jQuery.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'redco_track_performance',
                            nonce: '<?php echo wp_create_nonce('redco_performance_nonce'); ?>',
                            metrics: this.metrics,
                            grades: grades,
                            page: 'diagnostic-autofix',
                            timestamp: Date.now()
                        },
                        success: function(response) {
                            if (response.success && window.console) {
                                console.log('Redco Performance Metrics:', response.data);
                            }
                        }
                    });
                },
                
                calculateGrades: function() {
                    const grades = {};
                    
                    // Load time grade (A: <2s, B: <3s, C: <5s, D: <8s, F: >8s)
                    const loadTimeSeconds = this.metrics.loadTime / 1000;
                    if (loadTimeSeconds < 2) grades.loadTime = 'A';
                    else if (loadTimeSeconds < 3) grades.loadTime = 'B';
                    else if (loadTimeSeconds < 5) grades.loadTime = 'C';
                    else if (loadTimeSeconds < 8) grades.loadTime = 'D';
                    else grades.loadTime = 'F';
                    
                    // Cache efficiency grade
                    const totalCacheRequests = this.metrics.cacheHits + this.metrics.cacheMisses;
                    const cacheHitRatio = totalCacheRequests > 0 ? this.metrics.cacheHits / totalCacheRequests : 0;
                    if (cacheHitRatio >= 0.9) grades.cache = 'A';
                    else if (cacheHitRatio >= 0.8) grades.cache = 'B';
                    else if (cacheHitRatio >= 0.6) grades.cache = 'C';
                    else if (cacheHitRatio >= 0.4) grades.cache = 'D';
                    else grades.cache = 'F';
                    
                    // AJAX efficiency grade
                    if (this.metrics.ajaxRequests <= 2) grades.ajax = 'A';
                    else if (this.metrics.ajaxRequests <= 4) grades.ajax = 'B';
                    else if (this.metrics.ajaxRequests <= 6) grades.ajax = 'C';
                    else if (this.metrics.ajaxRequests <= 10) grades.ajax = 'D';
                    else grades.ajax = 'F';
                    
                    // Error rate grade
                    if (this.metrics.errors === 0) grades.errors = 'A';
                    else if (this.metrics.errors <= 1) grades.errors = 'B';
                    else if (this.metrics.errors <= 2) grades.errors = 'C';
                    else if (this.metrics.errors <= 5) grades.errors = 'D';
                    else grades.errors = 'F';
                    
                    return grades;
                }
            };
            
            // Initialize tracking
            performanceTracker.init();
        })();
        </script>
        <?php
    }
    
    /**
     * AJAX handler for performance tracking
     */
    public static function ajax_track_performance() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_performance_nonce')) {
            wp_die('Security check failed');
        }
        
        $metrics = isset($_POST['metrics']) ? $_POST['metrics'] : array();
        $grades = isset($_POST['grades']) ? $_POST['grades'] : array();
        $page = isset($_POST['page']) ? sanitize_text_field($_POST['page']) : 'unknown';
        $timestamp = isset($_POST['timestamp']) ? intval($_POST['timestamp']) : time() * 1000;
        
        // Store performance data
        $performance_data = array(
            'timestamp' => $timestamp,
            'page' => $page,
            'metrics' => $metrics,
            'grades' => $grades,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'url' => $_SERVER['REQUEST_URI'] ?? ''
        );
        
        // Store in database
        self::store_performance_data($performance_data);
        
        // Calculate performance summary
        $summary = self::calculate_performance_summary($performance_data);
        
        wp_send_json_success(array(
            'message' => 'Performance metrics recorded',
            'summary' => $summary,
            'recommendations' => self::get_performance_recommendations($grades)
        ));
    }
    
    /**
     * Store performance data
     */
    private static function store_performance_data($data) {
        // Store in daily aggregated format
        $date = date('Y-m-d', $data['timestamp'] / 1000);
        $option_key = 'redco_diagnostic_performance_' . $date;
        
        $daily_data = get_option($option_key, array(
            'date' => $date,
            'total_loads' => 0,
            'avg_load_time' => 0,
            'avg_ajax_requests' => 0,
            'avg_cache_hit_ratio' => 0,
            'total_errors' => 0,
            'grade_distribution' => array(),
            'raw_data' => array()
        ));
        
        // Update aggregated data
        $daily_data['total_loads']++;
        $daily_data['avg_load_time'] = (($daily_data['avg_load_time'] * ($daily_data['total_loads'] - 1)) + $data['metrics']['loadTime']) / $daily_data['total_loads'];
        $daily_data['avg_ajax_requests'] = (($daily_data['avg_ajax_requests'] * ($daily_data['total_loads'] - 1)) + $data['metrics']['ajaxRequests']) / $daily_data['total_loads'];
        
        $cache_total = $data['metrics']['cacheHits'] + $data['metrics']['cacheMisses'];
        $cache_ratio = $cache_total > 0 ? $data['metrics']['cacheHits'] / $cache_total : 0;
        $daily_data['avg_cache_hit_ratio'] = (($daily_data['avg_cache_hit_ratio'] * ($daily_data['total_loads'] - 1)) + $cache_ratio) / $daily_data['total_loads'];
        
        $daily_data['total_errors'] += $data['metrics']['errors'];
        
        // Track grade distribution
        foreach ($data['grades'] as $metric => $grade) {
            if (!isset($daily_data['grade_distribution'][$metric])) {
                $daily_data['grade_distribution'][$metric] = array();
            }
            if (!isset($daily_data['grade_distribution'][$metric][$grade])) {
                $daily_data['grade_distribution'][$metric][$grade] = 0;
            }
            $daily_data['grade_distribution'][$metric][$grade]++;
        }
        
        // Store raw data (keep last 50 entries)
        $daily_data['raw_data'][] = $data;
        if (count($daily_data['raw_data']) > 50) {
            $daily_data['raw_data'] = array_slice($daily_data['raw_data'], -50);
        }
        
        update_option($option_key, $daily_data, false); // Don't autoload
    }
    
    /**
     * Calculate performance summary
     */
    private static function calculate_performance_summary($data) {
        $load_time_seconds = $data['metrics']['loadTime'] / 1000;
        $cache_total = $data['metrics']['cacheHits'] + $data['metrics']['cacheMisses'];
        $cache_hit_ratio = $cache_total > 0 ? ($data['metrics']['cacheHits'] / $cache_total) * 100 : 0;
        
        return array(
            'load_time' => round($load_time_seconds, 2) . 's',
            'load_time_grade' => $data['grades']['loadTime'],
            'ajax_requests' => $data['metrics']['ajaxRequests'],
            'cache_hit_ratio' => round($cache_hit_ratio, 1) . '%',
            'cache_grade' => $data['grades']['cache'],
            'errors' => $data['metrics']['errors'],
            'overall_grade' => self::calculate_overall_grade($data['grades'])
        );
    }
    
    /**
     * Calculate overall performance grade
     */
    private static function calculate_overall_grade($grades) {
        $grade_values = array('A' => 4, 'B' => 3, 'C' => 2, 'D' => 1, 'F' => 0);
        $total_value = 0;
        $count = 0;
        
        foreach ($grades as $grade) {
            $total_value += $grade_values[$grade];
            $count++;
        }
        
        if ($count === 0) return 'F';
        
        $avg_value = $total_value / $count;
        
        if ($avg_value >= 3.5) return 'A';
        if ($avg_value >= 2.5) return 'B';
        if ($avg_value >= 1.5) return 'C';
        if ($avg_value >= 0.5) return 'D';
        return 'F';
    }
    
    /**
     * Get performance recommendations
     */
    private static function get_performance_recommendations($grades) {
        $recommendations = array();
        
        if ($grades['loadTime'] === 'D' || $grades['loadTime'] === 'F') {
            $recommendations[] = 'Consider enabling more aggressive caching or optimizing database queries';
        }
        
        if ($grades['cache'] === 'D' || $grades['cache'] === 'F') {
            $recommendations[] = 'Cache hit ratio is low - consider increasing cache duration or improving cache keys';
        }
        
        if ($grades['ajax'] === 'D' || $grades['ajax'] === 'F') {
            $recommendations[] = 'Too many AJAX requests - consider batching requests or implementing better caching';
        }
        
        if ($grades['errors'] === 'D' || $grades['errors'] === 'F') {
            $recommendations[] = 'JavaScript errors detected - check browser console for details';
        }
        
        if (empty($recommendations)) {
            $recommendations[] = 'Performance is optimal! Keep up the good work.';
        }
        
        return $recommendations;
    }
    
    /**
     * Get performance metrics for dashboard
     */
    public static function get_performance_metrics($days = 7) {
        $metrics = array();
        
        for ($i = 0; $i < $days; $i++) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $option_key = 'redco_diagnostic_performance_' . $date;
            $daily_data = get_option($option_key);
            
            if ($daily_data) {
                $metrics[] = $daily_data;
            }
        }
        
        return array_reverse($metrics); // Oldest first
    }
    
    /**
     * Cleanup old performance data
     */
    public static function cleanup_old_metrics() {
        global $wpdb;
        
        // Keep 30 days of data
        $cutoff_date = date('Y-m-d', strtotime('-30 days'));
        
        $old_options = $wpdb->get_col($wpdb->prepare("
            SELECT option_name 
            FROM {$wpdb->options} 
            WHERE option_name LIKE 'redco_diagnostic_performance_%' 
            AND option_name < %s
        ", 'redco_diagnostic_performance_' . $cutoff_date));
        
        foreach ($old_options as $option_name) {
            delete_option($option_name);
        }
    }
}

// Initialize performance tracking
Redco_Diagnostic_Performance_Tracker::init();
