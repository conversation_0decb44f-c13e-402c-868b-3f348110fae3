-- =====================================================
-- Redco Optimizer WebP Recovery Script
-- =====================================================
-- This script helps recover original images from WebP files
-- when original images were deleted during WebP conversion
-- 
-- IMPORTANT: Run this in phpMyAdmin or MySQL command line
-- Make sure to backup your database before running!
-- =====================================================

-- Step 1: Identify all converted WebP images with original format information
-- =====================================================

SELECT 
    'ANALYSIS: WebP Converted Images' as analysis_type,
    COUNT(*) as total_converted_images
FROM wp_postmeta 
WHERE meta_key = '_webp_conversion_data' 
AND meta_value LIKE '%"converted":true%';

-- Step 2: Extract original format information from conversion metadata
-- =====================================================

SELECT 
    pm.post_id,
    p.post_title,
    p.post_mime_type as current_mime_type,
    CASE 
        WHEN pm.meta_value LIKE '%"file_path"%jpg%' OR pm.meta_value LIKE '%"file_path"%jpeg%' THEN 'image/jpeg'
        WHEN pm.meta_value LIKE '%"file_path"%png%' THEN 'image/png'
        WHEN pm.meta_value LIKE '%"file_path"%gif%' THEN 'image/gif'
        ELSE 'unknown'
    END as original_format,
    CASE 
        WHEN pm.meta_value LIKE '%"file_path"%jpg%' OR pm.meta_value LIKE '%"file_path"%jpeg%' THEN 'jpg'
        WHEN pm.meta_value LIKE '%"file_path"%png%' THEN 'png'
        WHEN pm.meta_value LIKE '%"file_path"%gif%' THEN 'gif'
        ELSE 'unknown'
    END as original_extension,
    pm.meta_value as conversion_data
FROM wp_postmeta pm
INNER JOIN wp_posts p ON pm.post_id = p.ID
WHERE pm.meta_key = '_webp_conversion_data' 
AND pm.meta_value LIKE '%"converted":true%'
AND p.post_type = 'attachment'
ORDER BY pm.post_id;

-- Step 3: Identify images that need recovery (WebP mime type but have conversion data)
-- =====================================================

SELECT 
    'RECOVERY NEEDED' as status,
    pm.post_id,
    p.post_title,
    p.post_mime_type,
    CASE 
        WHEN pm.meta_value LIKE '%"file_path"%jpg%' OR pm.meta_value LIKE '%"file_path"%jpeg%' THEN 'image/jpeg'
        WHEN pm.meta_value LIKE '%"file_path"%png%' THEN 'image/png'
        WHEN pm.meta_value LIKE '%"file_path"%gif%' THEN 'image/gif'
        ELSE 'unknown'
    END as should_be_mime_type
FROM wp_postmeta pm
INNER JOIN wp_posts p ON pm.post_id = p.ID
WHERE pm.meta_key = '_webp_conversion_data' 
AND pm.meta_value LIKE '%"converted":true%'
AND p.post_type = 'attachment'
AND p.post_mime_type = 'image/webp'
ORDER BY pm.post_id;

-- Step 4: Recovery Script - Restore original MIME types
-- =====================================================
-- CAUTION: This will update your database!
-- Only run this after confirming the analysis above looks correct

-- Restore JPEG images
UPDATE wp_posts p
INNER JOIN wp_postmeta pm ON p.ID = pm.post_id
SET p.post_mime_type = 'image/jpeg'
WHERE pm.meta_key = '_webp_conversion_data' 
AND pm.meta_value LIKE '%"converted":true%'
AND (pm.meta_value LIKE '%"file_path"%jpg%' OR pm.meta_value LIKE '%"file_path"%jpeg%')
AND p.post_type = 'attachment'
AND p.post_mime_type = 'image/webp';

-- Restore PNG images  
UPDATE wp_posts p
INNER JOIN wp_postmeta pm ON p.ID = pm.post_id
SET p.post_mime_type = 'image/png'
WHERE pm.meta_key = '_webp_conversion_data' 
AND pm.meta_value LIKE '%"converted":true%'
AND pm.meta_value LIKE '%"file_path"%png%'
AND p.post_type = 'attachment'
AND p.post_mime_type = 'image/webp';

-- Restore GIF images
UPDATE wp_posts p
INNER JOIN wp_postmeta pm ON p.ID = pm.post_id
SET p.post_mime_type = 'image/gif'
WHERE pm.meta_key = '_webp_conversion_data' 
AND pm.meta_value LIKE '%"converted":true%'
AND pm.meta_value LIKE '%"file_path"%gif%'
AND p.post_type = 'attachment'
AND p.post_mime_type = 'image/webp';

-- Step 5: Extract file paths for manual file recovery
-- =====================================================

SELECT 
    'FILE RECOVERY INFO' as info_type,
    pm.post_id,
    p.post_title,
    SUBSTRING_INDEX(SUBSTRING_INDEX(pm.meta_value, '"file_path":"', -1), '"', 1) as original_file_path,
    SUBSTRING_INDEX(SUBSTRING_INDEX(pm.meta_value, '"webp_path":"', -1), '"', 1) as webp_file_path,
    CASE 
        WHEN pm.meta_value LIKE '%"file_path"%jpg%' OR pm.meta_value LIKE '%"file_path"%jpeg%' THEN 'jpg'
        WHEN pm.meta_value LIKE '%"file_path"%png%' THEN 'png'
        WHEN pm.meta_value LIKE '%"file_path"%gif%' THEN 'gif'
        ELSE 'unknown'
    END as target_extension
FROM wp_postmeta pm
INNER JOIN wp_posts p ON pm.post_id = p.ID
WHERE pm.meta_key = '_webp_conversion_data' 
AND pm.meta_value LIKE '%"converted":true%'
AND p.post_type = 'attachment'
ORDER BY pm.post_id;

-- Step 6: Update attachment file paths to point back to originals
-- =====================================================
-- This assumes you've converted WebP files back to originals on the file system

-- Extract and update file paths
UPDATE wp_postmeta pm
INNER JOIN wp_posts p ON pm.post_id = p.ID
INNER JOIN wp_postmeta conversion_meta ON p.ID = conversion_meta.post_id
SET pm.meta_value = SUBSTRING_INDEX(SUBSTRING_INDEX(conversion_meta.meta_value, '"file_path":"', -1), '"', 1)
WHERE pm.meta_key = '_wp_attached_file'
AND conversion_meta.meta_key = '_webp_conversion_data'
AND conversion_meta.meta_value LIKE '%"converted":true%'
AND p.post_type = 'attachment';

-- Step 7: Clean up WebP conversion metadata (optional)
-- =====================================================
-- Uncomment these lines if you want to remove WebP conversion tracking

-- DELETE FROM wp_postmeta 
-- WHERE meta_key = '_webp_conversion_data';

-- DELETE FROM wp_options 
-- WHERE option_name LIKE '%redco_webp%';

-- Step 8: Verification queries
-- =====================================================

SELECT 
    'VERIFICATION: Updated Images' as verification_type,
    COUNT(*) as total_images,
    post_mime_type
FROM wp_posts 
WHERE post_type = 'attachment' 
AND ID IN (
    SELECT DISTINCT post_id 
    FROM wp_postmeta 
    WHERE meta_key = '_webp_conversion_data'
)
GROUP BY post_mime_type;

-- Final summary
SELECT 
    'RECOVERY SUMMARY' as summary_type,
    COUNT(CASE WHEN p.post_mime_type = 'image/jpeg' THEN 1 END) as restored_jpeg,
    COUNT(CASE WHEN p.post_mime_type = 'image/png' THEN 1 END) as restored_png,
    COUNT(CASE WHEN p.post_mime_type = 'image/gif' THEN 1 END) as restored_gif,
    COUNT(CASE WHEN p.post_mime_type = 'image/webp' THEN 1 END) as still_webp
FROM wp_posts p
INNER JOIN wp_postmeta pm ON p.ID = pm.post_id
WHERE pm.meta_key = '_webp_conversion_data' 
AND pm.meta_value LIKE '%"converted":true%'
AND p.post_type = 'attachment';
