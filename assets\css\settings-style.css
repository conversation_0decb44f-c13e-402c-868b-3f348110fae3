/**
 * Redco Optimizer Settings Page Styles
 * Ultra-clean, minimal interface inspired by modern SaaS applications
 * Maintaining brand identity with maximum simplicity
 */

/* CSS Variables for clean, minimal theming */
:root {
    --redco-primary: #4CAF50;
    --redco-primary-dark: #388E3C;
    --redco-primary-light: #66BB6A;
    --redco-primary-lighter: #E8F5E9;
    --redco-text-primary: #2c3e50;
    --redco-text-secondary: #7f8c8d;
    --redco-text-muted: #bdc3c7;
    --redco-bg-primary: #ffffff;
    --redco-bg-secondary: #fafbfc;
    --redco-border: #e1e8ed;
    --redco-border-light: #f0f3f6;
    --redco-radius: 6px;
    --redco-transition: all 0.15s ease;
    --redco-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    --redco-shadow-hover: 0 2px 8px rgba(0, 0, 0, 0.08);
}

/* Clean base styling */
.redco-optimizer-settings {
    clear: both;
    margin: 0;
    background: var(--redco-bg-secondary);
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: var(--redco-text-primary);
}

.wrap .notice + .redco-optimizer-settings,
.wrap .updated + .redco-optimizer-settings,
.wrap .error + .redco-optimizer-settings {
    margin-top: 0;
}

/* Ultra-clean minimal header */
.redco-optimizer-settings .redco-settings-header {
    background: var(--redco-bg-primary);
    margin: 0;
    padding: 0;
    border-bottom: 1px solid var(--redco-border);
}

.redco-settings-header .settings-header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 32px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.settings-title-section h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--redco-text-primary);
    display: flex;
    align-items: center;
    gap: 12px;
}

.settings-title-section h1 .dashicons {
    font-size: 24px;
    color: var(--redco-primary);
}

.settings-title-section .settings-subtitle {
    margin: 4px 0 0 36px;
    font-size: 14px;
    color: var(--redco-text-secondary);
    font-weight: 400;
}

.redco-settings-header .settings-actions {
    display: flex;
    gap: 8px;
}

.redco-settings-header .settings-actions .button {
    background: var(--redco-primary);
    border: 1px solid var(--redco-primary);
    color: white;
    padding: 8px 16px;
    border-radius: var(--redco-radius);
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    transition: var(--redco-transition);
    display: flex;
    align-items: center;
    gap: 6px;
}

.redco-settings-header .settings-actions .button:hover {
    background: var(--redco-primary-dark);
    border-color: var(--redco-primary-dark);
}

.redco-settings-header .settings-actions .button.button-secondary {
    background: transparent;
    border-color: var(--redco-border);
    color: var(--redco-text-secondary);
}

.redco-settings-header .settings-actions .button.button-secondary:hover {
    background: var(--redco-bg-secondary);
    border-color: var(--redco-primary);
    color: var(--redco-primary);
}

/* Clean container */
.redco-settings-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
}

/* Ultra-clean minimal navigation tabs */
.redco-nav-tab-wrapper {
    display: flex;
    gap: 0;
    margin: 0 0 32px 0;
    border-bottom: 1px solid var(--redco-border);
    background: none;
    padding: 0 24px;
}

.redco-nav-tab {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 20px;
    background: transparent;
    border: none;
    border-bottom: 2px solid transparent;
    text-decoration: none;
    color: var(--redco-text-secondary);
    transition: var(--redco-transition);
    font-weight: 500;
    font-size: 14px;
    position: relative;
}

.redco-nav-tab:hover {
    color: var(--redco-primary);
    background: var(--redco-primary-lighter);
}

.redco-nav-tab.redco-nav-tab-active {
    color: var(--redco-primary);
    border-bottom-color: var(--redco-primary);
    background: transparent;
}

.redco-nav-tab .dashicons {
    font-size: 16px;
    flex-shrink: 0;
}

.redco-nav-tab .tab-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.redco-nav-tab .tab-title {
    font-weight: 500;
    font-size: 14px;
    line-height: 1.2;
}

.redco-nav-tab .tab-description {
    font-size: 12px;
    opacity: 0.7;
    line-height: 1.2;
    display: none; /* Hide descriptions for cleaner look */
}

/* Ultra-clean settings content */
.redco-settings-content {
    background: var(--redco-bg-primary);
    border-radius: var(--redco-radius);
    box-shadow: var(--redco-shadow);
    overflow: hidden;
    margin-bottom: 24px;
}

.settings-content-wrapper {
    padding: 32px;
}

/* Clean settings sections */
.redco-settings-section {
    margin-bottom: 32px;
}

.redco-settings-section:last-child {
    margin-bottom: 0;
}

.settings-section-header {
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--redco-border-light);
}

.settings-section-header h2 {
    margin: 0 0 6px 0;
    color: var(--redco-text-primary);
    font-size: 18px;
    font-weight: 600;
}

.settings-section-header p {
    margin: 0;
    color: var(--redco-text-secondary);
    font-size: 14px;
    line-height: 1.4;
}

/* Clean form styling */
.redco-settings-form {
    background: transparent;
}

/* Hide any WordPress default settings sections since we use custom cards */
.redco-settings-form h2.title,
.redco-settings-form table.form-table {
    display: none;
}

/* Clean settings grid */
.redco-optimizer-settings .settings-cards-grid {
    display: block !important;
    margin-bottom: 24px;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Minimal clean settings cards */
.redco-optimizer-settings .settings-card {
    background: white !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 8px !important;
    margin-bottom: 12px !important;
    transition: all 0.2s ease !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
}

.settings-card:hover {
    border-color: #d1d5db !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
}

.redco-optimizer-settings .settings-card-header {
    padding: 0 !important;
    border-bottom: none !important;
    display: none !important; /* Hide headers for minimal design */
    visibility: hidden !important;
}

.redco-optimizer-settings .settings-card-content {
    padding: 24px !important;
    display: block !important;
    visibility: visible !important;
}

/* Ultra-clean setting items like the screenshot */
.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 0;
    border-bottom: 1px solid #f3f4f6;
}

.setting-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.setting-item:first-child {
    padding-top: 0;
}

.setting-control {
    flex-shrink: 0;
    order: 2;
}

.setting-info {
    flex: 1;
    order: 1;
    margin-right: 16px;
}

.setting-info h4 {
    margin: 0 0 2px 0;
    color: #111827;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.3;
}

.setting-info p {
    margin: 0;
    color: #6b7280;
    line-height: 1.4;
    font-size: 13px;
}

/* Hide extra elements for minimal design */
.setting-warning,
.setting-benefits,
.benefit-item,
.setting-recommendation,
.setting-note {
    display: none !important;
}



/* Modern minimal toggle switch */
.redco-toggle-switch {
    position: relative;
    display: inline-block;
    width: 48px;
    height: 24px;
}

.redco-toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #e5e7eb;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 12px;
    border: 1px solid #d1d5db;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 2px;
    top: 1px;
    background: white;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Enabled state */
.redco-toggle-switch input:checked + .toggle-slider {
    background: var(--redco-primary);
    border-color: var(--redco-primary);
}

.redco-toggle-switch input:checked + .toggle-slider:before {
    transform: translateX(24px);
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

/* Focus state */
.redco-toggle-switch input:focus + .toggle-slider {
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
    outline: none;
}

/* Hover state */
.redco-toggle-switch:hover .toggle-slider {
    border-color: var(--redco-primary);
}

.redco-toggle-switch:hover input:checked + .toggle-slider {
    background: var(--redco-primary-dark);
}

/* Clean radio buttons */
.setting-item input[type="radio"] {
    margin: 0 8px 0 0;
    accent-color: var(--redco-primary);
}

/* Clean checkboxes */
.setting-item input[type="checkbox"] {
    margin: 0 8px 0 0;
    accent-color: var(--redco-primary);
}

/* Role selection grid */
.role-selection-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin-top: 8px;
}

.role-checkbox-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: var(--redco-bg-secondary);
    border: 1px solid var(--redco-border);
    border-radius: var(--redco-radius);
    transition: var(--redco-transition);
}

.role-checkbox-item:hover {
    border-color: var(--redco-primary);
    background: var(--redco-primary-lighter);
}

.role-checkbox-item input[type="checkbox"] {
    margin: 0;
}

.role-checkbox-item label {
    margin: 0;
    font-size: 14px;
    color: var(--redco-text-primary);
    cursor: pointer;
}



/* Hide form footer for minimal design */
.settings-form-footer {
    display: none !important;
}

.redco-save-button.saved {
    background: #27ae60 !important;
    border-color: #27ae60 !important;
}

/* Clean form controls */
.redco-select {
    width: 200px;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    font-size: 14px;
    color: #111827;
    transition: all 0.2s ease;
    font-family: inherit;
}

.redco-select:focus {
    border-color: var(--redco-primary);
    outline: none;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.redco-text-input,
.redco-number-input {
    width: 200px;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    font-size: 14px;
    color: #111827;
    transition: all 0.2s ease;
    font-family: inherit;
}

.redco-text-input:focus,
.redco-number-input:focus {
    border-color: var(--redco-primary);
    outline: none;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.redco-text-input::placeholder,
.redco-number-input::placeholder {
    color: var(--redco-text-muted);
}

/* Clean setup wizard card */
.setup-wizard-card {
    background: var(--redco-bg-primary);
    border: 1px solid var(--redco-border);
    border-radius: var(--redco-radius);
    transition: var(--redco-transition);
}

.setup-wizard-card:hover {
    border-color: var(--redco-primary);
    box-shadow: var(--redco-shadow-hover);
}

.wizard-card-content {
    padding: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 24px;
}

.wizard-info {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
}

.wizard-info .dashicons {
    font-size: 24px;
    color: var(--redco-primary);
    flex-shrink: 0;
}

.wizard-text h3 {
    margin: 0 0 4px 0;
    color: var(--redco-text-primary);
    font-size: 16px;
    font-weight: 600;
}

.wizard-text p {
    margin: 0;
    color: var(--redco-text-secondary);
    line-height: 1.4;
    font-size: 14px;
}

.wizard-actions .button {
    background: var(--redco-primary) !important;
    border: 1px solid var(--redco-primary) !important;
    color: white !important;
    padding: 8px 16px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    border-radius: var(--redco-radius) !important;
    transition: var(--redco-transition) !important;
    display: flex !important;
    align-items: center !important;
    gap: 6px !important;
    box-shadow: none !important;
}

.wizard-actions .button:hover {
    background: var(--redco-primary-dark) !important;
    border-color: var(--redco-primary-dark) !important;
}

/* Custom Select Styling */
.redco-select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    font-size: 14px;
    color: #333;
    transition: all 0.3s ease;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
    padding-right: 40px;
}

.redco-select:focus {
    border-color: #4CAF50;
    outline: none;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

/* Custom Text Input Styling */
.redco-text-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    font-size: 14px;
    color: #333;
    transition: all 0.3s ease;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.redco-text-input:focus {
    border-color: #4CAF50;
    outline: none;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.redco-text-input::placeholder {
    color: #999;
    font-style: italic;
}

/* Setting Benefits */
.setting-benefits {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
}

.benefit-item {
    background: rgba(76, 175, 80, 0.1);
    color: #388E3C;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

/* Setting Recommendation */
.setting-recommendation {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #4CAF50;
    font-size: 14px;
    font-weight: 500;
    margin-top: 8px;
}

.setting-recommendation .dashicons {
    font-size: 16px;
}

/* Setting Note */
.setting-note {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
    font-size: 14px;
    margin-top: 8px;
}

.setting-note .dashicons {
    font-size: 16px;
    color: #2196F3;
}

/* Loading States */
.redco-settings-form.loading {
    opacity: 0.6;
    pointer-events: none;
}

.redco-settings-form.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 32px;
    height: 32px;
    margin: -16px 0 0 -16px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success Messages */
.redco-settings-success {
    background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
    color: white;
    padding: 16px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 4px 16px rgba(76, 175, 80, 0.3);
}

.redco-settings-success .dashicons {
    font-size: 20px;
}

/* Error Messages */
.redco-settings-error {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    color: white;
    padding: 16px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 4px 16px rgba(244, 67, 54, 0.3);
}

.redco-settings-error .dashicons {
    font-size: 20px;
}

/* Advanced Settings Specific Styles */
.settings-card.advanced-warning {
    border-color: #ff9800;
}

.settings-card.advanced-warning .settings-card-header {
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
}

.settings-card.advanced-warning .settings-card-header .dashicons {
    color: #ff9800;
}

/* Role Selection Grid */
.role-selection-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin-top: 12px;
}

.role-checkbox-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.role-checkbox-item:hover {
    background: #e8f5e8;
    border-color: #4CAF50;
}

.role-checkbox-item input[type="checkbox"] {
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .redco-settings-header .settings-header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .redco-nav-tab-wrapper {
        flex-direction: column;
    }

    .redco-nav-tab {
        min-width: auto;
    }

    .settings-cards-grid {
        grid-template-columns: 1fr;
    }

    .wizard-card-content {
        flex-direction: column;
        text-align: center;
    }

    .settings-content-wrapper {
        padding: 20px;
    }

    .setting-item {
        flex-direction: column;
        gap: 12px;
    }

    .setting-control {
        align-self: flex-start;
    }

    .role-selection-grid {
        grid-template-columns: 1fr;
    }

    .settings-form-footer {
        text-align: center;
    }
}

/* Clean responsive design */
@media (max-width: 768px) {
    .redco-settings-header .settings-header-content {
        flex-direction: column;
        gap: 16px;
        text-align: center;
        padding: 24px 16px;
    }

    .settings-title-section .settings-subtitle {
        margin-left: 0;
        text-align: center;
    }

    .redco-settings-container {
        padding: 0 16px;
    }

    .redco-nav-tab-wrapper {
        padding: 0 16px;
        flex-wrap: wrap;
    }

    .redco-nav-tab {
        flex: 1;
        min-width: 120px;
        justify-content: center;
        padding: 12px 16px;
    }

    .settings-content-wrapper {
        padding: 24px 16px;
    }

    .wizard-card-content {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }

    .wizard-info {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .setting-item {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }

    .setting-control {
        align-self: flex-start;
    }
}

/* Print Styles */
@media print {
    .redco-settings-header,
    .redco-nav-tab-wrapper,
    .settings-form-footer {
        display: none;
    }

    .redco-optimizer-settings {
        background: white;
    }

    .redco-settings-content {
        box-shadow: none;
        border: 1px solid #ccc;
    }

    .settings-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .redco-nav-tab {
        border-width: 3px;
    }

    .settings-card {
        border-width: 3px;
    }

    .redco-toggle-switch {
        border: 2px solid currentColor;
    }

    .redco-select,
    .redco-text-input,
    .redco-number-input {
        border-width: 3px;
    }
}

/* Enhanced JavaScript Features */

/* Search Functionality */
.settings-search-container {
    position: relative;
    max-width: 400px;
    margin: 0 auto 32px;
}

.settings-search {
    width: 100%;
    padding: 14px 18px 14px 48px;
    border: 2px solid var(--redco-border);
    border-radius: var(--redco-radius-lg);
    background: var(--redco-bg-primary);
    font-size: 15px;
    color: var(--redco-text-primary);
    transition: var(--redco-transition);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

.settings-search:focus {
    border-color: var(--redco-primary);
    outline: none;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.settings-search-container .dashicons {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--redco-text-muted);
    font-size: 18px;
}

.settings-search:focus + .dashicons {
    color: var(--redco-primary);
}

/* Highlight search matches */
.settings-card.highlight-match {
    border-color: var(--redco-primary);
    box-shadow: var(--redco-shadow-lg);
    transform: translateY(-2px);
}

.no-results {
    grid-column: 1 / -1;
    text-align: center;
    padding: 48px 24px;
    color: var(--redco-text-muted);
    font-size: 16px;
    background: var(--redco-bg-tertiary);
    border-radius: var(--redco-radius-lg);
    border: 2px dashed var(--redco-border);
}

/* Animation Classes */
.settings-card {
    opacity: 0;
    transform: translateY(20px);
    transition: var(--redco-transition);
}

.settings-card.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.redco-settings-content.transitioning {
    opacity: 0.7;
    transform: scale(0.98);
    transition: var(--redco-transition);
}

/* Loading States */
.settings-form-footer.saving::after {
    content: 'Saving...';
    position: absolute;
    right: 120px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--redco-primary);
    font-weight: 600;
    font-size: 14px;
}

.settings-form-footer.saved::after {
    content: '✓ Saved';
    position: absolute;
    right: 120px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--redco-success);
    font-weight: 600;
    font-size: 14px;
}

.redco-save-button.loading {
    opacity: 0.7;
    pointer-events: none;
}

.redco-save-button.success {
    background: linear-gradient(135deg, var(--redco-success) 0%, #2e7d32 100%) !important;
}

/* Enhanced Tooltips */
.redco-tooltip {
    position: fixed;
    background: var(--redco-dark);
    color: white;
    padding: 8px 12px;
    border-radius: var(--redco-radius);
    font-size: 13px;
    font-weight: 500;
    z-index: 10000;
    opacity: 0;
    transform: translateY(5px);
    transition: var(--redco-transition);
    pointer-events: none;
    max-width: 250px;
    text-align: center;
    line-height: 1.4;
}

.redco-tooltip.visible {
    opacity: 1;
    transform: translateY(0);
}

.redco-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--redco-dark);
}

/* Theme Toggle */
.theme-toggle {
    background: rgba(255, 255, 255, 0.15) !important;
    border: 2px solid rgba(255, 255, 255, 0.25) !important;
    color: white !important;
    padding: 12px !important;
    border-radius: var(--redco-radius) !important;
    transition: var(--redco-transition) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 44px !important;
    height: 44px !important;
}

.theme-toggle:hover {
    background: rgba(255, 255, 255, 0.25) !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
    transform: translateY(-2px) !important;
}

/* Legacy notification styles removed - using global toast notification system only */

/* Error States */
.setting-item.has-error {
    background: rgba(244, 67, 54, 0.05);
    border-radius: var(--redco-radius);
    padding: 16px;
    margin: 0 -16px;
}

.setting-item.has-error .setting-info h4 {
    color: var(--redco-danger);
}

input.error,
select.error,
textarea.error {
    border-color: var(--redco-danger) !important;
    box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.1) !important;
}
