<?php
/**
 * Performance Dashboard Tab Template
 * 
 * Displays comprehensive performance monitoring and optimization insights
 * Following the same layout and interface design as other modules
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Initialize help system
$help_system = new Redco_Help_System();

/**
 * Helper function to render help icon
 */
function redco_help_icon($module, $section, $field = '', $tooltip = '') {
    $help_system = new Redco_Help_System();
    return $help_system->render_help_icon($module, $section, $field, $tooltip);
}
?>

<div class="redco-module-tab" data-module="performance-dashboard">
    <!-- Professional Header Section -->
    <div class="module-header-section">
        <!-- Breadcrumb Navigation -->
        <div class="header-breadcrumb">
            <div class="breadcrumb-nav">
                <a href="<?php echo admin_url('admin.php?page=redco-optimizer'); ?>">
                    <span class="dashicons dashicons-performance"></span>
                    <?php _e('Redco Optimizer', 'redco-optimizer'); ?>
                </a>
                <span class="breadcrumb-separator">›</span>
                <span class="breadcrumb-current"><?php _e('Performance Dashboard', 'redco-optimizer'); ?></span>
            </div>
        </div>

        <!-- Main Header Content -->
        <div class="header-content">
            <div class="header-main">
                <div class="header-icon">
                    <span class="dashicons dashicons-chart-line"></span>
                </div>
                <div class="header-text">
                    <h1><?php _e('Performance Dashboard', 'redco-optimizer'); ?></h1>
                    <p><?php _e('Comprehensive performance monitoring, user feedback analysis, and optimization insights for your website.', 'redco-optimizer'); ?></p>
                    
                    <!-- Status Indicators -->
                    <div class="header-status">
                        <div class="status-badge status-<?php echo esc_attr($performance_status); ?>">
                            <span class="dashicons dashicons-performance"></span>
                            <?php echo ucfirst($performance_status); ?> <?php _e('Performance', 'redco-optimizer'); ?>
                        </div>
                        <?php if ($latest_metrics): ?>
                            <div class="status-badge enabled">
                                <span class="dashicons dashicons-yes-alt"></span>
                                <?php _e('Monitoring Active', 'redco-optimizer'); ?>
                            </div>
                        <?php else: ?>
                            <div class="status-badge disabled">
                                <span class="dashicons dashicons-warning"></span>
                                <?php _e('No Data Available', 'redco-optimizer'); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Header Actions -->
            <div class="header-actions">
                <div class="header-action-group">
                    <!-- Header Metrics -->
                    <div class="header-metrics">
                        <?php if ($latest_metrics): ?>
                            <div class="header-metric">
                                <div class="header-metric-value"><?php echo number_format($latest_metrics['avg_load_time'] / 1000, 2); ?>s</div>
                                <div class="header-metric-label"><?php _e('Avg Load Time', 'redco-optimizer'); ?></div>
                            </div>
                            <div class="header-metric">
                                <div class="header-metric-value"><?php echo number_format($latest_metrics['avg_cache_hit_ratio'] * 100, 1); ?>%</div>
                                <div class="header-metric-label"><?php _e('Cache Hit Ratio', 'redco-optimizer'); ?></div>
                            </div>
                            <div class="header-metric">
                                <div class="header-metric-value"><?php echo $latest_metrics['total_errors']; ?></div>
                                <div class="header-metric-label"><?php _e('Errors', 'redco-optimizer'); ?></div>
                            </div>
                        <?php else: ?>
                            <div class="header-metric">
                                <div class="header-metric-value">--</div>
                                <div class="header-metric-label"><?php _e('Load Time', 'redco-optimizer'); ?></div>
                            </div>
                            <div class="header-metric">
                                <div class="header-metric-value">--</div>
                                <div class="header-metric-label"><?php _e('Cache Ratio', 'redco-optimizer'); ?></div>
                            </div>
                            <div class="header-metric">
                                <div class="header-metric-value">--</div>
                                <div class="header-metric-label"><?php _e('Errors', 'redco-optimizer'); ?></div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Quick Actions -->
                    <div class="header-quick-actions">
                        <button class="header-action-btn primary" onclick="location.reload();" data-tooltip="<?php _e('Refresh all performance data', 'redco-optimizer'); ?>">
                            <span class="dashicons dashicons-update"></span>
                            <?php _e('Refresh Data', 'redco-optimizer'); ?>
                        </button>
                        
                        <button class="header-action-btn redco-feedback-trigger" data-tooltip="<?php _e('Provide feedback on performance dashboard', 'redco-optimizer'); ?>">
                            <span class="dashicons dashicons-feedback"></span>
                            <?php _e('Give Feedback', 'redco-optimizer'); ?>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Module Content -->
    <div class="redco-module-content performance-dashboard-layout">
        <div class="redco-content-main">
            
            <!-- Performance Metrics Overview -->
            <div class="redco-card">
                <div class="card-header">
                    <h3>
                        <span class="dashicons dashicons-performance"></span>
                        <?php _e('Performance Metrics Overview', 'redco-optimizer'); ?>
                        <?php echo redco_help_icon('performance-dashboard', 'metrics', '', 'Real-time performance metrics from the Diagnostic & Auto-Fix module'); ?>
                    </h3>
                    <div class="card-actions">
                        <span class="last-updated">
                            <?php if ($latest_metrics): ?>
                                <?php _e('Last updated:', 'redco-optimizer'); ?> <?php echo human_time_diff(time() - 300) . ' ' . __('ago', 'redco-optimizer'); ?>
                            <?php else: ?>
                                <?php _e('No data available', 'redco-optimizer'); ?>
                            <?php endif; ?>
                        </span>
                    </div>
                </div>
                
                <div class="card-content">
                    <?php if ($latest_metrics): ?>
                        <!-- Performance Metrics Display -->
                        <div class="performance-metrics-overview">
                            <div class="performance-status-header">
                                <div class="status-indicator status-<?php echo esc_attr($performance_status); ?>">
                                    <span class="dashicons dashicons-performance"></span>
                                    <span class="status-text"><?php echo ucfirst($performance_status); ?> <?php _e('Performance', 'redco-optimizer'); ?></span>
                                </div>
                                <div class="last-check">
                                    <?php _e('Last updated:', 'redco-optimizer'); ?> <?php echo human_time_diff(time() - 300) . ' ' . __('ago', 'redco-optimizer'); ?>
                                </div>
                            </div>

                            <div class="performance-metrics-grid">
                                <div class="metric-card load-time">
                                    <div class="metric-icon">
                                        <span class="dashicons dashicons-clock"></span>
                                    </div>
                                    <div class="metric-content">
                                        <div class="metric-value">
                                            <?php echo number_format($latest_metrics['avg_load_time'] / 1000, 2); ?>s
                                        </div>
                                        <div class="metric-label"><?php _e('Avg Load Time', 'redco-optimizer'); ?></div>
                                        <div class="metric-target">
                                            <?php _e('Target: <2s', 'redco-optimizer'); ?>
                                        </div>
                                    </div>
                                    <div class="metric-grade grade-<?php echo strtolower(redco_get_load_time_grade($latest_metrics['avg_load_time'])); ?>">
                                        <?php echo redco_get_load_time_grade($latest_metrics['avg_load_time']); ?>
                                    </div>
                                </div>

                                <div class="metric-card cache-performance">
                                    <div class="metric-icon">
                                        <span class="dashicons dashicons-database-view"></span>
                                    </div>
                                    <div class="metric-content">
                                        <div class="metric-value">
                                            <?php echo number_format($latest_metrics['avg_cache_hit_ratio'] * 100, 1); ?>%
                                        </div>
                                        <div class="metric-label"><?php _e('Cache Hit Ratio', 'redco-optimizer'); ?></div>
                                        <div class="metric-target">
                                            <?php _e('Target: >80%', 'redco-optimizer'); ?>
                                        </div>
                                    </div>
                                    <div class="metric-grade grade-<?php echo strtolower(redco_get_cache_grade($latest_metrics['avg_cache_hit_ratio'])); ?>">
                                        <?php echo redco_get_cache_grade($latest_metrics['avg_cache_hit_ratio']); ?>
                                    </div>
                                </div>

                                <div class="metric-card ajax-efficiency">
                                    <div class="metric-icon">
                                        <span class="dashicons dashicons-networking"></span>
                                    </div>
                                    <div class="metric-content">
                                        <div class="metric-value">
                                            <?php echo number_format($latest_metrics['avg_ajax_requests'], 1); ?>
                                        </div>
                                        <div class="metric-label"><?php _e('Avg AJAX Requests', 'redco-optimizer'); ?></div>
                                        <div class="metric-target">
                                            <?php _e('Target: <3', 'redco-optimizer'); ?>
                                        </div>
                                    </div>
                                    <div class="metric-grade grade-<?php echo strtolower(redco_get_ajax_grade($latest_metrics['avg_ajax_requests'])); ?>">
                                        <?php echo redco_get_ajax_grade($latest_metrics['avg_ajax_requests']); ?>
                                    </div>
                                </div>

                                <div class="metric-card error-rate">
                                    <div class="metric-icon">
                                        <span class="dashicons dashicons-warning"></span>
                                    </div>
                                    <div class="metric-content">
                                        <div class="metric-value">
                                            <?php echo $latest_metrics['total_errors']; ?>
                                        </div>
                                        <div class="metric-label"><?php _e('Total Errors', 'redco-optimizer'); ?></div>
                                        <div class="metric-target">
                                            <?php _e('Target: 0', 'redco-optimizer'); ?>
                                        </div>
                                    </div>
                                    <div class="metric-grade grade-<?php echo strtolower(redco_get_error_grade($latest_metrics['total_errors'])); ?>">
                                        <?php echo redco_get_error_grade($latest_metrics['total_errors']); ?>
                                    </div>
                                </div>
                            </div>

                            <div class="performance-chart-container">
                                <h4><?php _e('7-Day Performance Trend', 'redco-optimizer'); ?></h4>
                                <canvas id="performance-trend-chart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="no-data-message">
                            <div class="no-data-icon">
                                <span class="dashicons dashicons-chart-line"></span>
                            </div>
                            <h4><?php _e('No Performance Data Available', 'redco-optimizer'); ?></h4>
                            <p><?php _e('Performance tracking will begin once you start using the Diagnostic & Auto-Fix module. Enable the module and visit its tab to start collecting performance data.', 'redco-optimizer'); ?></p>
                            <div class="no-data-actions">
                                <a href="<?php echo admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix'); ?>" class="button button-primary">
                                    <span class="dashicons dashicons-admin-tools"></span>
                                    <?php _e('Go to Diagnostic Module', 'redco-optimizer'); ?>
                                </a>
                                <a href="<?php echo admin_url('admin.php?page=redco-optimizer-modules'); ?>" class="button">
                                    <span class="dashicons dashicons-admin-plugins"></span>
                                    <?php _e('Enable Modules', 'redco-optimizer'); ?>
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- User Feedback Analysis -->
            <div class="redco-card">
                <div class="card-header">
                    <h3>
                        <span class="dashicons dashicons-feedback"></span>
                        <?php _e('User Feedback Analysis', 'redco-optimizer'); ?>
                        <?php echo redco_help_icon('performance-dashboard', 'feedback', '', 'Analysis of user feedback and experience reports'); ?>
                    </h3>
                    <div class="card-actions">
                        <span class="feedback-count">
                            <?php printf(__('%d feedback submissions', 'redco-optimizer'), $total_feedback); ?>
                        </span>
                    </div>
                </div>
                
                <div class="card-content">
                    <?php if (!empty($feedback_stats)): ?>
                        <div class="feedback-summary-grid">
                            <?php
                            // Calculate feedback summary
                            $avg_rating = array_sum(array_column($feedback_stats, 'avg_performance_rating')) / count($feedback_stats);
                            $experience_totals = array();
                            $issue_totals = array();
                            
                            foreach ($feedback_stats as $day_stats) {
                                foreach ($day_stats['experience_distribution'] as $experience => $count) {
                                    $experience_totals[$experience] = ($experience_totals[$experience] ?? 0) + $count;
                                }
                                foreach ($day_stats['common_issues'] as $issue => $count) {
                                    $issue_totals[$issue] = ($issue_totals[$issue] ?? 0) + $count;
                                }
                            }
                            ?>
                            
                            <div class="feedback-metric">
                                <div class="feedback-metric-icon">
                                    <span class="dashicons dashicons-star-filled"></span>
                                </div>
                                <div class="feedback-metric-content">
                                    <div class="feedback-metric-value"><?php echo number_format($avg_rating, 1); ?>/5</div>
                                    <div class="feedback-metric-label"><?php _e('Average Rating', 'redco-optimizer'); ?></div>
                                </div>
                            </div>
                            
                            <div class="feedback-metric">
                                <div class="feedback-metric-icon">
                                    <span class="dashicons dashicons-smiley"></span>
                                </div>
                                <div class="feedback-metric-content">
                                    <div class="feedback-metric-value"><?php echo $experience_totals['excellent'] ?? 0; ?></div>
                                    <div class="feedback-metric-label"><?php _e('Excellent Reviews', 'redco-optimizer'); ?></div>
                                </div>
                            </div>
                            
                            <div class="feedback-metric">
                                <div class="feedback-metric-icon">
                                    <span class="dashicons dashicons-warning"></span>
                                </div>
                                <div class="feedback-metric-content">
                                    <div class="feedback-metric-value"><?php echo array_sum($issue_totals); ?></div>
                                    <div class="feedback-metric-label"><?php _e('Issues Reported', 'redco-optimizer'); ?></div>
                                </div>
                            </div>
                        </div>
                        
                        <?php if (!empty($issue_totals)): ?>
                            <div class="common-issues">
                                <h4><?php _e('Most Common Issues', 'redco-optimizer'); ?></h4>
                                <div class="issues-list">
                                    <?php
                                    arsort($issue_totals);
                                    $top_issues = array_slice($issue_totals, 0, 5, true);
                                    foreach ($top_issues as $issue => $count):
                                    ?>
                                        <div class="issue-item">
                                            <span class="issue-name"><?php echo ucwords(str_replace('-', ' ', $issue)); ?></span>
                                            <span class="issue-count"><?php echo $count; ?> <?php _e('reports', 'redco-optimizer'); ?></span>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="no-feedback-message">
                            <p><?php _e('No user feedback available yet. Feedback collection will start automatically when users interact with the Diagnostic & Auto-Fix module.', 'redco-optimizer'); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Optimization Opportunities -->
            <div class="redco-card">
                <div class="card-header">
                    <h3>
                        <span class="dashicons dashicons-lightbulb"></span>
                        <?php _e('Optimization Opportunities', 'redco-optimizer'); ?>
                        <?php echo redco_help_icon('performance-dashboard', 'opportunities', '', 'AI-powered optimization recommendations based on performance data and user feedback'); ?>
                    </h3>
                    <div class="card-actions">
                        <?php if ($critical_opportunities > 0 || $high_opportunities > 0): ?>
                            <span class="opportunities-alert">
                                <?php if ($critical_opportunities > 0): ?>
                                    <span class="critical-count"><?php echo $critical_opportunities; ?> <?php _e('Critical', 'redco-optimizer'); ?></span>
                                <?php endif; ?>
                                <?php if ($high_opportunities > 0): ?>
                                    <span class="high-count"><?php echo $high_opportunities; ?> <?php _e('High Priority', 'redco-optimizer'); ?></span>
                                <?php endif; ?>
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="card-content">
                    <?php if (!empty($optimization_opportunities)): ?>
                        <div class="opportunities-list">
                            <?php
                            // Show top 5 opportunities
                            $top_opportunities = array_slice($optimization_opportunities, 0, 5);
                            foreach ($top_opportunities as $opportunity):
                            ?>
                                <div class="opportunity-item priority-<?php echo esc_attr($opportunity['priority']); ?>">
                                    <div class="opportunity-header">
                                        <div class="opportunity-title">
                                            <span class="priority-badge priority-<?php echo esc_attr($opportunity['priority']); ?>">
                                                <?php echo ucfirst($opportunity['priority']); ?>
                                            </span>
                                            <h4><?php echo esc_html($opportunity['title']); ?></h4>
                                        </div>
                                        <div class="opportunity-impact">
                                            <span class="impact-score"><?php echo $opportunity['impact_score']; ?></span>
                                            <span class="impact-label"><?php _e('Impact', 'redco-optimizer'); ?></span>
                                        </div>
                                    </div>
                                    <div class="opportunity-description">
                                        <p><?php echo esc_html($opportunity['description']); ?></p>
                                    </div>
                                    <div class="opportunity-meta">
                                        <span class="effort-estimate">
                                            <span class="dashicons dashicons-clock"></span>
                                            <?php echo $opportunity['estimated_effort']; ?> <?php _e('Effort', 'redco-optimizer'); ?>
                                        </span>
                                        <span class="impact-estimate">
                                            <span class="dashicons dashicons-chart-line"></span>
                                            <?php echo $opportunity['estimated_impact']; ?> <?php _e('Impact', 'redco-optimizer'); ?>
                                        </span>
                                        <span class="category">
                                            <span class="dashicons dashicons-category"></span>
                                            <?php echo ucwords(str_replace('_', ' ', $opportunity['category'])); ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <?php if (count($optimization_opportunities) > 5): ?>
                            <div class="view-all-opportunities">
                                <button class="button" id="load-all-opportunities">
                                    <?php _e('View All Opportunities', 'redco-optimizer'); ?> (<?php echo count($optimization_opportunities); ?>)
                                </button>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="no-opportunities-message">
                            <p><?php _e('No optimization opportunities identified yet. Opportunities will be generated based on performance data and user feedback.', 'redco-optimizer'); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

        </div>

        <!-- Right Sidebar -->
        <div class="redco-content-sidebar">

            <!-- Quick Stats -->
            <div class="redco-card">
                <div class="card-header">
                    <h3>
                        <span class="dashicons dashicons-chart-bar"></span>
                        <?php _e('Quick Stats', 'redco-optimizer'); ?>
                    </h3>
                </div>
                <div class="card-content">
                    <div class="sidebar-stats">
                        <div class="sidebar-stat">
                            <div class="stat-label"><?php _e('Days Tracked', 'redco-optimizer'); ?></div>
                            <div class="stat-value"><?php echo count($performance_metrics); ?></div>
                        </div>
                        <div class="sidebar-stat">
                            <div class="stat-label"><?php _e('User Feedback', 'redco-optimizer'); ?></div>
                            <div class="stat-value"><?php echo $total_feedback; ?></div>
                        </div>
                        <div class="sidebar-stat">
                            <div class="stat-label"><?php _e('Opportunities', 'redco-optimizer'); ?></div>
                            <div class="stat-value"><?php echo count($optimization_opportunities); ?></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Tips -->
            <div class="redco-card">
                <div class="card-header">
                    <h3>
                        <span class="dashicons dashicons-lightbulb"></span>
                        <?php _e('Performance Tips', 'redco-optimizer'); ?>
                    </h3>
                </div>
                <div class="card-content">
                    <div class="performance-tips">
                        <div class="tip-item">
                            <span class="dashicons dashicons-yes-alt"></span>
                            <p><?php _e('Monitor your load times regularly to catch performance regressions early.', 'redco-optimizer'); ?></p>
                        </div>
                        <div class="tip-item">
                            <span class="dashicons dashicons-yes-alt"></span>
                            <p><?php _e('User feedback is invaluable - encourage users to report performance issues.', 'redco-optimizer'); ?></p>
                        </div>
                        <div class="tip-item">
                            <span class="dashicons dashicons-yes-alt"></span>
                            <p><?php _e('Focus on high-impact, low-effort optimizations for quick wins.', 'redco-optimizer'); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="redco-card">
                <div class="card-header">
                    <h3>
                        <span class="dashicons dashicons-admin-tools"></span>
                        <?php _e('Actions', 'redco-optimizer'); ?>
                    </h3>
                </div>
                <div class="card-content">
                    <div class="sidebar-actions">
                        <a href="<?php echo admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix'); ?>" class="sidebar-action-btn primary">
                            <span class="dashicons dashicons-admin-tools"></span>
                            <?php _e('Run Diagnostic Scan', 'redco-optimizer'); ?>
                        </a>
                        <button class="sidebar-action-btn redco-feedback-trigger">
                            <span class="dashicons dashicons-feedback"></span>
                            <?php _e('Submit Feedback', 'redco-optimizer'); ?>
                        </button>
                        <a href="<?php echo admin_url('admin.php?page=redco-optimizer-settings&settings_tab=performance'); ?>" class="sidebar-action-btn">
                            <span class="dashicons dashicons-admin-generic"></span>
                            <?php _e('Performance Settings', 'redco-optimizer'); ?>
                        </a>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

<?php
// Helper functions for grading
function redco_get_load_time_grade($load_time_ms) {
    $seconds = $load_time_ms / 1000;
    if ($seconds < 2) return 'A';
    if ($seconds < 3) return 'B';
    if ($seconds < 5) return 'C';
    if ($seconds < 8) return 'D';
    return 'F';
}

function redco_get_cache_grade($hit_ratio) {
    if ($hit_ratio >= 0.9) return 'A';
    if ($hit_ratio >= 0.8) return 'B';
    if ($hit_ratio >= 0.6) return 'C';
    if ($hit_ratio >= 0.4) return 'D';
    return 'F';
}

function redco_get_ajax_grade($requests) {
    if ($requests <= 2) return 'A';
    if ($requests <= 4) return 'B';
    if ($requests <= 6) return 'C';
    if ($requests <= 10) return 'D';
    return 'F';
}

function redco_get_error_grade($errors) {
    if ($errors == 0) return 'A';
    if ($errors <= 1) return 'B';
    if ($errors <= 2) return 'C';
    if ($errors <= 5) return 'D';
    return 'F';
}
?>

<script>
// Performance Dashboard JavaScript
jQuery(document).ready(function($) {
    // Load all opportunities button
    $('#load-all-opportunities').on('click', function() {
        // AJAX call to load all optimization opportunities
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_get_optimization_opportunities',
                nonce: redcoAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Display all opportunities in a modal or expand the list
                    console.log('All opportunities:', response.data.opportunities);
                    // Implementation for displaying all opportunities
                }
            }
        });
    });

    // Performance trend chart
    <?php if ($latest_metrics && !empty($performance_metrics)): ?>
    const ctx = document.getElementById('performance-trend-chart');
    if (ctx && typeof Chart !== 'undefined') {
        const chartData = <?php echo json_encode($performance_metrics); ?>;

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: chartData.map(d => d.date),
                datasets: [{
                    label: 'Load Time (ms)',
                    data: chartData.map(d => d.avg_load_time),
                    borderColor: '#4CAF50',
                    backgroundColor: 'rgba(76, 175, 80, 0.1)',
                    tension: 0.4
                }, {
                    label: 'AJAX Requests',
                    data: chartData.map(d => d.avg_ajax_requests),
                    borderColor: '#2196F3',
                    backgroundColor: 'rgba(33, 150, 243, 0.1)',
                    tension: 0.4,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Load Time (ms)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'AJAX Requests'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                },
                plugins: {
                    legend: {
                        display: true
                    }
                }
            }
        });
    }
    <?php endif; ?>
});
</script>
