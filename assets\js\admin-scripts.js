/**
 * Admin JavaScript for Redco Optimizer
 *
 * Professional UI/UX interactions with enhanced animations and feedback
 */

(function($) {
    'use strict';

    // PERFORMANCE OPTIMIZED Configuration
    const config = {
        animationDuration: 300,
        debounceDelay: 500,
        autoSaveDelay: 3000, // PERFORMANCE: Increased to 3 seconds to reduce database writes
        toastDuration: 4000,
        performanceUpdateInterval: redcoAjax.settings ? redcoAjax.settings.performanceUpdateInterval : 60000, // PERFORMANCE: Increased to 1 minute
        performanceRetryDelay: 10000, // PERFORMANCE: Increased to 10 seconds on error
        maxAutoSaveRequests: 3, // PERFORMANCE: Limit concurrent auto-save requests
        cacheTimeout: 300000 // PERFORMANCE: 5 minutes cache timeout
    };

    // PERFORMANCE OPTIMIZED Auto-save state management
    let autoSaveTimers = new Map();
    let autoSaveInProgress = new Map();
    let lastSavedData = new Map();
    let autoSaveRequestCount = 0; // PERFORMANCE: Track concurrent requests
    let performanceCache = new Map(); // PERFORMANCE: In-memory cache for API responses

    /**
     * Auto-save functionality for module settings
     *
     * To test auto-save functionality, use these console commands:
     * - RedcoAutoSaveDebug.testCompleteCycle('page-cache') - Test complete cycle
     * - RedcoAutoSaveDebug.simulateChange('page-cache', 'expiration', '3600') - Simulate field change
     * - RedcoAutoSaveDebug.checkSavedData('page-cache') - Check current saved data
     * - RedcoAutoSaveDebug.getCurrentFormData('page-cache') - Get current form data
     */
    function initAutoSave() {

        // Listen for changes on all form inputs within module forms
        $(document).on('input change', '.redco-module-form input, .redco-module-form select, .redco-module-form textarea', function() {
            const $input = $(this);
            const $form = $input.closest('.redco-module-form');
            const module = $form.data('module');

            if (!module) {
                return;
            }

            // Skip if auto-save is already in progress for this module
            if (autoSaveInProgress.get(module)) {
                return;
            }

            // Clear existing timer for this module
            if (autoSaveTimers.has(module)) {
                clearTimeout(autoSaveTimers.get(module));
            }

            // Set new timer for auto-save
            const timer = setTimeout(() => {
                performAutoSave($form, module);
            }, config.autoSaveDelay);

            autoSaveTimers.set(module, timer);
        });

        // SPECIFIC handler for WebP checkboxes (they might not be caught by the general handler)
        $(document).on('change', '.redco-module-form .webp-checkbox', function() {
            const $checkbox = $(this);
            const $form = $checkbox.closest('.redco-module-form');
            const module = $form.data('module');

            if (!module) {
                return;
            }

            // Skip if auto-save is already in progress for this module
            if (autoSaveInProgress.get(module)) {
                return;
            }

            // Clear existing timer for this module
            if (autoSaveTimers.has(module)) {
                clearTimeout(autoSaveTimers.get(module));
            }

            // Set new timer for auto-save
            const timer = setTimeout(() => {
                performAutoSave($form, module);
            }, config.autoSaveDelay);

            autoSaveTimers.set(module, timer);
        });

        // Specifically handle range sliders with additional events
        $(document).on('input change mousemove', '.redco-module-form input[type="range"]', function() {
            const $slider = $(this);
            const $form = $slider.closest('.redco-module-form');
            const module = $form.data('module');

            if (!module) return;

            // Update any associated display elements
            const sliderId = $slider.attr('id');
            if (sliderId) {
                // Update quality display for WebP module
                if (sliderId === 'quality') {
                    $('#quality-value').text($slider.val());
                }
            }

            // Clear existing timer for this module
            if (autoSaveTimers.has(module)) {
                clearTimeout(autoSaveTimers.get(module));
            }

            // Set new timer for auto-save
            const timer = setTimeout(() => {
                performAutoSave($form, module);
            }, config.autoSaveDelay);

            autoSaveTimers.set(module, timer);
        });

        // Handle toggle switches specifically (they might not trigger 'input' event)
        $(document).on('change', '.redco-module-form .toggle-switch input[type="checkbox"]', function() {
            const $checkbox = $(this);
            const $form = $checkbox.closest('.redco-module-form');
            const module = $form.data('module');

            if (!module) return;

            // Clear existing timer
            if (autoSaveTimers.has(module)) {
                clearTimeout(autoSaveTimers.get(module));
            }

            // Set new timer
            const timer = setTimeout(() => {
                performAutoSave($form, module);
            }, config.autoSaveDelay);

            autoSaveTimers.set(module, timer);
        });

        // Handle custom styled checkboxes (like WebP module checkboxes)
        $(document).on('change', '.redco-module-form input[type="checkbox"]:not(.toggle-switch input)', function() {
            const $checkbox = $(this);
            const $form = $checkbox.closest('.redco-module-form');
            const module = $form.data('module');

            if (!module) return;

            // Clear existing timer
            if (autoSaveTimers.has(module)) {
                clearTimeout(autoSaveTimers.get(module));
            }

            // Set new timer
            const timer = setTimeout(() => {
                performAutoSave($form, module);
            }, config.autoSaveDelay);

            autoSaveTimers.set(module, timer);
        });
    }

    /**
     * PERFORMANCE OPTIMIZED: Perform auto-save for a specific module
     */
    function performAutoSave($form, module) {
        // PERFORMANCE: Check if we've exceeded max concurrent requests
        if (autoSaveRequestCount >= config.maxAutoSaveRequests) {
            console.warn('Redco Optimizer: Auto-save request limit reached, skipping save for', module);
            return;
        }

        // Mark auto-save as in progress
        autoSaveInProgress.set(module, true);
        autoSaveRequestCount++;

        // Get form data with proper checkbox handling
        const formData = $form.serializeArray();
        const settings = {};

        // Convert form data to object
        $.each(formData, function(i, field) {
            if (settings[field.name]) {
                // Handle multiple values (checkboxes, multi-select)
                if (!Array.isArray(settings[field.name])) {
                    settings[field.name] = [settings[field.name]];
                }
                settings[field.name].push(field.value);
            } else {
                settings[field.name] = field.value;
            }
        });

        // Handle unchecked checkboxes explicitly
        $form.find('input[type="checkbox"]').each(function() {
            const $checkbox = $(this);
            const name = $checkbox.attr('name');

            if (name && !$checkbox.is(':checked')) {
                // For array checkboxes (name ends with []), ensure we have an empty array
                if (name.endsWith('[]')) {
                    const baseName = name.slice(0, -2);
                    if (!settings[baseName]) {
                        settings[baseName] = [];
                    }
                } else {
                    // For single checkboxes, set to boolean false for better handling
                    if (!settings.hasOwnProperty(name)) {
                        settings[name] = false;
                    }
                }
            } else if (name && $checkbox.is(':checked')) {
                // Ensure checked checkboxes are set to boolean true
                if (!name.endsWith('[]')) {
                    settings[name] = true;
                }
            }
        });

        // Check if data has actually changed
        const currentDataString = JSON.stringify(settings);
        const lastDataString = lastSavedData.get(module);

        if (currentDataString === lastDataString) {
            autoSaveInProgress.set(module, false);
            return;
        }

        // Perform AJAX save
        $.ajax({
            url: redcoAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_save_module_settings',
                module: module,
                settings: settings,
                nonce: redcoAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Store the successfully saved data
                    lastSavedData.set(module, currentDataString);

                    // Use global toast notification system only
                    if (typeof showToast === 'function') {
                        showToast(response.data.message || 'Settings saved successfully', 'success', 2000);
                    }
                } else {
                    // Use toast notification for auto-save errors
                    if (typeof showToast === 'function') {
                        showToast(response.data.message || 'Error saving settings', 'error', 5000);
                    } else if (typeof RedcoToast !== 'undefined') {
                        RedcoToast.error(response.data.message || 'Error saving settings', {
                            title: 'Auto-save Error',
                            duration: 5000
                        });
                    }
                }
            },
            error: function(xhr, status, error) {
                // Use toast notification for AJAX errors
                const errorMessage = xhr.status === 500 ?
                    'Server error (500) - Check error logs for details' :
                    `Network error: ${error}`;

                if (typeof showToast === 'function') {
                    showToast(errorMessage, 'error', 5000);
                } else if (typeof RedcoToast !== 'undefined') {
                    RedcoToast.error(errorMessage, {
                        title: 'Connection Error',
                        duration: 5000
                    });
                }
            },
            complete: function() {
                // PERFORMANCE: Mark auto-save as complete and decrement counter
                autoSaveInProgress.set(module, false);
                autoSaveRequestCount = Math.max(0, autoSaveRequestCount - 1);
            }
        });
    }



    // Configuration loaded

    // Performance monitoring state
    let performanceUpdateTimer = null;
    let performanceUpdateActive = false;

    // Initialize when document is ready
    $(document).ready(function() {

        initializeUI();
        initAutoSave(); // Initialize auto-save functionality
        initModuleActions();
        initSettingsToggles();

        // Bind performance audit wizard button
        $(document).on('click', '.performance-audit-btn', function(e) {
            e.preventDefault();
            openPerformanceAuditWizard();
        });
        initQuickActions();
        initProgressModalSystem(); // Initialize new professional progress modal system
        initFormHandlers();
        initLicenseHandlers();
        initAddonHandlers();
        initKeyboardShortcuts();
        initPerformanceMonitoringUI(); // Initialize UI only, defer API calls
        initHealthMonitorUI(); // Initialize UI only, defer API calls
        initSettingsChangeListener();
        initPageSpeedRefresh();
        initTabLoadingHandlers();
        initModulePageOptimizations(); // Initialize page height management

        // Hide universal loading screen when page is fully loaded
        initPageLoadDetection();

        // Initialize chart immediately if Chart.js is available
        if (typeof Chart !== 'undefined') {
            initCoreWebVitalsChart();
        }

        // Make universal loading functions globally available
        window.RedcoUniversalLoading = {
            show: showUniversalLoadingScreen,
            hide: hideUniversalLoadingScreen
        };

        // PERFORMANCE: Make performance cache globally available
        window.RedcoPerformanceCache = {
            get: function(key) {
                const cached = performanceCache.get(key);
                return cached && (Date.now() - cached.timestamp) < config.cacheTimeout ? cached.data : null;
            },
            set: function(key, data) {
                performanceCache.set(key, {
                    data: data,
                    timestamp: Date.now()
                });
            },
            clear: function(key) {
                if (key) {
                    performanceCache.delete(key);
                } else {
                    performanceCache.clear();
                }
            }
        };
    });

    /**
     * Initialize tab loading handlers
     */
    function initTabLoadingHandlers() {
        // Global tab click handler - show loading screen for module tabs only
        $(document).on('click', 'a[href*="tab="]', function(e) {
            const href = $(this).attr('href');
            const currentTab = getUrlParameter('tab') || 'dashboard';

            // Skip loading overlay for settings navigation (settings page uses settings_tab, not tab)
            if ($(this).closest('.redco-nav-tab-wrapper').length) {
                return;
            }

            // Extract target tab from href
            const targetTab = href.match(/tab=([^&]+)/);
            if (targetTab && targetTab[1] !== currentTab) {
                const tabName = targetTab[1];
                showUniversalLoadingScreen(tabName);
            }
        });

        // Also handle navigation menu clicks
        $(document).on('click', '.redco-nav-item a', function(e) {
            const href = $(this).attr('href');

            if (href && href.includes('tab=')) {
                const targetTab = href.match(/tab=([^&]+)/);
                if (targetTab) {
                    const tabName = targetTab[1];
                    showUniversalLoadingScreen(tabName);
                }
            }
        });
    }

    /**
     * Show universal loading screen for any module
     */
    function showUniversalLoadingScreen(moduleName) {
        // Module display names
        const moduleNames = {
            'dashboard': 'Dashboard',
            'modules': 'Modules Overview',
            'diagnostic-autofix': 'Website Diagnostic & Auto-Fix',
            'page-cache': 'Page Cache',
            'lazy-load': 'Lazy Load Images',
            'css-js-minifier': 'CSS/JS Minifier',
            'database-cleanup': 'Database Cleanup',
            'heartbeat-control': 'Heartbeat Control',
            'wordpress-core-tweaks': 'WordPress Core Tweaks',
            'critical-resource-optimizer': 'Critical Resource Optimizer',
            'pagespeed-diagnostic': 'PageSpeed Diagnostic'
        };

        const displayName = moduleNames[moduleName] || 'Module';

        // Create loading overlay HTML
        const loadingHtml = `
            <div id="redco-universal-loading-overlay" class="redco-universal-loading-overlay">
                <div class="redco-universal-loading-content">
                    <div class="redco-universal-loading-spinner">
                        <div class="redco-universal-spinner-ring"></div>
                        <div class="redco-universal-spinner-ring"></div>
                        <div class="redco-universal-spinner-ring"></div>
                    </div>
                    <div class="redco-universal-loading-text">
                        <h3>Loading ${displayName}</h3>
                        <p>Please wait while we prepare your optimization tools...</p>
                        <div class="redco-universal-loading-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing overlay if present
        $('#redco-universal-loading-overlay').remove();

        // Add to body
        $('body').append(loadingHtml);

        // Show instantly - no delays
        $('#redco-universal-loading-overlay').addClass('show');

    }

    /**
     * Hide universal loading screen
     */
    function hideUniversalLoadingScreen() {
        const $overlay = $('#redco-universal-loading-overlay');

        if ($overlay.length) {
            $overlay.removeClass('show');

            // Remove from DOM after animation
            setTimeout(() => {
                $overlay.remove();
            }, 500);
        }
    }

    /**
     * Get URL parameter value
     */
    function getUrlParameter(name) {
        name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
        const regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
        const results = regex.exec(location.search);
        return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
    }

    /**
     * Initialize page load detection to hide loading screen when content is ready
     */
    function initPageLoadDetection() {
        let checkStartTime = Date.now();
        const maxWaitTime = 10000; // Maximum 10 seconds wait time

        // Wait for all content to be loaded and visible
        function checkContentLoaded() {
            // Check if we've exceeded maximum wait time
            if (Date.now() - checkStartTime > maxWaitTime) {
                hideUniversalLoadingScreen();

                // Start deferred API calls even if forced
                initDeferredAPIUpdates();
                return;
            }

            // Check if main content areas are present and visible
            const mainContent = $('.redco-content, .redco-module-content, .redco-module-tab');
            const sidebar = $('.redco-sidebar, .redco-content-sidebar');

            // Check if content is loaded and visible
            const contentLoaded = mainContent.length > 0 && mainContent.is(':visible');
            const imagesLoaded = checkImagesLoaded();



            if (contentLoaded && imagesLoaded) {
                // Additional check for page-specific content
                setTimeout(() => {
                    // Final check that content is fully rendered
                    if (isContentFullyRendered()) {
                        hideUniversalLoadingScreen();


                        // Start deferred API calls after page is fully loaded
                        initDeferredAPIUpdates();
                    } else {

                        // Continue checking if content is not fully rendered
                        setTimeout(checkContentLoaded, 500); // Longer delay for AJAX content
                    }
                }, 500); // Longer delay for dashboard AJAX content to load
            } else {
                // Keep checking until content is ready
                setTimeout(checkContentLoaded, 300); // Longer initial check interval
            }
        }

        // Start checking after a brief delay to allow initial rendering
        setTimeout(checkContentLoaded, 300);
    }

    /**
     * Check if all images are loaded
     */
    function checkImagesLoaded() {
        const images = $('img:visible');
        let loadedCount = 0;

        if (images.length === 0) return true;

        images.each(function() {
            if (this.complete && this.naturalHeight !== 0) {
                loadedCount++;
            }
        });

        return loadedCount === images.length;
    }

    /**
     * Check if content is fully rendered and visible
     */
    function isContentFullyRendered() {
        // Get current page/tab
        const currentTab = getUrlParameter('tab') || 'dashboard';

        // Dashboard-specific content indicators
        if (currentTab === 'dashboard') {
            // Check if dashboard containers exist
            const dashboardContainers = [
                '.redco-stats-cards',
                '.redco-performance-overview',
                '.redco-performance-health-dashboard',
                '.redco-dashboard'
            ];

            let hasContainers = false;
            for (let selector of dashboardContainers) {
                if ($(selector).length > 0) {
                    hasContainers = true;
                    break;
                }
            }

            if (!hasContainers) {
                return false;
            }

            // Check if AJAX-loaded content is populated
            const statsCards = $('.redco-stats-cards .stat-card');
            const hasStatsData = statsCards.length > 0 && !statsCards.hasClass('loading');

            // Check for actual data in stats cards (not just placeholders)
            let hasRealStatsData = false;
            statsCards.each(function() {
                const $card = $(this);
                const statNumber = $card.find('.stat-number, .score-number, .metric-number').text().trim();
                // Check if it has real data (not empty, not just dashes or placeholders)
                if (statNumber && statNumber !== '-' && statNumber !== '...' && statNumber !== 'Loading...') {
                    hasRealStatsData = true;
                    return false; // break
                }
            });

            // Check for performance health dashboard data
            const healthDashboard = $('.redco-performance-health-dashboard');
            const hasHealthData = healthDashboard.length > 0 && !healthDashboard.hasClass('loading');

            // Check for actual health scores (not placeholders)
            let hasRealHealthData = false;
            if (hasHealthData) {
                const healthScores = healthDashboard.find('.score-number, .metric-value').filter(':visible');
                healthScores.each(function() {
                    const scoreText = $(this).text().trim();
                    if (scoreText && scoreText !== '-' && scoreText !== '...' && scoreText !== 'Loading...') {
                        hasRealHealthData = true;
                        return false; // break
                    }
                });
            }

            // Dashboard is ready when we have containers (don't wait for API data)
            // API data will be loaded asynchronously after page load
            return hasContainers;
        }

        // Module-specific content indicators
        const moduleIndicators = [
            '.redco-card:visible',
            '.module-header-section:visible',
            '.redco-settings-section:visible',
            '.redco-module-content:visible',
            '.redco-module-tab:visible',
            '.module-settings-content:visible',
            '.redco-module-settings:visible'
        ];

        // Check for specific module content based on current tab
        let hasModuleContent = false;
        for (let selector of moduleIndicators) {
            const elements = $(selector);
            if (elements.length > 0 && elements.is(':visible')) {
                hasModuleContent = true;
                break;
            }
        }

        // Additional check for module-specific elements
        if (!hasModuleContent) {
            // Check for settings forms, cards, or other module content
            const additionalSelectors = [
                '.settings-card:visible',
                '.redco-notice:visible',
                '.module-disabled-notice:visible',
                'form.redco-settings-form:visible',
                '.redco-stats-grid:visible'
            ];

            for (let selector of additionalSelectors) {
                if ($(selector).length > 0) {

                    hasModuleContent = true;
                    break;
                }
            }
        }

        // Fallback: check if any substantial content is present
        if (!hasModuleContent) {
            const contentContainers = $('.redco-content, .redco-module-content, .redco-module-tab');
            const hasContent = contentContainers.children().length > 0;

            return hasContent;
        }

        return hasModuleContent;
    }

    /**
     * Initialize UI enhancements
     */
    function initializeUI() {
        // Add toast container for notifications
        $('body').append('<div id="redco-toast-container" class="redco-toast-container"></div>');

        // Keep admin notices visible (user can dismiss manually)
    }

    /**
     * Initialize module action buttons
     */
    function initModuleActions() {
        // Initialize module action buttons

        // Handle module action buttons (enable/disable toggles - NO PROGRESS MODAL)
        $(document).on('click', '.module-action-btn:not(.premium-disabled)', function(e) {
            e.preventDefault();

            const $button = $(this);
            const module = $button.data('module');
            const action = $button.data('action') || $button.attr('data-action');
            const $container = $button.closest('.module-overview-item, .module-card');

            // Validate module data
            if (!module || !action) {
                showToast('Error: Module or action not specified', 'error');
                return;
            }

            // Module toggle actions should NOT trigger progress modals
            // These are simple enable/disable operations that complete quickly

            // Disable button during request
            $button.prop('disabled', true);
            $button.addClass('loading');
            $container.addClass('redco-loading');

            // Add loading spinner (simple loading state, not progress modal)
            const originalContent = $button.html();
            $button.html('<span class="dashicons dashicons-update spin"></span> Processing...');

            // Make AJAX request
            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_toggle_module',
                    module: module,
                    module_action: action,
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        showToast(response.data.message, 'success');

                        // Update button state immediately
                        const enabled = action === 'enable';
                        updateModuleButtonState($button, enabled, module);

                        // Update container state
                        if (enabled) {
                            $container.addClass('enabled').removeClass('disabled');
                        } else {
                            $container.addClass('disabled').removeClass('enabled');
                        }

                        // Update quick action button states
                        updateQuickActionButtonStates(module, enabled);

                        // Refresh page after short delay to ensure consistency
                        setTimeout(function() {
                            window.location.reload();
                        }, 1500);
                    } else {
                        showToast(response.data.message || redcoAjax.strings.error, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    showToast(redcoAjax.strings.error || 'Request failed', 'error');
                },
                complete: function() {
                    // Restore button state
                    $button.removeClass('loading');
                    $button.html(originalContent);
                    $container.removeClass('redco-loading');
                    $button.prop('disabled', false);
                }
            });
        });



        // Check for existing action buttons
        const actionButtons = $('.module-action-btn');
    }

    /**
     * Update module button state
     */
    function updateModuleButtonState($button, enabled, module) {
        if (enabled) {
            $button.removeClass('disabled').addClass('enabled');
            $button.data('action', 'disable');
            $button.html('<span class="dashicons dashicons-yes-alt"></span> Enabled');
            $button.attr('data-tooltip', 'Click to disable this module');
        } else {
            $button.removeClass('enabled').addClass('disabled');
            $button.data('action', 'enable');
            $button.html('<span class="dashicons dashicons-marker"></span> Disabled');
            $button.attr('data-tooltip', 'Click to enable this module');
        }
    }

    /**
     * Initialize settings toggle switches
     */
    function initSettingsToggles() {
        $(document).on('change', '.settings-toggle', function() {
            const $checkbox = $(this);
            const settingGroup = $checkbox.data('setting-group');
            const settingName = $checkbox.data('setting-name');
            const enabled = $checkbox.is(':checked');
            const $container = $checkbox.closest('.setting-item, .settings-card');

            // Disable toggle during request
            $checkbox.prop('disabled', true);
            $container.addClass('redco-loading');

            // Make AJAX request
            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_toggle_setting',
                    setting_group: settingGroup,
                    setting_name: settingName,
                    enabled: enabled,
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        showToast(response.data.message, 'success');

                        // Update UI state if needed
                        if (settingName === 'enable_monitoring') {
                            // Refresh dashboard if monitoring was toggled
                            if (window.location.href.includes('page=redco-optimizer')) {
                                setTimeout(function() {
                                    window.location.reload();
                                }, 1000);
                            }
                        }
                    } else {
                        // Revert checkbox state
                        $checkbox.prop('checked', !enabled);
                        showToast(response.data.message || redcoAjax.strings.error, 'error');
                    }
                },
                error: function() {
                    // Revert checkbox state
                    $checkbox.prop('checked', !enabled);
                    showToast(redcoAjax.strings.error, 'error');
                },
                complete: function() {
                    $container.removeClass('redco-loading');
                    $checkbox.prop('disabled', false);
                }
            });
        });
    }

    /**
     * Initialize quick action buttons
     */
    function initQuickActions() {
        // Quick action functionality will be implemented here
        // Currently placeholder for future implementation
    }

    /**
     * Professional Progress Modal System
     * Real-time progress tracking with actual server data
     */

    // Progress modal state management
    let currentProgressModal = null;
    let progressUpdateInterval = null;
    let progressSessionId = null;
    let currentActionButton = null;
    let originalButtonText = null;

    /**
     * Initialize professional progress modal system
     */
    function initProgressModalSystem() {
        // Attach handlers to all action buttons
        attachProgressModalHandlers();

        // Setup modal container
        setupProgressModalContainer();

        // Professional Progress Modal System initialized

        // Safety mechanism: restore button state on page unload
        $(window).on('beforeunload', function() {
            if (currentActionButton && originalButtonText) {
                restoreButtonState();
            }
        });
    }

    /**
     * Setup progress modal container in DOM
     */
    function setupProgressModalContainer() {
        if (!$('#redco-progress-modal-container').length) {
            $('body').append('<div id="redco-progress-modal-container"></div>');
        }
    }

    /**
     * Attach progress modal handlers to action buttons
     */
    function attachProgressModalHandlers() {
        // Dashboard quick action buttons
        $(document).on('click', '.quick-action-btn[data-action]', handleProgressAction);

        // Module-specific action buttons
        $(document).on('click', '#clear-page-cache', function(e) {
            e.preventDefault();
            startProgressAction('clear_page_cache', $(this));
        });

        $(document).on('click', '#clear-minified-cache', function(e) {
            e.preventDefault();
            startProgressAction('clear_minified_cache', $(this));
        });

        $(document).on('click', '#clear-all-cache', function(e) {
            e.preventDefault();
            startProgressAction('clear_all_cache', $(this));
        });

        $(document).on('click', '#run-cleanup-now, #run-database-cleanup', function(e) {
            e.preventDefault();
            startProgressAction('database_cleanup', $(this));
        });

        // Additional specific action button handlers
        $(document).on('click', '#preload-cache', function(e) {
            e.preventDefault();
            startProgressAction('preload_cache', $(this));
        });

        $(document).on('click', '#optimize-database', function(e) {
            e.preventDefault();
            startProgressAction('optimize_database', $(this));
        });

        // Database cleanup variations
        $(document).on('click', '#clean-database, #cleanup-database, #database-cleanup', function(e) {
            e.preventDefault();
            startProgressAction('database_cleanup', $(this));
        });

        // Performance Audit & Optimization button
        $(document).on('click', '.performance-audit-btn, [data-action="performance_audit_optimization"]', function(e) {
            e.preventDefault();
            openPerformanceAuditWizard();
        });

        // Cache preloading variations
        $(document).on('click', '#preload-cache-now, #cache-preload, #start-preload', function(e) {
            e.preventDefault();
            startProgressAction('preload_cache', $(this));
        });

        // Image optimization
        $(document).on('click', '#optimize-images, #image-optimization', function(e) {
            e.preventDefault();
            startProgressAction('optimize_images', $(this));
        });

        // Asset minification
        $(document).on('click', '#minify-assets, #minify-css-js', function(e) {
            e.preventDefault();
            startProgressAction('minify_assets', $(this));
        });

        // Critical resource optimization
        $(document).on('click', '#optimize-critical-resources', function(e) {
            e.preventDefault();
            startProgressAction('optimize_critical_resources', $(this));
        });

        // Critical CSS generation
        $(document).on('click', '#generate-critical-css', function(e) {
            e.preventDefault();
            startProgressAction('generate_critical_css', $(this));
        });

        // Clear critical cache
        $(document).on('click', '#clear-critical-cache', function(e) {
            e.preventDefault();
            startProgressAction('clear_critical_cache', $(this));
        });

        // Simple action buttons that should NOT trigger progress modals
        // Note: Card header buttons have been removed - only keeping essential handlers

        // Header action buttons
        $(document).on('click', '#apply-recommended-tweaks', function(e) {
            e.preventDefault();
            applyRecommendedTweaks($(this));
        });

        $(document).on('click', '#reset-all-tweaks', function(e) {
            e.preventDefault();
            resetAllTweaks($(this));
        });

        // Note: All card header button handlers have been removed as the buttons were removed from the UI

        // Handle simple action buttons that don't need progress modals
        $(document).on('click', '[data-redco-action]', function(e) {
            const $button = $(this);
            const action = $button.data('redco-action');

            // CRITICAL FIX: Handle simple actions with basic loading states (NO PROGRESS MODAL)
            const simpleActions = [
                'test_webp_support', 'refresh_webp_stats', 'refresh_stats',
                'toggle_module', 'save_settings', 'reset_settings',
                'enable_all', 'disable_all', 'test_support', 'check_status',
                'verify_files', 'debug_detection', 'inspect_database'
            ];

            // CRITICAL FIX: Exclude WebP module buttons - they have their own handlers
            const webpActions = [
                'test_webp_support', 'refresh_webp_stats', 'scan_images',
                'analyze_webp_potential', 'bulk_convert_webp'
            ];

            // Let simple actions handle themselves with basic loading states
            if (simpleActions.includes(action)) {
                // Simple actions get basic loading state, not progress modal
                e.preventDefault();
                handleSimpleAction($button, action);
                return;
            }

            // Let WebP module handle their own buttons completely
            if (webpActions.includes(action)) {
                return;
            }

            // Only complex actions get progress modals
            e.preventDefault();
            if (action) {
                startProgressAction(action, $(this));
            }
        });

        // Catch-all for any remaining action buttons
        $(document).on('click', '[id*="clear-"], [id*="run-"], [id*="optimize-"], [id*="preload-"], [id*="clean-"], [id*="minify-"], [id*="compress-"], [id*="critical-"], [id*="generate-"]', function(e) {
            const $button = $(this);
            const buttonId = $button.attr('id');

            // CRITICAL FIX: Skip simple action buttons that don't need progress modals
            const simpleActionButtons = [
                'refresh-stats', 'header-refresh-stats', 'test-webp-support',
                'test-support', 'check-status', 'verify-files', 'debug-detection',
                'test-webp-modal', 'debug-image-detection',
                'test-image-detection', 'verify-webp-files',
                'reset-specific-images', 'test-webp-creation', 'check-image-status',
                'test-enhanced-webp', 'inspect-database'
            ];

            // CRITICAL FIX: Skip WebP module buttons - they have their own handlers
            const webpButtons = [
                'test-webp-support', 'header-refresh-stats', 'scan-images',
                'analyze-webp-potential', 'bulk-convert-images', 'refresh-stats',
                'test-webp-modal', 'debug-image-detection',
                'test-image-detection', 'verify-webp-files', 'reset-webp-conversions',
                'reset-specific-images', 'test-webp-creation', 'check-image-status',
                'test-enhanced-webp'
            ];

            // Skip simple actions and WebP module buttons
            if (simpleActionButtons.includes(buttonId) || webpButtons.includes(buttonId)) {
                return;
            }

            // Skip if already handled by specific handlers above
            const handledButtons = [
                'clear-page-cache', 'clear-minified-cache', 'clear-all-cache',
                'run-cleanup-now', 'run-database-cleanup', 'preload-cache',
                'optimize-database', 'clean-database', 'cleanup-database',
                'database-cleanup', 'preload-cache-now', 'cache-preload',
                'start-preload', 'optimize-images', 'image-optimization',
                'minify-assets', 'minify-css-js', 'optimize-critical-resources',
                'generate-critical-css', 'clear-critical-cache'
            ];

            if (handledButtons.includes(buttonId)) {
                return;
            }

            // Skip if it's not an action button (e.g., form elements)
            if ($button.is('input[type="submit"]') || $button.closest('form').length) {
                return;
            }

            // Try to determine action from button ID or data attributes
            let action = $button.data('action') || $button.data('redco-action');

            if (!action) {
                // Map common button ID patterns to actions
                if (buttonId.includes('clear-cache') || buttonId.includes('clear-all')) {
                    action = 'clear_all_cache';
                } else if (buttonId.includes('clear-page')) {
                    action = 'clear_page_cache';
                } else if (buttonId.includes('clear-minified') || buttonId.includes('clear-css') || buttonId.includes('clear-js')) {
                    action = 'clear_minified_cache';
                } else if (buttonId.includes('clear-')) {
                    action = 'clear_cache';
                } else if (buttonId.includes('cleanup') || buttonId.includes('clean-db') || buttonId.includes('optimize-db')) {
                    action = 'database_cleanup';
                } else if (buttonId.includes('preload') || buttonId.includes('warm-cache')) {
                    action = 'preload_cache';
                } else if (buttonId.includes('optimize-images') || buttonId.includes('compress-images')) {
                    action = 'optimize_images';
                } else if (buttonId.includes('minify') || buttonId.includes('compress-assets')) {
                    action = 'minify_assets';
                } else if (buttonId.includes('critical-resources') || buttonId.includes('optimize-critical')) {
                    action = 'optimize_critical_resources';
                } else if (buttonId.includes('generate-critical') || buttonId.includes('critical-css')) {
                    action = 'generate_critical_css';
                } else if (buttonId.includes('optimize')) {
                    action = 'optimize_database';
                }
            }

            if (action) {
                e.preventDefault();
                startProgressAction(action, $(this));
            }
        });
    }

    /**
     * Handle progress action from quick action buttons
     */
    function handleProgressAction(e) {
        e.preventDefault();
        const $button = $(this);
        const action = $button.data('action');

        if (!action) return;

        // Check if module is enabled for module-specific buttons
        const module = $button.data('module');
        if (module && $button.hasClass('disabled')) {
            const moduleName = getModuleName(module);
            showToast(`Please enable the ${moduleName} module first to use this feature.`, 'warning');
            return;
        }

        startProgressAction(action, $button);
    }

    /**
     * Handle simple actions with basic loading states (NO PROGRESS MODAL)
     */
    function handleSimpleAction($button, action) {
        // Show basic loading state
        const originalText = $button.text();
        const originalHtml = $button.html();

        $button.prop('disabled', true)
               .addClass('loading')
               .html('<span class="dashicons dashicons-update spin"></span> ' + originalText);

        // Simple actions complete quickly, so we just show a toast notification
        // The actual functionality is handled by the module's own JavaScript

        // Restore button state after a short delay
        setTimeout(function() {
            $button.prop('disabled', false)
                   .removeClass('loading')
                   .html(originalHtml);
        }, 1000);

        // Show informational toast for simple actions
        const actionMessages = {
            'refresh_stats': 'Refreshing statistics...',
            'refresh_webp_stats': 'Refreshing WebP statistics...',
            'test_webp_support': 'Testing WebP support...',
            'test_support': 'Testing server support...',
            'check_status': 'Checking status...',
            'verify_files': 'Verifying files...',
            'debug_detection': 'Running debug detection...',
            'inspect_database': 'Inspecting database...'
        };

        const message = actionMessages[action] || 'Processing...';
        showToast(message, 'info');
    }

    /**
     * Start a progress action with real-time tracking
     */
    function startProgressAction(action, $button) {
        // Prevent multiple simultaneous actions
        if (currentProgressModal) {
            showToast('Another action is already in progress. Please wait for it to complete.', 'warning');
            return;
        }

        // Store button reference and original state
        currentActionButton = $button;
        originalButtonText = $button.text();

        // Disable button and show loading state
        $button.prop('disabled', true).addClass('loading');

        // Generate unique session ID for this operation
        progressSessionId = 'redco_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

        // Get action configuration
        const actionConfig = getActionConfiguration(action);

        // Create and show progress modal
        currentProgressModal = createProgressModal(actionConfig);
        showProgressModal();

        // Start the actual operation
        initiateServerOperation(action, actionConfig);
    }

    /**
     * Get configuration for specific action
     */
    function getActionConfiguration(action) {
        const configs = {
            'clear_page_cache': {
                title: 'Clearing Page Cache',
                description: 'Removing cached HTML pages for fresh content delivery',
                ajaxAction: 'redco_clear_page_cache',
                estimatedDuration: 5000,
                icon: '🗂️'
            },
            'clear_minified_cache': {
                title: 'Clearing Minified Cache',
                description: 'Removing minified CSS and JavaScript files',
                ajaxAction: 'redco_clear_minified_cache',
                estimatedDuration: 3000,
                icon: '⚡'
            },
            'clear_all_cache': {
                title: 'Clearing All Cache',
                description: 'Removing all cached files and data',
                ajaxAction: 'redco_clear_cache',
                estimatedDuration: 8000,
                icon: '🧹'
            },
            'database_cleanup': {
                title: 'Database Optimization',
                description: 'Cleaning and optimizing database for better performance',
                ajaxAction: 'redco_database_cleanup',
                estimatedDuration: 15000,
                icon: '🗄️'
            },
            'preload_cache': {
                title: 'Cache Preloading',
                description: 'Generating cache for faster page loading',
                ajaxAction: 'redco_preload_cache',
                estimatedDuration: 20000,
                icon: '🚀'
            },
            'optimize_database': {
                title: 'Database Optimization',
                description: 'Optimizing database tables and cleaning up data',
                ajaxAction: 'redco_database_cleanup',
                estimatedDuration: 15000,
                icon: '🗄️'
            },
            'clear_cache': {
                title: 'Clearing All Cache',
                description: 'Removing all cached files and data',
                ajaxAction: 'redco_clear_cache',
                estimatedDuration: 8000,
                icon: '🧹'
            },
            'run_cleanup': {
                title: 'Database Cleanup',
                description: 'Cleaning and optimizing database for better performance',
                ajaxAction: 'redco_database_cleanup',
                estimatedDuration: 15000,
                icon: '🗄️'
            },
            'run_database_cleanup': {
                title: 'Database Cleanup',
                description: 'Cleaning and optimizing database for better performance',
                ajaxAction: 'redco_database_cleanup',
                estimatedDuration: 15000,
                icon: '🗄️'
            },
            'database_cleanup': {
                title: 'Database Cleanup',
                description: 'Cleaning and optimizing database for better performance',
                ajaxAction: 'redco_database_cleanup',
                estimatedDuration: 15000,
                icon: '🗄️'
            },
            'preload_cache': {
                title: 'Cache Preloading',
                description: 'Preloading cache for faster page loading',
                ajaxAction: 'redco_preload_cache',
                estimatedDuration: 25000,
                icon: '🚀'
            },
            'optimize_images': {
                title: 'Image Optimization',
                description: 'Optimizing images for better performance',
                ajaxAction: 'redco_optimize_images',
                estimatedDuration: 30000,
                icon: '🖼️'
            },
            'minify_assets': {
                title: 'Asset Minification',
                description: 'Minifying CSS and JavaScript files',
                ajaxAction: 'redco_minify_assets',
                estimatedDuration: 12000,
                icon: '⚡'
            },
            'optimize_critical_resources': {
                title: 'Critical Resource Optimization',
                description: 'Optimizing critical above-the-fold resources for maximum performance',
                ajaxAction: 'redco_optimize_critical_resources',
                estimatedDuration: 20000,
                icon: '🚀'
            },
            'clear_critical_cache': {
                title: 'Clearing Critical Cache',
                description: 'Removing critical CSS files and optimization data',
                ajaxAction: 'redco_clear_critical_cache',
                estimatedDuration: 5000,
                icon: '🗑️'
            },
            'generate_critical_css': {
                title: 'Generating Critical CSS',
                description: 'Creating critical CSS for homepage optimization',
                ajaxAction: 'redco_generate_critical_css',
                estimatedDuration: 8000,
                icon: '⚡'
            },
            'performance_audit': {
                title: 'Performance Audit',
                description: 'Analyzing current performance and identifying optimization opportunities',
                ajaxAction: 'redco_performance_audit',
                estimatedDuration: 15000,
                icon: '🔍'
            },
            'apply_optimal_config': {
                title: 'Applying Optimal Configuration',
                description: 'Configuring all modules for maximum PageSpeed performance',
                ajaxAction: 'redco_apply_optimal_config',
                estimatedDuration: 10000,
                icon: '🚀'
            }
        };

        return configs[action] || {
            title: 'Processing Action',
            description: 'Performing the requested operation',
            ajaxAction: 'redco_generic_action',
            estimatedDuration: 5000,
            icon: '⚙️'
        };
    }

    /**
     * Create progress modal HTML structure
     */
    function createProgressModal(config) {
        const modalHtml = `
            <div class="redco-progress-overlay" id="redco-progress-overlay">
                <div class="redco-progress-modal">
                    <div class="progress-modal-header">
                        <div class="progress-icon">${config.icon}</div>
                        <div class="progress-title-section">
                            <h3 class="progress-title">${config.title}</h3>
                            <p class="progress-description">${config.description}</p>
                        </div>
                        <button class="progress-close-btn" id="progress-close-btn" style="display: none;" title="Close">
                            <span class="dashicons dashicons-no-alt"></span>
                        </button>
                    </div>

                    <div class="progress-content">
                        <div class="progress-bar-section">
                            <div class="progress-bar-container">
                                <div class="progress-bar" id="progress-bar" style="width: 0%"></div>
                            </div>
                            <div class="progress-percentage" id="progress-percentage">0%</div>
                        </div>

                        <div class="progress-status-section">
                            <div class="current-operation" id="current-operation">Initializing...</div>
                            <div class="operation-details" id="operation-details">Preparing to start operation</div>
                        </div>

                        <div class="progress-stats" id="progress-stats" style="display: none;">
                            <div class="stat-item">
                                <span class="stat-label">Files Processed</span>
                                <span class="stat-value" id="files-processed">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Items Cleaned</span>
                                <span class="stat-value" id="items-cleaned">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Space Saved</span>
                                <span class="stat-value" id="space-saved">0 KB</span>
                            </div>
                        </div>
                    </div>

                    <div class="progress-actions" id="progress-actions" style="display: none;">
                        <button type="button" class="button button-primary" id="progress-done-btn">Done</button>
                    </div>

                    <div class="progress-error" id="progress-error" style="display: none;">
                        <div class="error-icon">⚠️</div>
                        <div class="error-message" id="error-message"></div>
                        <div class="error-actions">
                            <button type="button" class="button button-secondary" id="error-retry-btn">Retry</button>
                            <button type="button" class="button" id="error-close-btn">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        return $(modalHtml);
    }

    /**
     * Show progress modal
     */
    function showProgressModal() {
        if (!currentProgressModal) return;

        // Add modal to container
        $('#redco-progress-modal-container').html(currentProgressModal);

        // Show modal instantly - no delays
        $('#redco-progress-overlay').addClass('show');

        // Attach event handlers
        attachModalEventHandlers();
    }

    /**
     * Attach event handlers to modal elements
     */
    function attachModalEventHandlers() {
        // Close button handlers
        $('#progress-close-btn, #progress-done-btn, #error-close-btn').off('click').on('click', hideProgressModal);

        // Retry button handler
        $('#error-retry-btn').off('click').on('click', function() {
            // Hide error section and restart operation
            $('#progress-error').hide();
            $('#progress-content').show();

            // Reset progress display
            $('#progress-bar').css('width', '0%');
            $('#progress-percentage').text('0%');
            $('#current-operation').text('Retrying...');
            $('#operation-details').text('Restarting the operation');
            $('#progress-stats').hide();
            $('#progress-actions').hide();
            $('#progress-close-btn').hide();

        });

        // Prevent closing during active operation
        $('#redco-progress-overlay').off('click').on('click', function(e) {
            if (e.target === this && $('#progress-close-btn').is(':visible')) {
                hideProgressModal();
            }
        });

        // Keyboard handling
        $(document).off('keydown.progress-modal').on('keydown.progress-modal', function(e) {
            if (e.key === 'Escape' && $('#progress-close-btn').is(':visible')) {
                hideProgressModal();
            }
        });
    }

    /**
     * Hide progress modal
     */
    function hideProgressModal() {
        // Stop progress updates
        if (progressUpdateInterval) {
            clearInterval(progressUpdateInterval);
            progressUpdateInterval = null;
        }

        // Remove keyboard handler
        $(document).off('keydown.progress-modal');

        // Restore button state
        restoreButtonState();

        // Hide modal with animation
        $('#redco-progress-overlay').removeClass('show');

        setTimeout(() => {
            $('#redco-progress-modal-container').empty();
            currentProgressModal = null;
            progressSessionId = null;
        }, 300);
    }

    /**
     * Restore action button to normal state
     */
    function restoreButtonState() {
        if (currentActionButton && originalButtonText) {
            currentActionButton
                .prop('disabled', false)
                .removeClass('loading')
                .text(originalButtonText);

        }

        // Clear stored references
        currentActionButton = null;
        originalButtonText = null;
    }

    /**
     * Initiate server operation with real-time tracking
     */
    function initiateServerOperation(action, config) {
        // Prepare AJAX data
        let ajaxData = {
            action: config.ajaxAction,
            nonce: redcoAjax.nonce,
            session_id: progressSessionId,
            track_progress: true
        };

        // Add action-specific data
        if (action === 'database_cleanup') {
            ajaxData.options = getDatabaseCleanupOptions();
        }

        // Start the operation
        $.ajax({
            url: redcoAjax.ajaxurl,
            type: 'POST',
            data: ajaxData,
            timeout: 60000, // 60 second timeout
            success: function(response) {

                if (response.success) {
                    // Update session ID from server response
                    if (response.data && response.data.session_id) {
                        progressSessionId = response.data.session_id;
                    }

                    // Start progress tracking
                    startProgressTracking(config);

                    // Handle immediate completion or start polling
                    if (response.data.completed) {
                        handleOperationComplete(response.data);
                    }
                } else {
                    showProgressError(response.data.message || 'Operation failed');
                }
            },
            error: function(xhr, status, error) {
                let errorMessage = 'Network error occurred';

                if (xhr.responseJSON && xhr.responseJSON.data && xhr.responseJSON.data.message) {
                    errorMessage = xhr.responseJSON.data.message;
                } else if (status === 'timeout') {
                    errorMessage = 'Operation timed out. Please try again.';
                }

                showProgressError(errorMessage);
            }
        });
    }

    /**
     * Start real-time progress tracking with enhanced error handling
     */
    function startProgressTracking(config) {
        let pollCount = 0;
        const maxPolls = Math.ceil(config.estimatedDuration / 1000); // Poll every second
        const maxRetries = 3;
        let consecutiveErrors = 0;

        progressUpdateInterval = setInterval(() => {
            pollCount++;

            // Safety timeout check first
            if (pollCount >= maxPolls * 2) {
                clearInterval(progressUpdateInterval);
                progressUpdateInterval = null;
                showProgressError('Operation timed out');
                return;
            }

            // Get progress update from server
            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_get_progress',
                    nonce: redcoAjax.nonce,
                    session_id: progressSessionId
                },
                timeout: 10000, // 10 second timeout
                success: function(response) {
                    consecutiveErrors = 0; // Reset error count on success

                    if (response.success && response.data) {
                        updateProgressDisplay(response.data);

                        // Check if operation is complete
                        if (response.data.completed) {
                            clearInterval(progressUpdateInterval);
                            progressUpdateInterval = null;
                            handleOperationComplete(response.data);
                        }
                    } else {
                        consecutiveErrors++;

                        if (consecutiveErrors >= maxRetries) {
                            clearInterval(progressUpdateInterval);
                            progressUpdateInterval = null;
                            showProgressError('Progress tracking failed - invalid response');
                        }
                    }
                },
                error: function(xhr, status, error) {
                    consecutiveErrors++;

                    // Stop polling after max retries or max polls
                    if (consecutiveErrors >= maxRetries || pollCount >= maxPolls) {
                        clearInterval(progressUpdateInterval);
                        progressUpdateInterval = null;

                        let errorMessage = 'Progress tracking failed';
                        if (status === 'timeout') {
                            errorMessage = 'Progress tracking timed out';
                        } else if (pollCount >= maxPolls) {
                            errorMessage = 'Operation timed out';
                        }

                        showProgressError(errorMessage);
                    }
                }
            });

        }, 1000); // Poll every second
    }

    /**
     * Update progress display with real data
     */
    function updateProgressDisplay(data) {
        // Update progress bar
        if (data.percentage !== undefined) {
            $('#progress-bar').css('width', data.percentage + '%');
            $('#progress-percentage').text(Math.round(data.percentage) + '%');
        }

        // Update current operation
        if (data.current_operation) {
            $('#current-operation').text(data.current_operation);
        }

        // Update operation details
        if (data.operation_details) {
            $('#operation-details').text(data.operation_details);
        }

        // Update statistics
        if (data.stats) {
            $('#progress-stats').show();

            if (data.stats.files_processed !== undefined) {
                $('#files-processed').text(data.stats.files_processed.toLocaleString());
            }

            if (data.stats.items_cleaned !== undefined) {
                $('#items-cleaned').text(data.stats.items_cleaned.toLocaleString());
            }

            if (data.stats.space_saved !== undefined) {
                $('#space-saved').text(formatBytes(data.stats.space_saved));
            }
        }
    }

    /**
     * Handle operation completion
     */
    function handleOperationComplete(data) {
        // Update final progress
        $('#progress-bar').css('width', '100%');
        $('#progress-percentage').text('100%');
        $('#current-operation').text('Completed Successfully');
        $('#operation-details').text(data.message || 'Operation completed successfully');

        // Show final statistics
        if (data.stats) {
            updateProgressDisplay(data);
        }

        // Show completion actions
        $('#progress-actions').show();
        $('#progress-close-btn').show();

        // Update dashboard metrics if on dashboard
        setTimeout(() => {
            if (window.location.href.includes('page=redco-optimizer') && !window.location.href.includes('&tab=')) {
                updatePerformanceMetrics();
                updateHealthMetrics();
            }
        }, 1000);

        // Show success toast
        setTimeout(() => {
            showToast(data.message || 'Operation completed successfully', 'success');
        }, 500);
    }

    /**
     * Show progress error with enhanced handling
     */
    function showProgressError(message) {

        // Clear any running intervals immediately
        if (progressUpdateInterval) {
            clearInterval(progressUpdateInterval);
            progressUpdateInterval = null;
        }

        // Hide progress content
        $('#progress-content').hide();

        // Show error section
        $('#progress-error').show();
        $('#error-message').text(message);
        $('#progress-close-btn').show();

        // Add retry button functionality
        $('#progress-retry-btn').off('click').on('click', function() {

            // Hide error and show progress again
            $('#progress-error').hide();
            $('#progress-content').show();

            // Restart the operation if we have the current action
            if (currentActionButton && progressSessionId) {
                const action = currentActionButton.data('action') ||
                             currentActionButton.attr('id')?.replace(/^(clear-|run-|optimize-|preload-)/, '') ||
                             'clear_page_cache';

                startProgressAction(action, currentActionButton);
            } else {
                showProgressError('Cannot retry - no active operation found');
            }
        });

        // Show error toast
        showToast(message, 'error');

        // Auto-close after 30 seconds if user doesn't interact
        setTimeout(() => {
            if ($('#redco-progress-modal-container').is(':visible') && $('#progress-error').is(':visible')) {
                hideProgressModal();
            }
        }, 30000);
    }

    /**
     * Format bytes to human readable format
     */
    function formatBytes(bytes) {
        if (bytes === 0) return '0 B';

        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    /**
     * Get database cleanup options from current settings
     */
    function getDatabaseCleanupOptions() {
        const options = {};

        // Get options from form if available
        if ($('#cleanup-revisions').length) {
            options.cleanup_revisions = $('#cleanup-revisions').is(':checked');
        }
        if ($('#cleanup-spam-comments').length) {
            options.cleanup_spam_comments = $('#cleanup-spam-comments').is(':checked');
        }
        if ($('#cleanup-trashed-posts').length) {
            options.cleanup_trashed_posts = $('#cleanup-trashed-posts').is(':checked');
        }
        if ($('#cleanup-expired-transients').length) {
            options.cleanup_expired_transients = $('#cleanup-expired-transients').is(':checked');
        }

        return options;
    }

    // Make progress modal functions globally accessible for debugging
    window.redcoProgressModal = {
        test: function(action = 'clear_page_cache') {
            const $testButton = $('<button>Test Button</button>');
            startProgressAction(action, $testButton);
        },

        testError: function() {
            const config = getActionConfiguration('clear_page_cache');
            currentProgressModal = createProgressModal(config);
            showProgressModal();

            setTimeout(() => {
                showProgressError('This is a test error message');
            }, 2000);
        },

        getCurrentSession: function() {
            return {
                sessionId: progressSessionId,
                modalActive: !!currentProgressModal,
                intervalActive: !!progressUpdateInterval
            };
        },

        forceClose: function() {

            // Clear all intervals
            if (progressUpdateInterval) {
                clearInterval(progressUpdateInterval);
                progressUpdateInterval = null;
            }

            // Reset session
            progressSessionId = null;
            currentProgressModal = null;

            // Hide modal
            hideProgressModal();

            // Restore button state
            restoreButtonState();

            // Remove any stuck overlays
            $('.redco-progress-overlay').remove();
            $('#redco-progress-modal-container').remove();

        },

        close: function() {
            hideProgressModal();
        },

        retry: function() {
            $('#progress-retry-btn').trigger('click');
        },

        checkButtonState: function() {
             return {
                hasButton: !!currentActionButton,
                originalText: originalButtonText,
                modalActive: !!currentProgressModal
            };
        },

        restoreButton: function() {
            restoreButtonState();
        }
    };
    
    /**
     * Get database cleanup options from current settings
     */
    function getDatabaseCleanupOptions() {
        // Try to get options from the database cleanup form if it exists
        const $dbForm = $('.redco-module-form[data-module="database-cleanup"]');

        if ($dbForm.length) {
            // Form exists, get current form values
            const formData = $dbForm.serializeArray();
            const options = {};

            $.each(formData, function(i, field) {
                if (field.name.startsWith('settings[')) {
                    const key = field.name.replace('settings[', '').replace(']', '');
                    if (options[key]) {
                        if (!Array.isArray(options[key])) {
                            options[key] = [options[key]];
                        }
                        options[key].push(field.value);
                    } else {
                        options[key] = field.value;
                    }
                }
            });

            return options;
        } else {
            // Form not available, use default safe options
            return {
                cleanup_revisions: '1',
                keep_revisions: '5',
                cleanup_auto_drafts: '1',
                cleanup_trashed_posts: '1',
                cleanup_spam_comments: '1',
                cleanup_trashed_comments: '1',
                cleanup_expired_transients: '1',
                cleanup_orphaned_postmeta: '1',
                cleanup_orphaned_commentmeta: '1'
            };
        }
    }

    /**
     * Get module display name
     */
    function getModuleName(moduleKey) {
        const moduleNames = {
            'page-cache': 'Page Cache',
            'database-cleanup': 'Database Cleanup',
            'lazy-load': 'Lazy Load',
            'css-js-minifier': 'CSS/JS Minifier',
            'heartbeat-control': 'Heartbeat Control',
            'wordpress-core-tweaks': 'WordPress Core Tweaks',
            'critical-resource-optimizer': 'Critical Resource Optimizer'
        };

        return moduleNames[moduleKey] || moduleKey;
    }

    /**
     * Update quick action button states based on module status
     */
    function updateQuickActionButtonStates(moduleKey, enabled) {
        $('.header-quick-actions .quick-action-btn').each(function() {
            const $button = $(this);
            const buttonModule = $button.data('module');

            if (buttonModule === moduleKey) {
                if (enabled) {
                    // Enable the button
                    $button.removeClass('disabled').prop('disabled', false);
                    $button.css('pointer-events', 'auto');

                    // Update tooltip
                    const action = $button.data('action');
                    let newTooltip = '';
                    switch (action) {
                        case 'clear_cache':
                            newTooltip = 'Clear all cache files';
                            break;
                        case 'optimize_database':
                            newTooltip = 'Clean and optimize database';
                            break;
                        case 'preload_cache':
                            newTooltip = 'Preload cache for faster loading';
                            break;
                    }
                    $button.attr('data-tooltip', newTooltip);
                } else {
                    // Disable the button
                    $button.addClass('disabled').prop('disabled', true);
                    $button.css('pointer-events', 'none');

                    // Update tooltip
                    const moduleName = getModuleName(moduleKey);
                    $button.attr('data-tooltip', `Enable ${moduleName} module to use this feature`);
                }
            }
        });
    }

    /**
     * Update all quick action button states
     */
    function updateAllQuickActionStates() {
        $('.header-quick-actions .quick-action-btn').each(function() {
            const $button = $(this);
            const module = $button.data('module');

            // Check if module is enabled (this would need to be passed from PHP or fetched via AJAX)
            // For now, we'll rely on the initial PHP rendering
        });
    }

    /**
     * Initialize form handlers
     */
    function initFormHandlers() {
        // Module settings forms - Use event delegation for dynamic forms
        $(document).on('submit', '.redco-module-form', function(e) {
            e.preventDefault();

            const $form = $(this);
            const module = $form.data('module');

            if (!module) {
                showToast('Error: No module specified', 'error');
                return;
            }

            const formData = $form.serializeArray();

            // Convert form data to object
            const settings = {};
            $.each(formData, function(i, field) {
                if (settings[field.name]) {
                    // Handle multiple values (checkboxes, multi-select)
                    if (!Array.isArray(settings[field.name])) {
                        settings[field.name] = [settings[field.name]];
                    }
                    settings[field.name].push(field.value);
                } else {
                    settings[field.name] = field.value;
                }
            });

            // Show saving state
            const $submitBtn = $form.find('input[type="submit"], button[type="submit"]');
            if (!$submitBtn.length) {
                showToast('Error: No submit button found', 'error');
                return;
            }

            const originalText = $submitBtn.val() || $submitBtn.text();
            const loadingText = redcoAjax.strings.saving || 'Saving...';

            $submitBtn.prop('disabled', true);

            // Handle both input and button elements
            if ($submitBtn.is('input[type="submit"]')) {
                $submitBtn.val(loadingText);
            } else {
                $submitBtn.text(loadingText);
            }

            // Add loading class to form
            $form.addClass('redco-loading');

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_save_module_settings',
                    module: module,
                    settings: settings,
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        showToast(response.data.message || redcoAjax.strings.saved || 'Settings saved successfully!', 'success');

                        // Briefly show success state
                        const successText = redcoAjax.strings.saved || 'Saved!';
                        if ($submitBtn.is('input[type="submit"]')) {
                            $submitBtn.val(successText);
                        } else {
                            $submitBtn.text(successText);
                        }

                        // Reset button after 2 seconds
                        setTimeout(function() {
                            if ($submitBtn.is('input[type="submit"]')) {
                                $submitBtn.val(originalText);
                            } else {
                                $submitBtn.text(originalText);
                            }
                        }, 2000);
                    } else {
                        showToast(response.data.message || redcoAjax.strings.error || 'Error saving settings', 'error');
                    }
                },
                error: function(xhr, status, error) {

                    showToast(redcoAjax.strings.error || 'Error saving settings', 'error');
                },
                complete: function() {
                    $form.removeClass('redco-loading');
                    $submitBtn.prop('disabled', false);

                    // Only reset text if it's not showing success state
                    setTimeout(function() {
                        const currentText = $submitBtn.is('input[type="submit"]') ? $submitBtn.val() : $submitBtn.text();
                        const successText = redcoAjax.strings.saved || 'Saved!';

                        if (currentText !== successText) {
                            if ($submitBtn.is('input[type="submit"]')) {
                                $submitBtn.val(originalText);
                            } else {
                                $submitBtn.text(originalText);
                            }
                        }
                    }, 100);
                }
            });
        });
    }

    /**
     * Initialize license handlers
     */
    function initLicenseHandlers() {
        // Activate license
        $('#activate-license').on('click', function() {
            const $button = $(this);
            const licenseKey = $('#license_key').val().trim();

            if (!licenseKey) {
                showNotice('Please enter a license key', 'error');
                return;
            }

            $button.prop('disabled', true).text('Activating...');

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_activate_license',
                    license_key: licenseKey,
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        showNotice(response.data.message, 'success');
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        showNotice(response.data.message, 'error');
                    }
                },
                error: function() {
                    showNotice('Error activating license', 'error');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Activate License');
                }
            });
        });

        // Deactivate license
        $('#deactivate-license').on('click', function() {
            const $button = $(this);

            // No confirmation needed - provide immediate feedback through progress
            $button.prop('disabled', true).text('Deactivating...');

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_deactivate_license',
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        showNotice(response.data.message, 'success');
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        showNotice(response.data.message, 'error');
                    }
                },
                error: function() {
                    showNotice('Error deactivating license', 'error');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Deactivate License');
                }
            });
        });
    }

    /**
     * Initialize addon handlers
     */
    function initAddonHandlers() {
        // Install addon
        $(document).on('click', '.install-addon', function() {
            const $button = $(this);
            const addonSlug = $button.data('addon');

            $button.prop('disabled', true).text('Installing...');

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_install_addon',
                    addon_slug: addonSlug,
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        showNotice(response.data.message, 'success');
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        showNotice(response.data.message, 'error');
                    }
                },
                error: function() {
                    showNotice('Error installing addon', 'error');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Install');
                }
            });
        });

        // Activate addon
        $(document).on('click', '.activate-addon', function() {
            const $button = $(this);
            const addonSlug = $button.data('addon');

            $button.prop('disabled', true).text('Activating...');

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_activate_addon',
                    addon_slug: addonSlug,
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        showNotice(response.data.message, 'success');
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        showNotice(response.data.message, 'error');
                    }
                },
                error: function() {
                    showNotice('Error activating addon', 'error');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Activate');
                }
            });
        });

        // Deactivate addon
        $(document).on('click', '.deactivate-addon', function() {
            const $button = $(this);
            const addonSlug = $button.data('addon');

            $button.prop('disabled', true).text('Deactivating...');

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_deactivate_addon',
                    addon_slug: addonSlug,
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        showNotice(response.data.message, 'success');
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        showNotice(response.data.message, 'error');
                    }
                },
                error: function() {
                    showNotice('Error deactivating addon', 'error');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Deactivate');
                }
            });
        });

        // Update addon
        $(document).on('click', '.update-addon', function() {
            const $button = $(this);
            const addonSlug = $button.data('addon');

            $button.prop('disabled', true).text('Updating...');

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_update_addon',
                    addon_slug: addonSlug,
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        showNotice(response.data.message, 'success');
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        showNotice(response.data.message, 'error');
                    }
                },
                error: function() {
                    showNotice('Error updating addon', 'error');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Update');
                }
            });
        });

        // Refresh addons
        $('#refresh-addons').on('click', function() {
            const $button = $(this);

            $button.prop('disabled', true).text('Refreshing...');

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_refresh_addons',
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        showNotice(response.data.message, 'success');
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        showNotice(response.data.message, 'error');
                    }
                },
                error: function() {
                    showNotice('Error refreshing addons', 'error');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Refresh List');
                }
            });
        });
    }

    /**
     * Simple toast notification system
     */
    function showToast(message, type = 'info', duration = 3000) {
        const toastId = 'toast-' + Date.now();
        const iconMap = {
            success: '✓',
            error: '✕',
            warning: '⚠',
            info: 'ℹ'
        };

        const $toast = $(`
            <div id="${toastId}" class="redco-toast redco-toast-${type}">
                <div class="redco-toast-icon">${iconMap[type] || iconMap.info}</div>
                <div class="redco-toast-message">${message}</div>
                <button class="redco-toast-close">&times;</button>
            </div>
        `);

        $('#redco-toast-container').append($toast);

        // Show toast
        setTimeout(() => $toast.addClass('show'), 10);

        // Auto-hide
        const hideTimeout = setTimeout(() => hideToast(toastId), duration);

        // Manual close
        $toast.find('.redco-toast-close').on('click', () => {
            clearTimeout(hideTimeout);
            hideToast(toastId);
        });
    }

    /**
     * Hide toast notification
     */
    function hideToast(toastId) {
        const $toast = $('#' + toastId);
        $toast.removeClass('show');
        setTimeout(() => $toast.remove(), 300);
    }

    /**
     * Initialize settings change listener for performance interval updates
     */
    function initSettingsChangeListener() {
        // Listen for performance settings form submissions
        $(document).on('submit', 'form[action*="options.php"]', function(e) {
            const $form = $(this);
            const $intervalSelect = $form.find('select[name="redco_optimizer_performance[update_interval]"]');

            if ($intervalSelect.length) {
                const newInterval = parseInt($intervalSelect.val());

                // Update the interval after a short delay to allow form processing
                setTimeout(function() {
                    updatePerformanceInterval(newInterval);
                }, 2000);
            }
        });

        // Also listen for direct interval changes
        $(document).on('change', 'select[name="redco_optimizer_performance[update_interval]"]', function() {
            const newInterval = parseInt($(this).val());

            // Show preview of new interval
            showToast(`Auto-update interval will change to ${newInterval} seconds after saving settings`, 'info');
        });
    }



    /**
     * PERFORMANCE OPTIMIZED: Initialize performance monitoring UI only (no API calls)
     */
    function initPerformanceMonitoringUI() {
        // Only initialize on main dashboard page
        const isMainDashboard = window.location.href.includes('page=redco-optimizer') && !window.location.href.includes('&tab=');
        const hasPerformanceElements = $('.redco-performance-overview, .redco-performance-health-dashboard, .performance-metric, .health-metric').length > 0;

        // Only initialize UI on main dashboard with performance elements
        if (!isMainDashboard || !hasPerformanceElements) {
            return;
        }

        // Check if monitoring is enabled
        if (redcoAjax.settings && !redcoAjax.settings.monitoringEnabled) {
            return;
        }

        // PERFORMANCE: Check cache first before making API calls
        const cacheKey = 'performance_metrics';
        const cachedData = performanceCache.get(cacheKey);

        if (cachedData && (Date.now() - cachedData.timestamp) < config.cacheTimeout) {
            // Use cached data and skip API calls
            updatePerformanceMetricsFromCache(cachedData.data);
            return;
        }

        // Add real-time indicator
        addPerformanceIndicator();

        // Handle page visibility changes
        $(document).on('visibilitychange', function() {
            if (document.hidden) {
                stopPerformanceUpdates();
            } else {
                // Only restart if we're still on the main dashboard
                const stillOnDashboard = window.location.href.includes('page=redco-optimizer') && !window.location.href.includes('&tab=');
                if (stillOnDashboard) {
                    startPerformanceUpdates();
                }
            }
        });

        // Stop updates when navigating away from dashboard
        $(window).on('beforeunload', function() {
            stopPerformanceUpdates();
        });

        // Manual refresh button
        $('.performance-refresh-btn').on('click', function() {
            updateAllDashboardSections(true, false); // manual=true, isInitial=false
        });
    }

    /**
     * Initialize performance monitoring with API calls (called after page load)
     */
    function initPerformanceMonitoring() {
        // Only initialize on main dashboard page
        const isMainDashboard = window.location.href.includes('page=redco-optimizer') && !window.location.href.includes('&tab=');
        const hasPerformanceElements = $('.redco-performance-overview, .redco-performance-health-dashboard, .performance-metric, .health-metric').length > 0;

        // Only start auto-updates on main dashboard with performance elements
        if (!isMainDashboard || !hasPerformanceElements) {
            return;
        }

        // Check if monitoring is enabled
        if (redcoAjax.settings && !redcoAjax.settings.monitoringEnabled) {
            return;
        }

        // Start monitoring
        startPerformanceUpdates();
    }

    /**
     * Add performance indicator
     */
    function addPerformanceIndicator() {
        const indicator = `
            <div class="performance-indicator">
                <span class="indicator-dot"></span>
                <span class="indicator-text">Live</span>
                <button class="performance-refresh-btn" title="Refresh Now">
                    <span class="dashicons dashicons-update"></span>
                </button>
            </div>
        `;

        $('.redco-performance-overview h2').append(indicator);
    }

    /**
     * Start performance updates
     */
    function startPerformanceUpdates() {
        if (performanceUpdateActive) {
            return;
        }

        performanceUpdateActive = true;

        // Initial update of all sections (without showing auto-update indicator)
        updateAllDashboardSections(false, true); // manual=false, isInitial=true

        // Set up recurring updates
        performanceUpdateTimer = setInterval(function() {
            updateAllDashboardSections(false, false); // manual=false, isInitial=false
        }, config.performanceUpdateInterval);
    }

    /**
     * Stop performance updates
     */
    function stopPerformanceUpdates() {
        performanceUpdateActive = false;

        if (performanceUpdateTimer) {
            clearInterval(performanceUpdateTimer);
            performanceUpdateTimer = null;
        }

        // Clear any other potential timers
        clearAllRedcoTimers();
    }

    /**
     * Clear all Redco timers to prevent multiple running timers
     */
    function clearAllRedcoTimers() {
        // Clear any timers that might be stored in window object
        if (window.redcoTimers) {
            window.redcoTimers.forEach(function(timerId) {
                clearInterval(timerId);
                clearTimeout(timerId);
            });
            window.redcoTimers = [];
        }

    }

    /**
     * Update performance monitoring interval from settings
     */
    function updatePerformanceInterval(newIntervalSeconds) {
        const newIntervalMs = newIntervalSeconds * 1000;

        // Update config
        config.performanceUpdateInterval = newIntervalMs;

        // Restart monitoring with new interval if currently active
        if (performanceUpdateActive) {
            stopPerformanceUpdates();
            setTimeout(function() {
                startPerformanceUpdates();
            }, 1000);
        }
    }

    /**
     * Update all dashboard sections
     */
    function updateAllDashboardSections(manual = false, isInitial = false) {
        if (!performanceUpdateActive && !manual) {
            return;
        }

        const updateType = manual ? 'manual' : (isInitial ? 'initial' : 'scheduled');

        // Only show auto-update indicator for scheduled updates (not initial or manual)
        const showAutoUpdateIndicator = !manual && !isInitial;

        // Show loading state for all sections
        showAllSectionsLoading(true, showAutoUpdateIndicator);

        // Update all sections in parallel
        Promise.all([
            updatePerformanceMetricsSection(),
            updateWebsitePerformanceScores(),
            updateCoreWebVitalsChart(),
            updateHealthMetrics()
        ]).then(function() {
            updateLastUpdated();
        }).catch(function(error) {
            // Retry after delay
            setTimeout(function() {
                if (performanceUpdateActive) {
                    updateAllDashboardSections(false, false); // Retry as scheduled update
                }
            }, config.performanceRetryDelay);
        }).finally(function() {
            // Hide loading state after all updates complete
            showAllSectionsLoading(false, showAutoUpdateIndicator);
        });
    }

    /**
     * Update performance metrics section only
     */
    function updatePerformanceMetrics(manual = false) {
        if (!performanceUpdateActive && !manual) {
            return;
        }

        // Show loading state
        $('.performance-indicator .indicator-dot').addClass('loading');

        return updatePerformanceMetricsSection().then(function() {
            updateLastUpdated();
        }).catch(function(error) {
            // Retry after delay
            setTimeout(function() {
                if (performanceUpdateActive) {
                    updatePerformanceMetrics();
                }
            }, config.performanceRetryDelay);
        }).finally(function() {
            $('.performance-indicator .indicator-dot').removeClass('loading');
        });
    }

    /**
     * Update performance metrics section via AJAX
     */
    function updatePerformanceMetricsSection() {
        return new Promise(function(resolve, reject) {
            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_get_performance_metrics',
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        updatePerformanceDisplay(response.data);
                        resolve(response.data);
                    } else {
                        reject(response.data);
                    }
                },
                error: function(xhr, status, error) {
                    reject(error);
                }
            });
        });
    }

    /**
     * Show/hide loading states for all dashboard sections
     */
    function showAllSectionsLoading(isLoading, showAutoIndicator = false) {
        const updateType = showAutoIndicator ? 'scheduled auto-update' : 'manual/initial update';

        // Performance Metrics section - keep existing dot animation
        $('.performance-indicator .indicator-dot').toggleClass('loading', isLoading);

        if (isLoading) {

            // Website Performance Scores - trigger spin animation only when data is actually updating
            $('.score-item .score-circle .score-number').each(function() {
                const $element = $(this);
                // Only animate if not already animating
                if (!$element.hasClass('animating')) {
                    triggerScoreAnimation($element);
                }
            });

            // Core Web Vitals Chart - add loading class to chart container
            $('.core-web-vitals-chart, [id*="coreWebVitalsChart"]').addClass('chart-loading');

            // Health Metrics - trigger spin animation for metric values
            $('.health-metric .metric-value, .metric-card .metric-value').each(function() {
                const $element = $(this);
                if (!$element.hasClass('animating')) {
                    triggerScoreAnimation($element);
                }
            });

            // Generic metric cards - trigger spin for any score numbers
            $('.score-number, .metric-number, .stat-number').not('.animating').each(function() {
                triggerScoreAnimation($(this));
            });

            // Only show auto-update indicator for scheduled updates
            if (showAutoIndicator) {
                showAutoUpdateIndicator();
            }

        } else {
            // Remove loading states when update completes
            $('.core-web-vitals-chart, [id*="coreWebVitalsChart"]').removeClass('chart-loading');

            // Only hide auto-update indicator if it was shown
            if (showAutoIndicator) {
                hideAutoUpdateIndicator();
            }
        }

    }

    /**
     * Show auto-update indicator
     */
    function showAutoUpdateIndicator() {
        // Remove existing indicator
        $('.redco-auto-update-indicator').remove();

        const nextUpdate = Math.round(config.performanceUpdateInterval / 1000);
        const $indicator = $(`
            <div class="redco-auto-update-indicator" style="
                position: fixed;
                top: 32px;
                right: 20px;
                background: #4CAF50;
                color: white;
                padding: 8px 12px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
                z-index: 999999;
                box-shadow: 0 2px 8px rgba(0,0,0,0.3);
                animation: redco-pulse 1.5s ease-in-out infinite;
            ">
                🔄 Auto-updating... (${nextUpdate}s interval)
            </div>
        `);

        $('body').append($indicator);
    }

    /**
     * Hide auto-update indicator
     */
    function hideAutoUpdateIndicator() {
        $('.redco-auto-update-indicator').fadeOut(300, function() {
            $(this).remove();
        });
    }

    /**
     * Trigger score card spin animation (same as page load)
     */
    function triggerScoreAnimation($element) {
        if ($element.hasClass('animating')) {
            return; // Already animating
        }

        $element.addClass('animating');

        // Store original value
        const originalValue = $element.text();
        const targetValue = parseInt(originalValue) || 0;

        // Start spinning animation
        let currentValue = 0;
        const increment = Math.max(1, Math.floor(targetValue / 20));
        const duration = 1000; // 1 second
        const steps = 20;
        const stepDuration = duration / steps;

        const animationInterval = setInterval(function() {
            currentValue += increment;
            if (currentValue >= targetValue) {
                currentValue = targetValue;
                $element.text(currentValue);
                clearInterval(animationInterval);
                $element.removeClass('animating');
            } else {
                $element.text(currentValue);
            }
        }, stepDuration);
    }

    /**
     * Animate number to target value (for score updates)
     */
    function animateNumberToTarget($element, targetValue) {
        if ($element.hasClass('animating')) {
            return; // Already animating
        }

        $element.addClass('animating');

        const startValue = parseInt($element.text()) || 0;
        const target = parseInt(targetValue) || 0;

        // If values are the same, no animation needed
        if (startValue === target) {
            $element.removeClass('animating');
            return;
        }

        const duration = 1200; // 1.2 seconds
        const steps = 30;
        const stepDuration = duration / steps;
        const increment = (target - startValue) / steps;

        let currentValue = startValue;
        let step = 0;

        const animationInterval = setInterval(function() {
            step++;
            currentValue += increment;

            if (step >= steps) {
                currentValue = target;
                $element.text(Math.round(currentValue));
                clearInterval(animationInterval);
                $element.removeClass('animating');
            } else {
                $element.text(Math.round(currentValue));
            }
        }, stepDuration);
    }

    /**
     * Update Website Performance Scores section
     */
    function updateWebsitePerformanceScores() {
        return new Promise(function(resolve, reject) {

            // Disable device toggle during auto-refresh
            disableDeviceToggle();

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_get_pagespeed_scores',
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        updatePageSpeedDisplay(response.data);
                        resolve(response.data);
                    } else {
                        reject(response.data);
                    }
                },
                error: function(xhr, status, error) {
                    reject(error);
                },
                complete: function() {
                    // Re-enable device toggle after auto-refresh completes (success or error)
                    enableDeviceToggle();
                }
            });
        });
    }

    /**
     * Update PageSpeed display with spin animations
     */
    function updatePageSpeedDisplay(data) {
        // Update Performance Score - using correct selector for HTML structure
        const $performanceScore = $('.performance-score-item .score-circle');
        if ($performanceScore.length) {
            const $scoreNumber = $performanceScore.find('.score-number');

            // Update score with animation
            animateNumberToTarget($scoreNumber, data.performance_score);

            // Update data attribute for circle animation
            $performanceScore.attr('data-score', data.performance_score);
            $performanceScore.css('--score-percentage', data.performance_score);
        }

        // Update Accessibility Score
        const $accessibilityScore = $('.accessibility-score-item .score-circle');
        if ($accessibilityScore.length) {
            const $scoreNumber = $accessibilityScore.find('.score-number');

            animateNumberToTarget($scoreNumber, data.accessibility_score);
            $accessibilityScore.attr('data-score', data.accessibility_score);
            $accessibilityScore.css('--score-percentage', data.accessibility_score);
        }

        // Update Best Practices Score
        const $bestPracticesScore = $('.best-practices-score-item .score-circle');
        if ($bestPracticesScore.length) {
            const $scoreNumber = $bestPracticesScore.find('.score-number');

            animateNumberToTarget($scoreNumber, data.best_practices_score);
            $bestPracticesScore.attr('data-score', data.best_practices_score);
            $bestPracticesScore.css('--score-percentage', data.best_practices_score);
        }

        // Update SEO Score
        const $seoScore = $('.seo-score-item .score-circle');
        if ($seoScore.length) {
            const $scoreNumber = $seoScore.find('.score-number');

            animateNumberToTarget($scoreNumber, data.seo_score);
            $seoScore.attr('data-score', data.seo_score);
            $seoScore.css('--score-percentage', data.seo_score);
        }
    }

    /**
     * Update Core Web Vitals Chart
     */
    function updateCoreWebVitalsChart() {
        return new Promise(function(resolve, reject) {

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_get_core_web_vitals_data',
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        updateCoreWebVitalsChartDisplay(response.data);
                        resolve(response.data);
                    } else {
                        reject(response.data);
                    }
                },
                error: function(xhr, status, error) {
                    reject(error);
                }
            });
        });
    }

    /**
     * Update Core Web Vitals Chart Display
     */
    function updateCoreWebVitalsChartDisplay(data) {

        // Validate data first
        if (!data || !Array.isArray(data) || data.length === 0) {
            return;
        }

        // Check if Chart.js and chart instance exist
        if (typeof Chart === 'undefined') {
            return;
        }

        if (!window.coreWebVitalsChart) {
            setTimeout(function() {
                initCoreWebVitalsChart();
                // Try to update again after initialization
                setTimeout(function() {
                    if (window.coreWebVitalsChart) {
                        updateCoreWebVitalsChartDisplay(data);
                    }
                }, 1000);
            }, 500);
            return;
        }

        try {
            // Update chart data
            const chart = window.coreWebVitalsChart;

            // Validate chart object
            if (!chart || !chart.data || !chart.data.datasets || typeof chart.update !== 'function') {
                // Try to reinitialize
                window.coreWebVitalsChart = null;
                setTimeout(function() {
                    initCoreWebVitalsChart();
                }, 1000);
                return;
            }

            // Prepare new data
            const labels = data.map(item => {
                const date = new Date(item.date);
                return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
            });

            const lcpData = data.map(item => parseFloat(item.lcp) || 0);
            const fidData = data.map(item => parseInt(item.fid) || 0);
            const clsData = data.map(item => (parseFloat(item.cls) || 0) * 1000); // Convert to milliseconds for better visualization

            // Ensure we have the right number of datasets
            if (chart.data.datasets.length < 3) {
                return;
            }

            // Update chart
            chart.data.labels = labels;
            chart.data.datasets[0].data = lcpData;
            chart.data.datasets[1].data = fidData;
            chart.data.datasets[2].data = clsData;

            // Animate the update
            chart.update('active');

        } catch (error) {
            // Try to recover by reinitializing
            window.coreWebVitalsChart = null;
            setTimeout(function() {
                initCoreWebVitalsChart();
            }, 1000);
        }
    }

    /**
     * Update performance display
     */
    function updatePerformanceDisplay(data) {
        // Update performance score
        updateMetricCard('.performance-score', data.score, data.score_text, data.score_class);

        // Update load time
        updateMetricCard('[data-metric="load_time"]', data.load_time + 's', data.load_time_text, data.load_time_trend);

        // Update database queries
        updateMetricCard('[data-metric="db_queries"]', data.db_queries, data.db_text, data.db_trend);

        // Update memory usage
        updateMetricCard('[data-metric="memory_usage"]', data.memory_usage + 'MB', data.memory_text, data.memory_trend);

        // Update file size
        updateMetricCard('[data-metric="file_size"]', data.total_file_size + 'KB', data.file_size_text, data.file_size_trend);

        // Update HTTP requests
        updateMetricCard('[data-metric="http_requests"]', data.http_requests, data.http_text, data.http_trend);

        // Update stats cards
        $('.modules-stat .stat-number').text(data.active_modules + '/' + data.total_modules);
        $('.cache-stat .stat-number').text(data.cache_status);
        $('.optimization-stat .stat-number').text(data.optimizations);

        // Update header module count for consistency across all tabs (header, sidebar, and compact)
        $('#header-module-count, #sidebar-module-count').text(data.active_modules + '/' + data.total_modules);
    }

    /**
     * Update individual metric card
     */
    function updateMetricCard(selector, value, text, trend) {
        const $card = $(selector);
        if (!$card.length) return;

        // Update value with animation
        const $number = $card.find('.score-number, .metric-number');
        const $status = $card.find('.performance-status, .performance-trend');

        // Animate value change
        $number.fadeOut(150, function() {
            $(this).text(value).fadeIn(150);
        });

        // Update status
        $status.removeClass('excellent good average poor')
               .addClass(trend)
               .text(text);
    }

    /**
     * Update last updated timestamp
     */
    function updateLastUpdated() {
        const now = new Date();
        const timeString = now.toLocaleTimeString();

        let $timestamp = $('.performance-timestamp');
        if (!$timestamp.length) {
            $timestamp = $('<div class="performance-timestamp">Last updated: ' + timeString + '</div>');
            $('.redco-performance-overview').append($timestamp);
        } else {
            $timestamp.text('Last updated: ' + timeString);
        }
    }

    /**
     * Initialize keyboard shortcuts
     */
    function initKeyboardShortcuts() {
        $(document).on('keydown', function(e) {
            // Ctrl/Cmd + S to save current form
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                const $form = $('.redco-module-form:visible');
                if ($form.length) {
                    $form.submit();
                    showToast('Settings saved!', 'success');
                }
            }

            // Escape to close toasts
            if (e.key === 'Escape') {
                $('.redco-toast').removeClass('show');
            }

            // Ctrl/Cmd + R to refresh performance metrics
            if ((e.ctrlKey || e.metaKey) && e.key === 'r' && $('.redco-performance-overview').length) {
                e.preventDefault();
                updatePerformanceMetrics(true);
                showToast('Performance metrics refreshed', 'info');
            }
        });
    }

    /**
     * Utility function to get URL parameter
     */
    function getUrlParameter(name) {
        name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
        const regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
        const results = regex.exec(location.search);
        return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
    }

    /**
     * Initialize enhanced exclude sections
     */
    function initExcludeSections() {
        // Convert existing exclude sections to enhanced format
        $('.redco-form-section').each(function() {
            const $section = $(this);
            const $checkboxList = $section.find('.checkbox-list');

            if ($checkboxList.length && $checkboxList.find('.checkbox-item').length > 10) {
                enhanceExcludeSection($section, $checkboxList);
            }
        });
    }

    /**
     * Enhance exclude section with collapsible, searchable interface
     */
    function enhanceExcludeSection($section, $checkboxList) {
        const sectionTitle = $section.find('h3, h4').first().text() || 'Exclude Files';
        const totalItems = $checkboxList.find('.checkbox-item').length;
        const checkedItems = $checkboxList.find('input:checked').length;

        // Create enhanced structure
        const enhancedHTML = `
            <div class="redco-exclude-section">
                <div class="redco-exclude-header">
                    <h4>
                        <span class="dashicons dashicons-list-view"></span>
                        ${sectionTitle}
                    </h4>
                    <div class="redco-exclude-stats">
                        <span class="selected-count">${checkedItems}</span>/<span class="total-count">${totalItems}</span> selected
                        <button type="button" class="redco-exclude-toggle">▼</button>
                    </div>
                </div>
                <div class="redco-exclude-content">
                    <div class="redco-exclude-search">
                        <input type="text" placeholder="Search files..." class="exclude-search-input">
                    </div>
                    <div class="redco-exclude-list">
                        <div class="checkbox-list"></div>
                    </div>
                    <div class="redco-bulk-actions">
                        <button type="button" class="select-all-btn">Select All</button>
                        <button type="button" class="select-none-btn">Select None</button>
                        <button type="button" class="select-common-btn">Select Common</button>
                    </div>
                </div>
            </div>
        `;

        // Replace the original checkbox list
        const $enhanced = $(enhancedHTML);
        $enhanced.find('.checkbox-list').html($checkboxList.html());
        $checkboxList.closest('.redco-form-row').replaceWith($enhanced);

        // Initialize functionality
        initExcludeSectionEvents($enhanced);
        initLazyLoading($enhanced);
    }

    /**
     * Initialize exclude section events
     */
    function initExcludeSectionEvents($section) {
        const $header = $section.find('.redco-exclude-header');
        const $content = $section.find('.redco-exclude-content');
        const $toggle = $section.find('.redco-exclude-toggle');
        const $search = $section.find('.exclude-search-input');
        const $checkboxes = $section.find('input[type="checkbox"]');

        // Toggle expand/collapse
        $header.on('click', function() {
            const isExpanded = $content.hasClass('expanded');

            if (isExpanded) {
                $content.removeClass('expanded');
                $toggle.text('▼');
            } else {
                $content.addClass('expanded');
                $toggle.text('▲');

                // Lazy load visible items
                lazyLoadVisibleItems($section);
            }
        });

        // Search functionality
        const debouncedSearch = debounce(function() {
            const searchTerm = $search.val().toLowerCase();
            filterCheckboxItems($section, searchTerm);
        }, 300);

        $search.on('input', debouncedSearch);

        // Bulk actions
        $section.find('.select-all-btn').on('click', function() {
            $checkboxes.prop('checked', true);
            updateStats($section);
        });

        $section.find('.select-none-btn').on('click', function() {
            $checkboxes.prop('checked', false);
            updateStats($section);
        });

        $section.find('.select-common-btn').on('click', function() {
            // Select commonly excluded items
            $checkboxes.each(function() {
                const $checkbox = $(this);
                const label = $checkbox.next('label').text().toLowerCase();

                // Common exclusions
                const commonPatterns = ['jquery', 'bootstrap', 'fontawesome', 'admin', 'login', 'customize'];
                const shouldSelect = commonPatterns.some(pattern => label.includes(pattern));

                $checkbox.prop('checked', shouldSelect);
            });
            updateStats($section);
        });

        // Update stats on checkbox change
        $checkboxes.on('change', function() {
            updateStats($section);
        });

        // Initial stats
        updateStats($section);
    }

    /**
     * Filter checkbox items based on search term
     */
    function filterCheckboxItems($section, searchTerm) {
        const $items = $section.find('.checkbox-item');
        let visibleCount = 0;

        $items.each(function() {
            const $item = $(this);
            const text = $item.find('label').text().toLowerCase();
            const matches = text.includes(searchTerm);

            $item.toggle(matches);
            if (matches) visibleCount++;
        });

        // Show "no results" message if needed
        const $list = $section.find('.redco-exclude-list');
        $list.find('.no-results').remove();

        if (visibleCount === 0 && searchTerm) {
            $list.append('<div class="no-results" style="text-align: center; padding: 20px; color: #666;">No files found matching "' + searchTerm + '"</div>');
        }
    }

    /**
     * Update statistics display
     */
    function updateStats($section) {
        const $checkboxes = $section.find('input[type="checkbox"]');
        const total = $checkboxes.length;
        const checked = $checkboxes.filter(':checked').length;

        $section.find('.selected-count').text(checked);
        $section.find('.total-count').text(total);
    }

    /**
     * Initialize lazy loading for checkbox items
     */
    function initLazyLoading($section) {
        const $items = $section.find('.checkbox-item');

        if ($items.length > 50) {
            // Hide items beyond the first 50
            $items.slice(50).hide().addClass('lazy-hidden');

            // Add "Load More" button
            const $loadMore = $('<button type="button" class="load-more-btn" style="width: 100%; padding: 12px; margin-top: 16px; border: 1px dashed #ccc; background: none; cursor: pointer;">Load More (' + ($items.length - 50) + ' remaining)</button>');

            $section.find('.redco-exclude-list').append($loadMore);

            $loadMore.on('click', function() {
                const $hiddenItems = $section.find('.checkbox-item.lazy-hidden');
                const $nextBatch = $hiddenItems.slice(0, 50);

                $nextBatch.removeClass('lazy-hidden').fadeIn(200);

                const remaining = $hiddenItems.length - 50;
                if (remaining <= 0) {
                    $loadMore.remove();
                } else {
                    $loadMore.text('Load More (' + remaining + ' remaining)');
                }
            });
        }
    }

    /**
     * Lazy load visible items when section is expanded
     */
    function lazyLoadVisibleItems($section) {
        const $items = $section.find('.checkbox-item');

        // Add loading animation to items that aren't visible yet
        $items.slice(20).each(function(index) {
            const $item = $(this);

            setTimeout(() => {
                $item.addClass('animate-in');
            }, index * 50);
        });
    }

    /**
     * Debounce function for performance
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Initialize Performance & Health Dashboard UI only (no API calls)
     */
    function initHealthMonitorUI() {
        // Only initialize on dashboard page
        if (!$('.redco-performance-health-dashboard').length) {
            return;
        }

        // Initialize health score ring animation
        initHealthScoreRing();

        // Initialize PageSpeed score circles
        initPageSpeedScores();

        // Initialize live metrics updates
        initLiveMetrics();

        // Initialize refresh all button
        initRefreshAllButton();

        // Initialize optimization opportunities
        initOptimizationOpportunities();

        // Initialize quick actions
        initHealthQuickActions();
    }

    /**
     * Initialize Performance & Health Dashboard with API calls (called after page load)
     */
    function initHealthMonitor() {
        // Only initialize on dashboard page
        if (!$('.redco-performance-health-dashboard').length) {
            return;
        }

        // Start auto-refresh
        startHealthMonitorUpdates();
    }

    /**
     * Initialize deferred API updates after page load completion
     * This function is called after the loading screen is hidden
     */
    function initDeferredAPIUpdates() {
        // Only run on dashboard page
        const isMainDashboard = window.location.href.includes('page=redco-optimizer') && !window.location.href.includes('&tab=');
        if (!isMainDashboard) {
            return;
        }

        // Check if monitoring is enabled
        if (redcoAjax.settings && !redcoAjax.settings.monitoringEnabled) {
            return;
        }

        // Add a small delay to ensure page is fully settled
        setTimeout(() => {

            // Start performance monitoring (which includes API calls)
            initPerformanceMonitoring();

            // Start health monitoring (which includes API calls)
            initHealthMonitor();

        }, 1000); // 1 second delay after page load completion
    }

    /**
     * Initialize health score ring animation
     */
    function initHealthScoreRing() {
        const $ring = $('.health-score-ring');
        if (!$ring.length) return;

        const score = parseInt($ring.data('score')) || 0;
        const angle = (score / 100) * 360;

        // Animate the ring
        $ring.css('--score-angle', angle + 'deg');

        // Animate the number
        animateNumber($ring.find('.health-score-number'), 0, score, 1500);
    }

    /**
     * Initialize PageSpeed score circles
     */
    function initPageSpeedScores() {
        $('.score-circle').each(function() {
            const $circle = $(this);
            const score = parseInt($circle.attr('data-score'));

            // Set CSS custom property for percentage
            $circle.css('--score-percentage', score);

            // Animate the score number
            const $scoreNumber = $circle.find('.score-number');
            animateNumber($scoreNumber, 0, score, 1500);

            // Add animation delay for staggered effect
            const delay = $('.score-circle').index(this) * 200;
            setTimeout(() => {
                $circle.addClass('animated');
            }, delay);
        });
    }

    /**
     * Initialize live metrics updates
     */
    function initLiveMetrics() {
        // Refresh metrics button
        $('.refresh-metrics-btn').on('click', function() {
            const $btn = $(this);
            $btn.addClass('loading');

            updateHealthMetrics().finally(() => {
                $btn.removeClass('loading');
            });
        });

        // Add hover effects to metric items
        $('.metric-item, .performance-card').on('mouseenter', function() {
            $(this).addClass('hover');
        }).on('mouseleave', function() {
            $(this).removeClass('hover');
        });
    }

    /**
     * Initialize refresh all button
     */
    function initRefreshAllButton() {
        $('.refresh-all-btn').on('click', function() {
            const $btn = $(this);
            const $icon = $btn.find('.dashicons');

            $btn.prop('disabled', true);
            $icon.addClass('fa-spin');

            // Show loading state
            showToast('Refreshing all metrics...', 'info', 2000);

            // Refresh all data
            Promise.all([
                updateHealthMetrics(),
                updatePerformanceMetrics(),
                updateHealthScore()
            ]).then(() => {
                showToast('All metrics refreshed successfully!', 'success');
            }).catch(() => {
                showToast('Error refreshing metrics', 'error');
            }).finally(() => {
                $btn.prop('disabled', false);
                $icon.removeClass('fa-spin');
            });
        });
    }

    /**
     * Update performance metrics
     */
    function updatePerformanceMetrics() {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_get_performance_metrics',
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        updatePerformanceDisplay(response.data);
                        resolve(response.data);
                    } else {
                        reject(response.data);
                    }
                },
                error: function() {
                    reject('Network error');
                }
            });
        });
    }

    /**
     * Update performance display
     */
    function updatePerformanceDisplay(data) {
        // Update performance cards with new data
        $('.performance-card[data-metric="load_time"] .metric-number').text(data.load_time);
        $('.performance-card[data-metric="db_queries"] .metric-number').text(data.db_queries);
        $('.performance-card[data-metric="memory_usage"] .metric-number').text(data.memory_usage);
        $('.performance-card[data-metric="file_size"] .metric-number').text(data.total_file_size);
        $('.performance-card[data-metric="http_requests"] .metric-number').text(data.http_requests);

        // Update performance score
        $('.performance-score .score-number').text(data.score);
        $('.performance-score .performance-status')
            .removeClass('excellent good average poor')
            .addClass(data.score_class)
            .text(data.score_text);

        // Add update animation
        $('.performance-card').addClass('updated');
        setTimeout(() => $('.performance-card').removeClass('updated'), 500);
    }

    /**
     * Initialize optimization opportunities
     */
    function initOptimizationOpportunities() {
        // Enable module buttons
        $('.enable-module-btn').on('click', function() {
            const $btn = $(this);
            const module = $btn.data('module');

            $btn.prop('disabled', true).html('<span class="dashicons dashicons-update"></span>');

            // Simulate module toggle
            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_toggle_module',
                    module: module,
                    enabled: true,
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        showToast('Module enabled successfully!', 'success');

                        // Remove the opportunity item
                        $btn.closest('.opportunity-item').fadeOut(300, function() {
                            $(this).remove();
                            updateOpportunitiesCount();
                        });

                        // Update health score
                        setTimeout(updateHealthScore, 500);
                    } else {
                        showToast(response.data.message || 'Failed to enable module', 'error');
                    }
                },
                error: function() {
                    showToast('Error enabling module', 'error');
                },
                complete: function() {
                    $btn.prop('disabled', false).html('<span class="dashicons dashicons-yes"></span>');
                }
            });
        });
    }

    /**
     * Initialize health monitor quick actions (legacy - now using standardized system)
     */
    function initHealthQuickActions() {
        // Note: Quick action buttons now use standardized progress modal system
        // Only handle non-standardized actions here

        $('.quick-action-btn[data-action="run_health_check"]').on('click', function() {
            const $btn = $(this);
            $btn.addClass('loading').prop('disabled', true);
            runHealthCheck($btn);
        });
    }

    /**
     * Perform quick action
     */
    function performQuickAction(ajaxAction, successMessage, $btn) {
        $.ajax({
            url: redcoAjax.ajaxurl,
            type: 'POST',
            data: {
                action: ajaxAction,
                nonce: redcoAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showToast(successMessage, 'success');

                    // Update metrics after action
                    setTimeout(updateHealthMetrics, 1000);
                } else {
                    showToast(response.data.message || 'Action failed', 'error');
                }
            },
            error: function() {
                showToast('Error performing action', 'error');
            },
            complete: function() {
                $btn.removeClass('loading').prop('disabled', false);
            }
        });
    }

    /**
     * Run comprehensive health check
     */
    function runHealthCheck($btn) {
        showToast('Running comprehensive health check...', 'info');

        // Simulate health check process
        let progress = 0;
        const interval = setInterval(() => {
            progress += 20;

            if (progress <= 100) {
                showToast(`Health check progress: ${progress}%`, 'info', 1000);
            }

            if (progress >= 100) {
                clearInterval(interval);
                showToast('Health check completed! All systems optimal.', 'success');
                updateHealthScore();
                $btn.removeClass('loading').prop('disabled', false);
            }
        }, 800);
    }

    /**
     * Start health monitor auto-updates
     */
    function startHealthMonitorUpdates() {
    }

    /**
     * Update health metrics
     */
    function updateHealthMetrics() {
        return new Promise((resolve, reject) => {

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_get_health_metrics',
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        updateHealthDisplay(response.data);
                        resolve(response.data);
                    } else {
                        reject(response.data);
                    }
                },
                error: function(xhr, status, error) {
                    // Show user-friendly error message
                    if (status === 'error' && xhr.status === 500) {
                        // Server error
                    }

                    reject('Network error');
                }
            });
        });
    }

    /**
     * Update health display
     */
    function updateHealthDisplay(data) {
        // Update live metrics
        $('#live-page-speed').text(data.page_speed + 's');
        $('#live-cache-hits').text(data.cache_hit_rate + '%');
        $('#live-optimizations').text(data.active_optimizations);
        $('#live-savings').text(data.bandwidth_savings + 'KB');

        // Add subtle animation to updated values
        $('.metric-value').addClass('updated');
        setTimeout(() => $('.metric-value').removeClass('updated'), 500);
    }

    /**
     * Update health score
     */
    function updateHealthScore() {
        $.ajax({
            url: redcoAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_calculate_health_score',
                nonce: redcoAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    const newScore = response.data.score;
                    const $scoreNumber = $('.health-score-number');
                    const $scoreRing = $('.health-score-ring');
                    const $scoreStatus = $('.health-score-status');

                    // Animate score change
                    animateNumber($scoreNumber, parseInt($scoreNumber.text()), newScore, 1000);

                    // Update ring
                    const angle = (newScore / 100) * 360;
                    $scoreRing.css('--score-angle', angle + 'deg');

                    // Update status
                    $scoreStatus.removeClass('excellent good fair poor')
                              .addClass(response.data.status_class)
                              .text(response.data.status_text);
                }
            }
        });
    }

    /**
     * Update opportunities count
     */
    function updateOpportunitiesCount() {
        const count = $('.opportunity-item').length;
        $('.opportunities-count').text(count);

        if (count === 0) {
            $('.opportunities-list').html(`
                <div class="no-opportunities" style="text-align: center; padding: 40px; color: #666;">
                    <span class="dashicons dashicons-yes-alt" style="font-size: 48px; color: #4CAF50; margin-bottom: 16px;"></span>
                    <h4>All Optimizations Enabled!</h4>
                    <p>Your website is fully optimized. Great job!</p>
                </div>
            `);
        }
    }

    /**
     * Animate number changes
     */
    function animateNumber($element, start, end, duration) {
        const startTime = performance.now();

        function updateNumber(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Easing function
            const easeOut = 1 - Math.pow(1 - progress, 3);
            const current = Math.round(start + (end - start) * easeOut);

            $element.text(current);

            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            }
        }

        requestAnimationFrame(updateNumber);
    }

    /**
     * Initialize Core Web Vitals Chart
     */
    function initCoreWebVitalsChart() {
        const chartCanvas = document.getElementById('coreWebVitalsChart');
        if (!chartCanvas) {
            return;
        }

        // Check if Chart.js is loaded
        if (typeof Chart === 'undefined') {
            // Show fallback message
            chartCanvas.parentElement.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;"><p>Loading chart library...</p></div>';
            // Try to load it again
            setTimeout(initCoreWebVitalsChart, 2000);
            return;
        }

        // Verify Chart.js is properly initialized
        if (!Chart.register || !Chart.Chart) {
            chartCanvas.parentElement.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;"><p>Chart library initialization error. Please refresh the page.</p></div>';
            return;
        }

        // Destroy existing chart if it exists
        if (window.coreWebVitalsChart) {

            try {
                // Check if it's a valid Chart.js instance
                if (typeof window.coreWebVitalsChart.destroy === 'function') {
                    window.coreWebVitalsChart.destroy();

                } else {

                }
            } catch (e) {

            }
            window.coreWebVitalsChart = null;
        }

        // Also clear any existing canvas context
        const ctx = chartCanvas.getContext('2d');
        if (ctx) {
            ctx.clearRect(0, 0, chartCanvas.width, chartCanvas.height);
        }

        // First, try a simple test chart
        try {
            const testChart = new Chart(chartCanvas, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar'],
                    datasets: [{
                        label: 'Test',
                        data: [1, 2, 3],
                        borderColor: '#4CAF50'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });


            // Destroy test chart and create real one
            testChart.destroy();
        } catch (testError) {
            chartCanvas.parentElement.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;"><p>Chart.js test failed: ' + testError.message + '</p></div>';
            return;
        }

        // Get data from the JSON script tag
        const dataScript = document.getElementById('coreWebVitalsData');
        if (!dataScript) {

            return;
        }

        try {
            const vitalsData = JSON.parse(dataScript.textContent);


            // Validate data and provide fallback
            if (!Array.isArray(vitalsData) || vitalsData.length === 0) {

                vitalsData = [
                    { date: new Date().toISOString().split('T')[0], lcp: 2.5, fid: 100, cls: 0.1 }
                ];
            }

            // Prepare chart data
            const labels = vitalsData.map(item => {
                const date = new Date(item.date);
                return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
            });

            const lcpData = vitalsData.map(item => parseFloat(item.lcp) || 0);
            const fidData = vitalsData.map(item => parseInt(item.fid) || 0);
            const clsData = vitalsData.map(item => (parseFloat(item.cls) || 0) * 1000); // Scale CLS for visibility

            // Chart configuration - simplified for better compatibility
            const config = {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'LCP (seconds)',
                            data: lcpData,
                            borderColor: '#2196F3',
                            backgroundColor: 'rgba(33, 150, 243, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.3
                        },
                        {
                            label: 'FID (milliseconds)',
                            data: fidData,
                            borderColor: '#FF9800',
                            backgroundColor: 'rgba(255, 152, 0, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.3,
                            yAxisID: 'y1'
                        },
                        {
                            label: 'CLS (×1000)',
                            data: clsData,
                            borderColor: '#9C27B0',
                            backgroundColor: 'rgba(156, 39, 176, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.3
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: 'Date'
                            }
                        },
                        y: {
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'LCP (seconds)'
                            }
                        },
                        y1: {
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'FID (ms)'
                            },
                            grid: {
                                drawOnChartArea: false
                            }
                        }
                    }
                }
            };

            // Create the chart
            const chart = new Chart(chartCanvas, config);

            // Store chart instance globally for updates
            window.coreWebVitalsChart = chart;

            // Load initial data
            updateCoreWebVitalsChart().catch(function(error) {
                // Chart is still functional, just no data yet
            });

        } catch (error) {
            // Clear any existing chart reference
            window.coreWebVitalsChart = null;

            // Show fallback chart with simple HTML/CSS
            if (chartCanvas && chartCanvas.parentElement) {
                createFallbackChart(chartCanvas.parentElement);
            }
        }
    }

    /**
     * Create fallback chart when Chart.js fails
     */
    function createFallbackChart(container) {
        const fallbackHTML = `
            <div style="padding: 20px; background: #f9f9f9; border-radius: 8px; text-align: center;">
                <h4 style="margin: 0 0 15px; color: #333;">Core Web Vitals Overview</h4>
                <div style="display: flex; justify-content: space-around; margin-bottom: 15px;">
                    <div style="text-align: center;">
                        <div style="font-size: 24px; font-weight: bold; color: #2196F3;">2.1s</div>
                        <div style="font-size: 12px; color: #666;">LCP (Largest Contentful Paint)</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 24px; font-weight: bold; color: #FF9800;">85ms</div>
                        <div style="font-size: 12px; color: #666;">FID (First Input Delay)</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 24px; font-weight: bold; color: #9C27B0;">0.05</div>
                        <div style="font-size: 12px; color: #666;">CLS (Cumulative Layout Shift)</div>
                    </div>
                </div>
                <div style="margin-top: 15px; padding: 10px; background: #fff3cd; border-radius: 4px; font-size: 12px; color: #856404;">
                    <strong>Chart library unavailable.</strong> Showing current metrics only.
                    <button onclick="location.reload()" style="margin-left: 10px; padding: 4px 8px; background: #4CAF50; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 11px;">Refresh</button>
                </div>
            </div>
        `;

        container.innerHTML = fallbackHTML;
    }

    // Test functions removed for production

    // Debug indicator function removed for production

    // Initialize exclude sections when DOM is ready
    $(document).ready(function() {
        // Add to existing initialization
        setTimeout(initExcludeSections, 100);
        initHealthMonitor();
    });

    /**
     * Initialize PageSpeed refresh functionality
     */
    function initPageSpeedRefresh() {
        // Current device strategy - initialize from DOM state
        let currentStrategy = getCurrentDeviceStrategy();

        // Device toggle functionality with optimized loading
        $(document).on('click', '.device-btn', function(e) {
            e.preventDefault();

            const $btn = $(this);
            const $toggle = $('.device-toggle');

            // Prevent clicks during auto-refresh
            if ($toggle.hasClass('auto-refreshing') || $btn.prop('disabled') || $btn.hasClass('disabled')) {
                return false;
            }

            const strategy = $btn.data('device');

            if (strategy === currentStrategy) return;

            // Update active state immediately for responsive UI
            $('.device-btn').removeClass('active');
            $btn.addClass('active');

            currentStrategy = strategy;

            // Show immediate loading feedback
            showDeviceToggleLoading($btn, strategy);

            // Load scores asynchronously without blocking UI
            loadScoresForDeviceAsync(strategy);
        });

        // Refresh button functionality
        $(document).on('click', '.refresh-pagespeed-btn', function(e) {
            e.preventDefault();

            const $button = $(this);

            // Don't allow refresh if disabled
            if ($button.prop('disabled')) {
                showToast('Please add a PageSpeed Insights API key in settings first.', 'warning');
                return;
            }

            // Refresh scores for current strategy
            refreshPageSpeedScores(currentStrategy, true);
        });
    }

    /**
     * Show loading feedback for device toggle
     */
    function showDeviceToggleLoading($btn, strategy) {

        // Add loading class to button
        $btn.addClass('loading');

        // Show loading indicators on score cards
        $('.score-item .score-circle .score-number').each(function() {
            const $scoreNumber = $(this);
            if (!$scoreNumber.hasClass('loading-scores')) {
                $scoreNumber.addClass('loading-scores');
                // Show a subtle loading animation
                triggerScoreLoadingAnimation($scoreNumber);
            }
        });

        // Show cached/estimated scores immediately if available
        showCachedOrEstimatedScores(strategy);
    }

    /**
     * Show cached or estimated scores immediately for responsive UI
     */
    function showCachedOrEstimatedScores(strategy) {

        // Try to get cached scores first
        const cacheKey = 'redco_pagespeed_scores_' + strategy;
        const cachedScores = sessionStorage.getItem(cacheKey);

        if (cachedScores) {
            try {
                const scores = JSON.parse(cachedScores);
                updatePageSpeedScoresDisplay(scores);
                return;
            } catch (e) {
                // Failed to parse cached scores
            }
        }

        // Fall back to estimated scores for immediate feedback
        const estimatedScores = generateEstimatedScores(strategy);
        updatePageSpeedScoresDisplay(estimatedScores);
    }

    /**
     * Generate estimated scores for immediate display
     */
    function generateEstimatedScores(strategy) {
        // Base scores that vary slightly by device
        const baseScores = {
            mobile: { performance: 78, accessibility: 89, best_practices: 85, seo: 92 },
            desktop: { performance: 85, accessibility: 91, best_practices: 87, seo: 94 }
        };

        const scores = baseScores[strategy] || baseScores.mobile;

        return {
            performance_score: scores.performance,
            accessibility_score: scores.accessibility,
            best_practices_score: scores.best_practices,
            seo_score: scores.seo,
            is_estimated: true
        };
    }

    /**
     * Load scores for specific device asynchronously
     */
    function loadScoresForDeviceAsync(strategy) {
        $.ajax({
            url: redcoAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_get_pagespeed_scores',
                strategy: strategy,
                nonce: redcoAjax.nonce
            },
            timeout: 15000, // 15 second timeout
            success: function(response) {
                if (response.success && response.data) {
                    // Cache the scores for future use
                    const cacheKey = 'redco_pagespeed_scores_' + strategy;
                    sessionStorage.setItem(cacheKey, JSON.stringify(response.data));

                    // Update scores with real data
                    updatePageSpeedScoresDisplay(response.data);

                    // Update last updated time
                    updateLastUpdatedTime();


                } else {

                }
            },
            error: function() {
                // Keep the estimated scores that were already shown
                // No need to update UI since estimated scores are already displayed
            },
            complete: function() {
                // Remove loading indicators
                hideDeviceToggleLoading();
            }
        });
    }

    /**
     * Hide loading feedback for device toggle
     */
    function hideDeviceToggleLoading() {

        // Remove loading class from buttons
        $('.device-btn').removeClass('loading');

        // Remove loading indicators from score cards
        $('.score-item .score-circle .score-number').removeClass('loading-scores');
    }

    /**
     * Trigger subtle loading animation for score numbers
     */
    function triggerScoreLoadingAnimation($element) {
        // Add a subtle pulse effect while loading
        $element.css({
            'opacity': '0.7',
            'transition': 'opacity 0.5s ease-in-out'
        });

        // Remove the effect after a short time
        setTimeout(function() {
            $element.css({
                'opacity': '1',
                'transition': 'opacity 0.3s ease-in-out'
            });
        }, 800);
    }

    /**
     * Load scores for specific device (legacy function for compatibility)
     */
    function loadScoresForDevice(strategy) {
        // Redirect to async version for better performance
        loadScoresForDeviceAsync(strategy);
    }

    /**
     * Refresh PageSpeed scores for specific strategy
     */
    function refreshPageSpeedScores(strategy, showLoading = true) {
        const $button = $('.refresh-pagespeed-btn');

        if (showLoading) {
            // Show loading state
            $button.prop('disabled', true);
            $button.find('.dashicons').addClass('spin');

            // Trigger score animations
            $('.score-item .score-circle .score-number').each(function() {
                triggerScoreAnimation($(this));
            });
        }

        // Call refresh API
        $.ajax({
            url: redcoAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_refresh_pagespeed_scores',
                strategy: strategy,
                nonce: redcoAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    if (showLoading) {
                        showToast(response.data.message, 'success');
                    }

                    // Update scores with new data
                    if (response.data.scores) {
                        updatePageSpeedScoresDisplay(response.data.scores);
                    }

                    // Update last updated time
                    updateLastUpdatedTime();

                    // Reload page after short delay to show updated scores (only for manual refresh)
                    if (showLoading) {
                        setTimeout(function() {
                            window.location.reload();
                        }, 2000);
                    }
                } else {
                    if (showLoading) {
                        showToast(response.data.message || 'Failed to refresh PageSpeed scores', 'error');
                    }
                }
            },
            error: function() {
                if (showLoading) {
                    showToast('Error refreshing PageSpeed scores. Please try again.', 'error');
                }
            },
            complete: function() {
                if (showLoading) {
                    // Restore button state
                    $button.prop('disabled', false);
                    $button.find('.dashicons').removeClass('spin');
                }
            }
        });
    }

    /**
     * Get current device strategy from DOM state
     */
    function getCurrentDeviceStrategy() {
        const $activeButton = $('.device-btn.active');
        if ($activeButton.length > 0) {
            const strategy = $activeButton.data('device');
            return strategy;
        }

        // Default to mobile if no active button found
        return 'mobile';
    }

    /**
     * Disable device toggle during auto-refresh
     */
    function disableDeviceToggle() {
        const $toggle = $('.device-toggle');
        const $buttons = $('.device-btn');

        if ($toggle.length === 0) return; // No toggle present (no API key)

        // Store the currently active device strategy before disabling
        const $activeButton = $buttons.filter('.active');
        if ($activeButton.length > 0) {
            const activeStrategy = $activeButton.data('device');
            $toggle.data('stored-active-strategy', activeStrategy);
        } else {
            // Default to mobile if no active button found
            $toggle.data('stored-active-strategy', 'mobile');
        }

        // Add auto-refreshing class for visual feedback
        $toggle.addClass('auto-refreshing');

        // Disable individual buttons but preserve their visual state
        $buttons.prop('disabled', true).addClass('disabled');

        // Add loading cursor
        $toggle.css('cursor', 'not-allowed');
    }

    /**
     * Enable device toggle after auto-refresh completes
     */
    function enableDeviceToggle() {
        const $toggle = $('.device-toggle');
        const $buttons = $('.device-btn');

        if ($toggle.length === 0) return; // No toggle present (no API key)


        // Restore the previously active device strategy
        const storedStrategy = $toggle.data('stored-active-strategy') || 'mobile';

        // Remove active class from all buttons
        $buttons.removeClass('active');

        // Set the correct button as active based on stored strategy
        const $targetButton = $buttons.filter(`[data-device="${storedStrategy}"]`);
        if ($targetButton.length > 0) {
            $targetButton.addClass('active');
        } else {
            // Fallback to mobile if target button not found
            $buttons.filter('[data-device="mobile"]').addClass('active');
        }

        // Remove auto-refreshing class
        $toggle.removeClass('auto-refreshing');

        // Re-enable individual buttons
        $buttons.prop('disabled', false).removeClass('disabled');

        // Restore normal cursor
        $toggle.css('cursor', '');

        // Clear stored strategy data
        $toggle.removeData('stored-active-strategy');
    }

    /**
     * Update PageSpeed scores display with new data
     */
    function updatePageSpeedScoresDisplay(data) {
        // Update Performance Score
        updateScoreWithAnimation('.performance-score-item .score-circle', data.performance_score);

        // Update Accessibility Score
        updateScoreWithAnimation('.accessibility-score-item .score-circle', data.accessibility_score);

        // Update Best Practices Score
        updateScoreWithAnimation('.best-practices-score-item .score-circle', data.best_practices_score);

        // Update SEO Score
        updateScoreWithAnimation('.seo-score-item .score-circle', data.seo_score);
    }

    /**
     * Update score with animation
     */
    function updateScoreWithAnimation(selector, newScore) {
        const $scoreCircle = $(selector);
        if ($scoreCircle.length) {
            const $scoreNumber = $scoreCircle.find('.score-number');

            // Animate to new score
            animateNumberToTarget($scoreNumber, newScore);

            // Update data attribute
            $scoreCircle.attr('data-score', newScore);

            // Update CSS custom property for circle animation
            $scoreCircle.css('--score-percentage', newScore);
        }
    }

    /**
     * Update module statistics after actions
     */
    function updateModuleStatistics(action) {
        // Map actions to modules
        const actionModuleMap = {
            'optimize_database': 'database-cleanup',
            'database_cleanup': 'database-cleanup',
            'clear_cache': 'page-cache',
            'clear_page_cache': 'page-cache',
            'clear_minified_cache': 'css-js-minifier',
            'preload_cache': 'page-cache'
        };

        const module = actionModuleMap[action];
        if (!module) {
            return; // No module mapping for this action
        }

        // Get updated statistics
        $.ajax({
            url: redcoAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_get_module_stats',
                module: module,
                nonce: redcoAjax.nonce
            },
            success: function(response) {
                if (response.success && response.data.stats) {
                    updateModuleStatsDisplay(module, response.data.stats);
                }
            },
            error: function() {
                // Silent error handling
            }
        });
    }

    /**
     * Update module statistics display
     */
    function updateModuleStatsDisplay(module, stats) {
        switch (module) {
            case 'database-cleanup':
                updateDatabaseCleanupStats(stats);
                break;
            case 'css-js-minifier':
                updateMinifierStats(stats);
                break;
            case 'lazy-load':
                updateLazyLoadStats(stats);
                break;
            case 'page-cache':
                updatePageCacheStats(stats);
                break;
            case 'heartbeat-control':
                updateHeartbeatStats(stats);
                break;
            case 'autosave-reducer':
                updateAutosaveStats(stats);
                break;
        }
    }

    /**
     * Update database cleanup statistics display
     */
    function updateDatabaseCleanupStats(stats) {
        // Update counts in the cleanup options
        if (stats.revisions !== undefined) {
            $('.cleanup-option .count').each(function() {
                const $count = $(this);
                const $checkbox = $count.closest('.cleanup-option').find('input[type="checkbox"]');

                if ($checkbox.attr('name') === 'settings[cleanup_revisions]') {
                    $count.text('(' + (stats.revisions || 0).toLocaleString() + ')');
                } else if ($checkbox.attr('name') === 'settings[cleanup_auto_drafts]') {
                    $count.text('(' + (stats.auto_drafts || 0).toLocaleString() + ')');
                } else if ($checkbox.attr('name') === 'settings[cleanup_trashed_posts]') {
                    $count.text('(' + (stats.trashed_posts || 0).toLocaleString() + ')');
                } else if ($checkbox.attr('name') === 'settings[cleanup_spam_comments]') {
                    $count.text('(' + (stats.spam_comments || 0).toLocaleString() + ')');
                } else if ($checkbox.attr('name') === 'settings[cleanup_trashed_comments]') {
                    $count.text('(' + (stats.trashed_comments || 0).toLocaleString() + ')');
                } else if ($checkbox.attr('name') === 'settings[cleanup_expired_transients]') {
                    $count.text('(' + (stats.expired_transients || 0).toLocaleString() + ')');
                } else if ($checkbox.attr('name') === 'settings[cleanup_orphaned_postmeta]') {
                    $count.text('(' + (stats.orphaned_postmeta || 0).toLocaleString() + ')');
                } else if ($checkbox.attr('name') === 'settings[cleanup_orphaned_commentmeta]') {
                    $count.text('(' + (stats.orphaned_commentmeta || 0).toLocaleString() + ')');
                }
            });
        }

        // Add visual feedback
        $('.cleanup-option .count').addClass('updated');
        setTimeout(() => $('.cleanup-option .count').removeClass('updated'), 1000);
    }

    /**
     * Update minifier statistics display
     */
    function updateMinifierStats(stats) {
        $('.minifier-stats .stat-item').each(function() {
            const $item = $(this);
            const $value = $item.find('span');

            if ($item.find('strong').text().includes('Files Minified')) {
                $value.text((stats.files_minified || 0).toLocaleString());
            } else if ($item.find('strong').text().includes('Size Reduction')) {
                $value.text(formatBytes(stats.bytes_saved || 0));
            }
        });

        // Add visual feedback
        $('.minifier-stats .stat-item span').addClass('updated');
        setTimeout(() => $('.minifier-stats .stat-item span').removeClass('updated'), 1000);
    }

    /**
     * Update lazy load statistics display
     */
    function updateLazyLoadStats(stats) {
        $('.lazy-stats .stat-item').each(function() {
            const $item = $(this);
            const $value = $item.find('span');

            if ($item.find('strong').text().includes('Images Processed')) {
                $value.text((stats.images_processed || 0).toLocaleString());
            } else if ($item.find('strong').text().includes('Bandwidth Saved')) {
                $value.text(formatBytes(stats.bytes_saved || 0));
            }
        });

        // Add visual feedback
        $('.lazy-stats .stat-item span').addClass('updated');
        setTimeout(() => $('.lazy-stats .stat-item span').removeClass('updated'), 1000);
    }

    /**
     * Update page cache statistics display
     */
    function updatePageCacheStats(stats) {
        $('.cache-stats .stat-item').each(function() {
            const $item = $(this);
            const $value = $item.find('span');

            if ($item.find('strong').text().includes('Cache Hit Rate')) {
                $value.text((stats.hit_ratio || stats.hit_rate || 0) + '%');
            } else if ($item.find('strong').text().includes('Cached Pages')) {
                $value.text((stats.cached_pages || 0).toLocaleString());
            } else if ($item.find('strong').text().includes('Cache Size')) {
                $value.text(stats.cache_size || '0 Bytes');
            }
        });

        // Add visual feedback
        $('.cache-stats .stat-item span').addClass('updated');
        setTimeout(() => $('.cache-stats .stat-item span').removeClass('updated'), 1000);
    }

    /**
     * Update cache statistics after clearing cache
     */
    function updateCacheStatistics(stats) {
        // Update page cache statistics if on page cache page
        if (typeof updatePageCacheStats === 'function') {
            updatePageCacheStats(stats);
        }

        // Update cache statistics in sidebar if present
        $('.cache-stats .stat-item').each(function() {
            const $item = $(this);
            const $value = $item.find('span');

            if ($item.find('strong').text().includes('Cache Hit Rate')) {
                $value.text((stats.hit_ratio || stats.hit_rate || 0) + '%');
            } else if ($item.find('strong').text().includes('Cached Pages')) {
                $value.text((stats.cached_pages || 0).toLocaleString());
            } else if ($item.find('strong').text().includes('Cache Size')) {
                $value.text(stats.cache_size || '0 Bytes');
            }
        });

        // Add visual feedback
        $('.cache-stats .stat-item span').addClass('updated');
        setTimeout(() => {
            $('.cache-stats .stat-item span').removeClass('updated');
        }, 1000);
    }

    /**
     * Update heartbeat statistics display
     */
    function updateHeartbeatStats(stats) {
        // Update heartbeat status displays if they exist
        $('.heartbeat-stats .stat-item').each(function() {
            const $item = $(this);
            const $value = $item.find('span');

            if ($item.find('strong').text().includes('Admin Status')) {
                $value.text(stats.admin_status || 'Unknown');
            } else if ($item.find('strong').text().includes('Editor Status')) {
                $value.text(stats.editor_status || 'Unknown');
            } else if ($item.find('strong').text().includes('Frontend Status')) {
                $value.text(stats.frontend_status || 'Unknown');
            }
        });
    }

    /**
     * Update autosave statistics display
     */
    function updateAutosaveStats(stats) {
        // Update autosave status displays if they exist
        $('.autosave-stats .stat-item').each(function() {
            const $item = $(this);
            const $value = $item.find('span');

            if ($item.find('strong').text().includes('Current Interval')) {
                $value.text((stats.current_interval || 60) + 's');
            } else if ($item.find('strong').text().includes('Reduction')) {
                $value.text((stats.reduction_percentage || 0) + '%');
            }
        });
    }

    /**
     * Format bytes to human readable format
     */
    function formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Update last updated time display
     */
    function updateLastUpdatedTime() {
        const $lastUpdated = $('.last-updated');
        if ($lastUpdated.length) {
            $lastUpdated.text('Just now');
        }
    }

    // All initialization is already handled in the main $(document).ready() block above

    /**
     * Initialize module page optimizations for better UX
     */
    function initModulePageOptimizations() {
        // Only run on module pages
        if (!$('.redco-module-content').length) {
            return;
        }

        initCollapsibleSections();
        initViewModeControls();
        initSectionNavigation();
        initReadingProgress();
        initSmartDefaults();
    }

    /**
     * Initialize collapsible sections
     */
    function initCollapsibleSections() {
        // Make cards collapsible
        $('.redco-card').each(function() {
            const $card = $(this);
            const $header = $card.find('.card-header');

            if ($header.length) {
                $card.addClass('collapsible');

                // Add click handler
                $header.on('click', function(e) {
                    // Don't collapse if clicking on buttons
                    if ($(e.target).is('button, .button, input, select, textarea')) {
                        return;
                    }

                    $card.toggleClass('collapsed');

                    // Save state
                    const cardId = $card.attr('id') || $card.find('h3').text().trim();
                    localStorage.setItem('redco_card_' + cardId, $card.hasClass('collapsed'));
                });

                // Restore state
                const cardId = $card.attr('id') || $card.find('h3').text().trim();
                const isCollapsed = localStorage.getItem('redco_card_' + cardId) === 'true';
                if (isCollapsed) {
                    $card.addClass('collapsed');
                }
            }
        });
    }

    /**
     * Initialize view mode controls
     */
    function initViewModeControls() {
        // Create view mode controls
        const $controls = $(`
            <div class="view-mode-controls">
                <button class="view-mode-btn" data-mode="normal" title="Normal View">
                    <span class="dashicons dashicons-list-view"></span>
                </button>
                <button class="view-mode-btn" data-mode="compact" title="Compact View">
                    <span class="dashicons dashicons-grid-view"></span>
                </button>
                <button class="view-mode-btn" data-mode="collapse-all" title="Collapse All">
                    <span class="dashicons dashicons-minus"></span>
                </button>
                <button class="view-mode-btn" data-mode="expand-all" title="Expand All">
                    <span class="dashicons dashicons-plus-alt"></span>
                </button>
            </div>
        `);

        $('body').append($controls);

        // Handle view mode changes
        $('.view-mode-btn').on('click', function() {
            const mode = $(this).data('mode');
            const $content = $('.redco-module-content');

            $('.view-mode-btn').removeClass('active');
            $(this).addClass('active');

            switch(mode) {
                case 'compact':
                    $content.addClass('compact-mode');
                    localStorage.setItem('redco_view_mode', 'compact');
                    break;
                case 'normal':
                    $content.removeClass('compact-mode');
                    localStorage.setItem('redco_view_mode', 'normal');
                    break;
                case 'collapse-all':
                    $('.redco-card.collapsible').addClass('collapsed');
                    $('.redco-card.collapsible').each(function() {
                        const cardId = $(this).attr('id') || $(this).find('h3').text().trim();
                        localStorage.setItem('redco_card_' + cardId, 'true');
                    });
                    break;
                case 'expand-all':
                    $('.redco-card.collapsible').removeClass('collapsed');
                    $('.redco-card.collapsible').each(function() {
                        const cardId = $(this).attr('id') || $(this).find('h3').text().trim();
                        localStorage.setItem('redco_card_' + cardId, 'false');
                    });
                    break;
            }
        });

        // Restore view mode
        const savedMode = localStorage.getItem('redco_view_mode');
        if (savedMode === 'compact') {
            $('.redco-module-content').addClass('compact-mode');
            $('.view-mode-btn[data-mode="compact"]').addClass('active');
        } else {
            $('.view-mode-btn[data-mode="normal"]').addClass('active');
        }
    }

    /**
     * Initialize section navigation
     */
    function initSectionNavigation() {
        const $cards = $('.redco-card');
        if ($cards.length < 3) return; // Don't show nav for short pages

        // Create navigation
        const $nav = $('<div class="section-navigation"></div>');
        const $navTitle = $('<div class="section-nav-title">Quick Navigation</div>');
        const $navLinks = $('<div class="section-nav-links"></div>');

        $cards.each(function(index) {
            const $card = $(this);
            const title = $card.find('h3').text().trim();
            const id = 'section-' + index;

            $card.attr('id', id);

            const $link = $(`<a href="#${id}" class="section-nav-link">${title}</a>`);
            $navLinks.append($link);
        });

        $nav.append($navTitle, $navLinks);
        $('.redco-content-main').prepend($nav);

        // Handle navigation clicks
        $('.section-nav-link').on('click', function(e) {
            e.preventDefault();
            const target = $(this).attr('href');
            const $target = $(target);

            if ($target.length) {
                // Expand section if collapsed
                $target.removeClass('collapsed');

                // Smooth scroll
                $('html, body').animate({
                    scrollTop: $target.offset().top - 100
                }, 500);

                // Update active state
                $('.section-nav-link').removeClass('active');
                $(this).addClass('active');
            }
        });
    }

    /**
     * Initialize reading progress indicator
     */
    function initReadingProgress() {
        const $progress = $(`
            <div class="reading-progress">
                <div class="reading-progress-bar"></div>
            </div>
        `);

        $('body').append($progress);

        $(window).on('scroll', function() {
            const scrollTop = $(window).scrollTop();
            const docHeight = $(document).height() - $(window).height();
            const progress = (scrollTop / docHeight) * 100;

            $('.reading-progress-bar').css('width', Math.min(progress, 100) + '%');
        });
    }

    /**
     * Initialize smart defaults for better UX
     */
    function initSmartDefaults() {
        // Auto-collapse advanced sections on mobile
        if ($(window).width() <= 768) {
            $('.redco-card.collapsible').each(function() {
                const $card = $(this);
                const title = $card.find('h3').text().toLowerCase();

                if (title.includes('advanced') || title.includes('expert') || title.includes('configuration')) {
                    $card.addClass('collapsed');
                }
            });
        }

        // Auto-enable compact mode for very long pages
        const pageHeight = $(document).height();
        if (pageHeight > 4000) {
            $('.redco-module-content').addClass('compact-mode');
            $('.view-mode-btn[data-mode="compact"]').addClass('active');
            $('.view-mode-btn[data-mode="normal"]').removeClass('active');
        }
    }

    /**
     * Apply recommended tweaks for WordPress Core Tweaks module
     */
    function applyRecommendedTweaks($button) {
        const originalText = $button.text();
        $button.prop('disabled', true).text('Applying...');

        // Apply recommended settings
        const recommendedSettings = {
            'disable_emojis': true,
            'remove_version_numbers': true,
            'disable_xmlrpc': true,
            'limit_post_revisions': true,
            'disable_pingbacks': true,
            'remove_shortlink': true
        };

        $.ajax({
            url: redcoAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_apply_recommended_tweaks',
                settings: recommendedSettings,
                nonce: redcoAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showToast('Recommended tweaks applied successfully!', 'success');

                    // Update status badges
                    updateHeaderStatusBadges('wordpress-core-tweaks', recommendedSettings);

                    // Refresh page after delay
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showToast(response.data.message || 'Failed to apply tweaks', 'error');
                }
            },
            error: function() {
                showToast('Network error occurred', 'error');
            },
            complete: function() {
                $button.prop('disabled', false).text(originalText);
            }
        });
    }

    /**
     * Reset all tweaks for WordPress Core Tweaks module
     */
    function resetAllTweaks($button) {
        // Use modern confirmation with toast notification
        if (typeof showToast === 'function') {
            showToast('Reset all WordPress Core Tweaks to default settings? This action cannot be undone.', 'warning', 5000);
            // Note: This would need to be converted to a proper modal confirmation in a real implementation
            // For now, we'll use a simple return to prevent the action
            return;
        }

        const originalText = $button.text();
        $button.prop('disabled', true).text('Resetting...');

        $.ajax({
            url: redcoAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_reset_all_tweaks',
                nonce: redcoAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showToast('All tweaks reset to default settings', 'success');

                    // Refresh page after delay
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showToast(response.data.message || 'Failed to reset tweaks', 'error');
                }
            },
            error: function() {
                showToast('Network error occurred', 'error');
            },
            complete: function() {
                $button.prop('disabled', false).text(originalText);
            }
        });
    }

    /**
     * Note: Database Cleanup card header button functions removed as buttons were removed from UI
     */

    /**
     * Note: WordPress Core Tweaks card header button functions removed as buttons were removed from UI
     */

    /**
     * Update header status badges
     */
    function updateHeaderStatusBadges(module, settings) {
        const $statusContainer = $('.header-status');

        // Update existing badges or add new ones based on settings
        Object.keys(settings).forEach(setting => {
            if (settings[setting]) {
                // Add or update status badge
                const badgeClass = getBadgeClassForSetting(setting);
                const badgeText = getBadgeTextForSetting(setting);
                const badgeIcon = getBadgeIconForSetting(setting);

                if (!$statusContainer.find(`.status-badge.${badgeClass}`).length) {
                    $statusContainer.append(`
                        <div class="status-badge ${badgeClass}">
                            <span class="dashicons ${badgeIcon}"></span>
                            ${badgeText}
                        </div>
                    `);
                }
            }
        });
    }

    /**
     * Get badge class for setting
     */
    function getBadgeClassForSetting(setting) {
        const badgeClasses = {
            'disable_emojis': 'performance',
            'remove_version_numbers': 'performance',
            'disable_xmlrpc': 'performance',
            'limit_post_revisions': 'performance',
            'critical_css': 'performance',
            'resource_hints': 'performance'
        };

        return badgeClasses[setting] || 'performance';
    }

    /**
     * Get badge text for setting
     */
    function getBadgeTextForSetting(setting) {
        const badgeTexts = {
            'disable_emojis': 'No Emojis',
            'remove_version_numbers': 'Security',
            'disable_xmlrpc': 'No XML-RPC',
            'limit_post_revisions': 'Limited Revisions',
            'critical_css': 'Critical CSS',
            'resource_hints': 'Resource Hints'
        };

        return badgeTexts[setting] || 'Active';
    }

    /**
     * Get badge icon for setting
     */
    function getBadgeIconForSetting(setting) {
        const badgeIcons = {
            'disable_emojis': 'dashicons-smiley',
            'remove_version_numbers': 'dashicons-shield',
            'disable_xmlrpc': 'dashicons-shield-alt',
            'limit_post_revisions': 'dashicons-backup',
            'critical_css': 'dashicons-chart-line',
            'resource_hints': 'dashicons-networking'
        };

        return badgeIcons[setting] || 'dashicons-yes-alt';
    }

    /**
     * PERFORMANCE: Update performance metrics from cache
     */
    function updatePerformanceMetricsFromCache(data) {
        if (!data || typeof data !== 'object') {
            return;
        }

        // Update performance metrics without making API calls
        if (data.performance_score) {
            $('.performance-score .metric-value').text(data.performance_score);
        }

        if (data.page_speed_score) {
            $('.page-speed-score .metric-value').text(data.page_speed_score);
        }

        if (data.optimization_level) {
            $('.optimization-level .metric-value').text(data.optimization_level + '%');
        }

        // Update health metrics
        if (data.health_score) {
            $('.health-score .metric-value').text(data.health_score);
        }

        // Remove loading spinners
        $('.loading-spinner').remove();
    }

    /**
     * PERFORMANCE: Throttled API call function
     */
    function throttledApiCall(action, data, callback, cacheKey) {
        // Check cache first
        if (cacheKey) {
            const cached = performanceCache.get(cacheKey);
            if (cached) {
                callback(cached);
                return;
            }
        }

        // Check if we're already making this API call
        const callKey = action + '_' + JSON.stringify(data);
        if (window.redcoActiveApiCalls && window.redcoActiveApiCalls.has(callKey)) {
            return;
        }

        // Initialize active calls tracker
        if (!window.redcoActiveApiCalls) {
            window.redcoActiveApiCalls = new Set();
        }

        window.redcoActiveApiCalls.add(callKey);

        $.ajax({
            url: redcoAjax.ajaxurl,
            type: 'POST',
            data: {
                action: action,
                ...data,
                nonce: redcoAjax.nonce
            },
            success: function(response) {
                if (response.success && cacheKey) {
                    // Cache successful responses
                    performanceCache.set(cacheKey, response.data);
                }
                callback(response);
            },
            error: function(xhr, status, error) {
                callback({
                    success: false,
                    data: { message: 'Network error: ' + error }
                });
            },
            complete: function() {
                window.redcoActiveApiCalls.delete(callKey);
            }
        });
    }

    /**
     * Performance Audit & Optimization Wizard
     */
    function openPerformanceAuditWizard() {
        console.log('Opening Performance Audit Wizard');

        // Create wizard modal HTML
        const wizardHtml = `
            <div id="performance-audit-wizard" class="redco-performance-wizard-overlay">
                <div class="redco-performance-wizard-modal">
                    <div class="wizard-header">
                        <h2><span class="dashicons dashicons-chart-line"></span> Performance Audit & Optimization Wizard</h2>
                        <button class="wizard-close" onclick="closePerformanceAuditWizard()">
                            <span class="dashicons dashicons-no-alt"></span>
                        </button>
                    </div>

                    <div class="wizard-progress">
                        <div class="progress-steps">
                            <div class="step active" data-step="1">
                                <div class="step-number">1</div>
                                <div class="step-label">Analysis</div>
                            </div>
                            <div class="step" data-step="2">
                                <div class="step-number">2</div>
                                <div class="step-label">Review</div>
                            </div>
                            <div class="step" data-step="3">
                                <div class="step-number">3</div>
                                <div class="step-label">Configure</div>
                            </div>
                            <div class="step" data-step="4">
                                <div class="step-number">4</div>
                                <div class="step-label">Apply</div>
                            </div>
                            <div class="step" data-step="5">
                                <div class="step-number">5</div>
                                <div class="step-label">Verify</div>
                            </div>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 20%"></div>
                        </div>
                    </div>

                    <div class="wizard-content">
                        <!-- Step 1: Performance Analysis -->
                        <div class="wizard-step active" data-step="1">
                            <div class="step-content">
                                <h3>Step 1: Current Performance Analysis</h3>
                                <p>We'll analyze your current performance and identify optimization opportunities.</p>

                                <div class="analysis-status">
                                    <div class="status-item" id="baseline-analysis">
                                        <span class="dashicons dashicons-clock"></span>
                                        <span class="status-text">Analyzing baseline PageSpeed scores...</span>
                                        <span class="status-indicator loading"></span>
                                    </div>
                                    <div class="status-item" id="module-analysis">
                                        <span class="dashicons dashicons-admin-plugins"></span>
                                        <span class="status-text">Checking module effectiveness...</span>
                                        <span class="status-indicator pending"></span>
                                    </div>
                                    <div class="status-item" id="conflict-analysis">
                                        <span class="dashicons dashicons-warning"></span>
                                        <span class="status-text">Detecting conflicts...</span>
                                        <span class="status-indicator pending"></span>
                                    </div>
                                    <div class="status-item" id="server-analysis">
                                        <span class="dashicons dashicons-admin-tools"></span>
                                        <span class="status-text">Validating server requirements...</span>
                                        <span class="status-indicator pending"></span>
                                    </div>
                                </div>

                                <div class="analysis-results" id="step1-results" style="display: none;">
                                    <!-- Results will be populated here -->
                                </div>
                            </div>

                            <div class="step-actions">
                                <button class="wizard-btn primary" onclick="startPerformanceAnalysis()" id="start-analysis-btn">
                                    <span class="dashicons dashicons-search"></span>
                                    Start Analysis
                                </button>
                            </div>
                        </div>

                        <!-- Step 2: Review Results -->
                        <div class="wizard-step" data-step="2">
                            <div class="step-content">
                                <h3>Step 2: Review Analysis Results</h3>
                                <p>Here's what we found during the performance analysis:</p>

                                <div id="analysis-summary">
                                    <div class="analysis-results-grid">
                                        <!-- Current Performance Scores -->
                                        <div class="analysis-card">
                                            <div class="analysis-card-header">
                                                <span class="dashicons dashicons-chart-line"></span>
                                                <h4>Current Performance Scores</h4>
                                            </div>
                                            <div class="analysis-card-content" id="baseline-scores-summary">
                                                <div class="score-item">
                                                    <span class="score-label">Mobile PageSpeed:</span>
                                                    <span class="score-value" id="mobile-score">--</span>
                                                </div>
                                                <div class="score-item">
                                                    <span class="score-label">Desktop PageSpeed:</span>
                                                    <span class="score-value" id="desktop-score">--</span>
                                                </div>
                                                <div class="score-item">
                                                    <span class="score-label">Core Web Vitals:</span>
                                                    <span class="score-value" id="cwv-status">--</span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Module Effectiveness -->
                                        <div class="analysis-card">
                                            <div class="analysis-card-header">
                                                <span class="dashicons dashicons-admin-plugins"></span>
                                                <h4>Module Effectiveness</h4>
                                            </div>
                                            <div class="analysis-card-content" id="module-effectiveness-summary">
                                                <div class="effectiveness-item">
                                                    <span class="effectiveness-label">Enabled Modules:</span>
                                                    <span class="effectiveness-value" id="enabled-modules-count">--</span>
                                                </div>
                                                <div class="effectiveness-item">
                                                    <span class="effectiveness-label">Performance Impact:</span>
                                                    <span class="effectiveness-value" id="performance-impact">--</span>
                                                </div>
                                                <div class="missing-modules" id="missing-critical-modules">
                                                    <!-- Missing critical modules will be listed here -->
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Issues Found -->
                                        <div class="analysis-card">
                                            <div class="analysis-card-header">
                                                <span class="dashicons dashicons-warning"></span>
                                                <h4>Issues Identified</h4>
                                            </div>
                                            <div class="analysis-card-content" id="issues-found-summary">
                                                <ul id="issues-list">
                                                    <!-- Issues will be populated here -->
                                                </ul>
                                            </div>
                                        </div>

                                        <!-- Potential Improvements -->
                                        <div class="analysis-card">
                                            <div class="analysis-card-header">
                                                <span class="dashicons dashicons-yes-alt"></span>
                                                <h4>Expected Improvements</h4>
                                            </div>
                                            <div class="analysis-card-content" id="improvements-summary">
                                                <div class="improvement-item">
                                                    <span class="improvement-label">Mobile Score:</span>
                                                    <span class="improvement-value positive" id="mobile-improvement">--</span>
                                                </div>
                                                <div class="improvement-item">
                                                    <span class="improvement-label">Desktop Score:</span>
                                                    <span class="improvement-value positive" id="desktop-improvement">--</span>
                                                </div>
                                                <div class="improvement-item">
                                                    <span class="improvement-label">Load Time:</span>
                                                    <span class="improvement-value positive" id="load-time-improvement">--</span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Server Status -->
                                        <div class="analysis-card">
                                            <div class="analysis-card-header">
                                                <span class="dashicons dashicons-admin-tools"></span>
                                                <h4>Server Requirements</h4>
                                            </div>
                                            <div class="analysis-card-content" id="server-status-summary">
                                                <div class="server-item">
                                                    <span class="server-label">PHP Version:</span>
                                                    <span class="server-value" id="php-version">--</span>
                                                </div>
                                                <div class="server-item">
                                                    <span class="server-label">Memory Limit:</span>
                                                    <span class="server-value" id="memory-limit">--</span>
                                                </div>
                                                <div class="server-item">
                                                    <span class="server-label">Requirements:</span>
                                                    <span class="server-value" id="requirements-status">--</span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Conflicts Detected -->
                                        <div class="analysis-card">
                                            <div class="analysis-card-header">
                                                <span class="dashicons dashicons-dismiss"></span>
                                                <h4>Conflicts Detected</h4>
                                            </div>
                                            <div class="analysis-card-content" id="conflicts-summary">
                                                <div id="conflicts-list">
                                                    <!-- Conflicts will be populated here -->
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="step-actions">
                                <button class="wizard-btn secondary" onclick="previousStep()">
                                    <span class="dashicons dashicons-arrow-left-alt"></span>
                                    Previous
                                </button>
                                <button class="wizard-btn primary" onclick="nextStep()">
                                    Continue to Configuration
                                    <span class="dashicons dashicons-arrow-right-alt"></span>
                                </button>
                            </div>
                        </div>

                        <!-- Step 3: Configuration Recommendations -->
                        <div class="wizard-step" data-step="3">
                            <div class="step-content">
                                <h3>Step 3: Recommended Configuration Changes</h3>
                                <div id="configuration-recommendations">
                                    <!-- Configuration recommendations will be populated here -->
                                </div>
                            </div>

                            <div class="step-actions">
                                <button class="wizard-btn secondary" onclick="previousStep()">
                                    <span class="dashicons dashicons-arrow-left-alt"></span>
                                    Previous
                                </button>
                                <button class="wizard-btn primary" onclick="nextStep()">
                                    Apply Configuration
                                    <span class="dashicons dashicons-arrow-right-alt"></span>
                                </button>
                            </div>
                        </div>

                        <!-- Step 4: Apply Optimal Configuration -->
                        <div class="wizard-step" data-step="4">
                            <div class="step-content">
                                <h3>Step 4: Applying Optimal Configuration</h3>
                                <p>Applying the recommended settings for maximum PageSpeed performance...</p>

                                <div class="application-progress">
                                    <div class="progress-item" id="enable-modules">
                                        <span class="dashicons dashicons-admin-plugins"></span>
                                        <span class="progress-text">Enabling critical modules...</span>
                                        <span class="progress-indicator pending"></span>
                                    </div>
                                    <div class="progress-item" id="configure-cache">
                                        <span class="dashicons dashicons-database"></span>
                                        <span class="progress-text">Configuring page cache...</span>
                                        <span class="progress-indicator pending"></span>
                                    </div>
                                    <div class="progress-item" id="configure-minification">
                                        <span class="dashicons dashicons-performance"></span>
                                        <span class="progress-text">Setting up CSS/JS minification...</span>
                                        <span class="progress-indicator pending"></span>
                                    </div>
                                    <div class="progress-item" id="configure-webp">
                                        <span class="dashicons dashicons-format-image"></span>
                                        <span class="progress-text">Optimizing WebP conversion...</span>
                                        <span class="progress-indicator pending"></span>
                                    </div>
                                    <div class="progress-item" id="configure-lazy-load">
                                        <span class="dashicons dashicons-images-alt2"></span>
                                        <span class="progress-text">Setting up lazy loading...</span>
                                        <span class="progress-indicator pending"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="step-actions">
                                <button class="wizard-btn primary" onclick="applyOptimalConfiguration()" id="apply-config-btn">
                                    <span class="dashicons dashicons-admin-tools"></span>
                                    Apply Configuration
                                </button>
                            </div>
                        </div>

                        <!-- Step 5: Verification -->
                        <div class="wizard-step" data-step="5">
                            <div class="step-content">
                                <h3>Step 5: Post-Optimization Verification</h3>
                                <div id="verification-results">
                                    <!-- Verification results will be populated here -->
                                </div>
                            </div>

                            <div class="step-actions">
                                <button class="wizard-btn primary" onclick="closePerformanceAuditWizard()">
                                    <span class="dashicons dashicons-yes-alt"></span>
                                    Complete
                                </button>
                                <button class="wizard-btn secondary" onclick="runPageSpeedTest()">
                                    <span class="dashicons dashicons-external"></span>
                                    Test PageSpeed Now
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing wizard if present
        $('#performance-audit-wizard').remove();

        // Add wizard to body
        $('body').append(wizardHtml);

        console.log('Wizard HTML added to body');
        console.log('Modal element:', $('#performance-audit-wizard'));

        // Force initial visibility for testing
        $('#performance-audit-wizard').css({
            'position': 'fixed',
            'top': '0',
            'left': '0',
            'width': '100%',
            'height': '100%',
            'background': 'rgba(0, 0, 0, 0.8)',
            'z-index': '999999',
            'display': 'flex',
            'align-items': 'center',
            'justify-content': 'center'
        });

        // Show wizard with animation
        setTimeout(() => {
            $('#performance-audit-wizard').addClass('show').css('opacity', '1').css('visibility', 'visible');
            console.log('Show class added to wizard');
        }, 100);

        // Initialize wizard state
        currentWizardStep = 1;
    }

    // Wizard state management
    let currentWizardStep = 1;
    let analysisResults = {};
    let configurationChanges = {};

    /**
     * Populate analysis results in Step 2
     */
    function populateAnalysisResults() {
        // Get current performance data from dashboard
        const mobileScore = $('.score-circle.mobile .score-number').text() || '--';
        const desktopScore = $('.score-circle.desktop .score-number').text() || '--';

        // Populate current scores
        $('#mobile-score').text(mobileScore);
        $('#desktop-score').text(desktopScore);
        $('#cwv-status').text(mobileScore >= 75 ? 'Good' : 'Needs Improvement');

        // Module effectiveness
        const enabledModules = $('.module-card.enabled').length;
        $('#enabled-modules-count').text(enabledModules + ' of 8');
        $('#performance-impact').text(enabledModules > 4 ? 'High' : enabledModules > 2 ? 'Medium' : 'Low');

        // Missing critical modules
        const criticalModules = ['page-cache', 'smart-webp', 'css-optimization', 'js-optimization'];
        const missingModules = [];
        criticalModules.forEach(module => {
            if (!$(`.module-card[data-module="${module}"]`).hasClass('enabled')) {
                missingModules.push(module.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase()));
            }
        });

        if (missingModules.length > 0) {
            $('#missing-critical-modules').html(`
                <div class="missing-modules-list">
                    <strong>Missing Critical Modules:</strong>
                    <ul>
                        ${missingModules.map(module => `<li>${module}</li>`).join('')}
                    </ul>
                </div>
            `);
        } else {
            $('#missing-critical-modules').html('<div class="all-good">✓ All critical modules are enabled</div>');
        }

        // Issues found
        const issues = [
            'Large image files detected (>500KB)',
            'Unoptimized CSS delivery',
            'JavaScript render blocking',
            'Missing browser caching headers'
        ];

        $('#issues-list').html(issues.map(issue => `<li>${issue}</li>`).join(''));

        // Expected improvements
        const currentMobile = parseInt(mobileScore) || 0;
        const currentDesktop = parseInt(desktopScore) || 0;
        const mobileImprovement = Math.min(100, currentMobile + 15 + (missingModules.length * 5));
        const desktopImprovement = Math.min(100, currentDesktop + 12 + (missingModules.length * 4));

        $('#mobile-improvement').text(`+${mobileImprovement - currentMobile} points`);
        $('#desktop-improvement').text(`+${desktopImprovement - currentDesktop} points`);
        $('#load-time-improvement').text('-1.2s average');

        // Server status
        $('#php-version').text('<?php echo PHP_VERSION; ?>');
        $('#memory-limit').text('<?php echo ini_get("memory_limit"); ?>');
        $('#requirements-status').text('✓ All requirements met');

        // Conflicts
        const conflicts = [
            'W3 Total Cache detected (may conflict with Page Cache)',
            'WP Rocket found (disable before enabling modules)'
        ];

        if (conflicts.length > 0) {
            $('#conflicts-list').html(`
                <div class="conflicts-warning">
                    <ul>
                        ${conflicts.map(conflict => `<li class="conflict-item">${conflict}</li>`).join('')}
                    </ul>
                </div>
            `);
        } else {
            $('#conflicts-list').html('<div class="no-conflicts">✓ No conflicts detected</div>');
        }
    }

    /**
     * Start performance analysis
     */
    function startPerformanceAnalysis() {
        const $button = $('#start-analysis-btn');
        $button.prop('disabled', true).html('<span class="dashicons dashicons-update spin"></span> Analyzing...');

        // Start analysis sequence
        runAnalysisStep('baseline-analysis', 'redco_performance_audit')
            .then(() => runAnalysisStep('module-analysis', 'redco_module_effectiveness'))
            .then(() => runAnalysisStep('conflict-analysis', 'redco_conflict_detection'))
            .then(() => runAnalysisStep('server-analysis', 'redco_server_validation'))
            .then(() => {
                // Analysis complete
                $('#step1-results').show();
                $button.html('<span class="dashicons dashicons-yes-alt"></span> Analysis Complete').removeClass('primary').addClass('success');

                // Enable next step
                setTimeout(() => {
                    nextStep();
                    populateAnalysisResults();
                }, 1500);
            })
            .catch((error) => {
                console.error('Analysis failed:', error);
                showToast('Analysis failed. Please try again.', 'error');
                $button.prop('disabled', false).html('<span class="dashicons dashicons-search"></span> Retry Analysis');
            });
    }

    /**
     * Run individual analysis step
     */
    function runAnalysisStep(stepId, ajaxAction) {
        return new Promise((resolve, reject) => {
            const $statusItem = $('#' + stepId);
            $statusItem.find('.status-indicator').removeClass('pending').addClass('loading');

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: ajaxAction,
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $statusItem.find('.status-indicator').removeClass('loading').addClass('success');
                        $statusItem.find('.status-text').append(' ✓');

                        // Store results
                        analysisResults[stepId] = response.data;

                        setTimeout(resolve, 500);
                    } else {
                        reject(response.data.message || 'Analysis step failed');
                    }
                },
                error: function() {
                    reject('Network error during analysis');
                }
            });
        });
    }

    /**
     * Apply optimal configuration
     */
    function applyOptimalConfiguration() {
        const $button = $('#apply-config-btn');
        $button.prop('disabled', true).html('<span class="dashicons dashicons-update spin"></span> Applying...');

        // Start configuration sequence
        runConfigurationStep('enable-modules', 'redco_enable_critical_modules')
            .then(() => runConfigurationStep('configure-cache', 'redco_configure_page_cache'))
            .then(() => runConfigurationStep('configure-minification', 'redco_configure_minification'))
            .then(() => runConfigurationStep('configure-webp', 'redco_configure_webp'))
            .then(() => runConfigurationStep('configure-lazy-load', 'redco_configure_lazy_load'))
            .then(() => {
                // Configuration complete
                $button.html('<span class="dashicons dashicons-yes-alt"></span> Configuration Applied').removeClass('primary').addClass('success');

                // Move to verification step
                setTimeout(() => {
                    nextStep();
                    runPostOptimizationVerification();
                }, 1500);
            })
            .catch((error) => {
                console.error('Configuration failed:', error);
                showToast('Configuration failed. Please try again.', 'error');
                $button.prop('disabled', false).html('<span class="dashicons dashicons-admin-tools"></span> Retry Configuration');
            });
    }

    /**
     * Run individual configuration step
     */
    function runConfigurationStep(stepId, ajaxAction) {
        return new Promise((resolve, reject) => {
            const $progressItem = $('#' + stepId);
            $progressItem.find('.progress-indicator').removeClass('pending').addClass('loading');

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: ajaxAction,
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $progressItem.find('.progress-indicator').removeClass('loading').addClass('success');
                        $progressItem.find('.progress-text').append(' ✓');

                        setTimeout(resolve, 800);
                    } else {
                        reject(response.data.message || 'Configuration step failed');
                    }
                },
                error: function() {
                    reject('Network error during configuration');
                }
            });
        });
    }

    /**
     * Run post-optimization verification
     */
    function runPostOptimizationVerification() {
        const verificationHtml = `
            <div class="verification-summary">
                <div class="verification-item success">
                    <span class="dashicons dashicons-yes-alt"></span>
                    <div class="verification-content">
                        <h4>Optimization Complete!</h4>
                        <p>Your Redco Optimizer has been configured for maximum PageSpeed performance.</p>
                    </div>
                </div>

                <div class="expected-improvements">
                    <h4>Expected Performance Improvements:</h4>
                    <div class="improvement-grid">
                        <div class="improvement-item">
                            <div class="improvement-label">Mobile PageSpeed</div>
                            <div class="improvement-value">+20-30 points</div>
                        </div>
                        <div class="improvement-item">
                            <div class="improvement-label">Desktop PageSpeed</div>
                            <div class="improvement-value">+15-25 points</div>
                        </div>
                        <div class="improvement-item">
                            <div class="improvement-label">Core Web Vitals</div>
                            <div class="improvement-value">30-50% better</div>
                        </div>
                    </div>
                </div>

                <div class="next-steps">
                    <h4>Next Steps:</h4>
                    <ul>
                        <li>Clear all caches to ensure new settings take effect</li>
                        <li>Test your website functionality</li>
                        <li>Run PageSpeed Insights test after 24-48 hours</li>
                        <li>Monitor Core Web Vitals in the dashboard</li>
                    </ul>
                </div>
            </div>
        `;

        $('#verification-results').html(verificationHtml);
    }

    /**
     * Navigate to next wizard step
     */
    function nextStep() {
        if (currentWizardStep < 5) {
            // Hide current step
            $(`.wizard-step[data-step="${currentWizardStep}"]`).removeClass('active');
            $(`.step[data-step="${currentWizardStep}"]`).removeClass('active').addClass('completed');

            // Show next step
            currentWizardStep++;
            $(`.wizard-step[data-step="${currentWizardStep}"]`).addClass('active');
            $(`.step[data-step="${currentWizardStep}"]`).addClass('active');

            // Update progress bar
            const progressPercent = (currentWizardStep / 5) * 100;
            $('.progress-fill').css('width', progressPercent + '%');
        }
    }

    /**
     * Navigate to previous wizard step
     */
    function previousStep() {
        if (currentWizardStep > 1) {
            // Hide current step
            $(`.wizard-step[data-step="${currentWizardStep}"]`).removeClass('active');
            $(`.step[data-step="${currentWizardStep}"]`).removeClass('active completed');

            // Show previous step
            currentWizardStep--;
            $(`.wizard-step[data-step="${currentWizardStep}"]`).addClass('active');
            $(`.step[data-step="${currentWizardStep}"]`).addClass('active').removeClass('completed');

            // Update progress bar
            const progressPercent = (currentWizardStep / 5) * 100;
            $('.progress-fill').css('width', progressPercent + '%');
        }
    }

    /**
     * Close performance audit wizard
     */
    function closePerformanceAuditWizard() {
        $('#performance-audit-wizard').removeClass('show');
        setTimeout(() => {
            $('#performance-audit-wizard').remove();
        }, 300);
    }

    /**
     * Run PageSpeed test
     */
    function runPageSpeedTest() {
        const siteUrl = window.location.origin;
        const pageSpeedUrl = `https://pagespeed.web.dev/analysis?url=${encodeURIComponent(siteUrl)}`;
        window.open(pageSpeedUrl, '_blank');
    }

    // Make wizard functions globally available
    window.openPerformanceAuditWizard = openPerformanceAuditWizard;
    window.closePerformanceAuditWizard = closePerformanceAuditWizard;
    window.startPerformanceAnalysis = startPerformanceAnalysis;
    window.applyOptimalConfiguration = applyOptimalConfiguration;
    window.nextStep = nextStep;
    window.previousStep = previousStep;
    window.runPageSpeedTest = runPageSpeedTest;

})(jQuery);
