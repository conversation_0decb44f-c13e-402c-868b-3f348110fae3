<?php
/**
 * Performance Fixer for Redco Optimizer
 * 
 * Automatically fixes performance issues causing PageSpeed degradation
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Performance_Fixer {
    
    /**
     * Initialize performance fixes
     */
    public static function init() {
        // Apply critical performance fixes immediately
        add_action('init', array(__CLASS__, 'apply_critical_fixes'), 1);
        
        // Frontend-specific optimizations
        if (!is_admin()) {
            add_action('wp_enqueue_scripts', array(__CLASS__, 'prevent_admin_assets_on_frontend'), 999);
            add_action('wp_head', array(__CLASS__, 'disable_frontend_monitoring'), 1);
        }
        
        // Add AJAX handler for manual fixes
        add_action('wp_ajax_redco_apply_performance_fixes', array(__CLASS__, 'ajax_apply_fixes'));
    }
    
    /**
     * Apply critical performance fixes
     */
    public static function apply_critical_fixes() {
        // 1. Disable performance monitoring on frontend for non-admin users
        if (!is_admin() && !current_user_can('manage_options')) {
            remove_action('init', array('Redco_Performance_Monitor', 'init'));
            remove_action('wp_footer', array('Redco_Performance_Monitor', 'track_page_load_time'));
        }
        
        // 2. Disable security filtering for frontend unless explicitly needed
        if (!is_admin() && !is_user_logged_in()) {
            $security_config = get_option('redco_optimizer_security_config', array());
            if (empty($security_config['enable_request_filtering'])) {
                remove_action('init', array('Redco_Security_Manager', 'filter_malicious_requests'));
                remove_action('wp_loaded', array('Redco_Security_Manager', 'advanced_request_filtering'));
            }
        }
        
        // 3. Optimize module loading for frontend
        if (!is_admin()) {
            add_filter('redco_load_module', array(__CLASS__, 'optimize_module_loading'), 10, 2);
        }
        
        // 4. Remove Chart.js CDN dependency
        add_action('wp_enqueue_scripts', array(__CLASS__, 'remove_external_dependencies'), 999);
        
        // 5. Disable debug features on production
        if (!defined('WP_DEBUG') || !WP_DEBUG) {
            self::disable_debug_features();
        }
    }
    
    /**
     * Prevent admin assets from loading on frontend
     */
    public static function prevent_admin_assets_on_frontend() {
        if (is_admin()) {
            return;
        }
        
        global $wp_scripts, $wp_styles;
        
        // Remove admin scripts
        $admin_script_patterns = array('admin', 'chart-js', 'redco-admin');
        foreach ($admin_script_patterns as $pattern) {
            if (isset($wp_scripts->registered)) {
                foreach ($wp_scripts->registered as $handle => $script) {
                    if (strpos($handle, $pattern) !== false || 
                        (isset($script->src) && strpos($script->src, $pattern) !== false)) {
                        wp_dequeue_script($handle);
                        wp_deregister_script($handle);
                    }
                }
            }
        }
        
        // Remove admin styles
        $admin_style_patterns = array('admin', 'redco-admin', 'enhanced-ui', 'modules-style');
        foreach ($admin_style_patterns as $pattern) {
            if (isset($wp_styles->registered)) {
                foreach ($wp_styles->registered as $handle => $style) {
                    if (strpos($handle, $pattern) !== false || 
                        (isset($style->src) && strpos($style->src, $pattern) !== false)) {
                        wp_dequeue_style($handle);
                        wp_deregister_style($handle);
                    }
                }
            }
        }
    }
    
    /**
     * Disable frontend monitoring
     */
    public static function disable_frontend_monitoring() {
        // Remove performance tracking constants and scripts
        if (defined('REDCO_PAGE_START_TIME')) {
            // Don't output any performance tracking scripts
            remove_action('wp_footer', array('Redco_Performance_Monitor', 'track_page_load_time'));
            remove_action('wp_footer', array('Redco_Performance_Monitor', 'log_performance_data'));
        }
        
        // Disable query tracking
        remove_filter('query', array('Redco_Performance_Monitor', 'track_query'));
    }
    
    /**
     * Optimize module loading
     */
    public static function optimize_module_loading($load_module, $module_key) {
        // Don't load heavy modules on frontend unless specifically needed
        $heavy_modules = array(
            'diagnostic-autofix',
            'css-js-minifier', // Only load if minification is actually enabled
            'critical-resource-optimizer'
        );
        
        if (!is_admin() && in_array($module_key, $heavy_modules)) {
            // Check if module is actually needed for current request
            if ($module_key === 'css-js-minifier') {
                $settings = get_option('redco_optimizer_css_js_minifier', array());
                if (empty($settings['minify_css']) && empty($settings['minify_js'])) {
                    return false;
                }
            }
            
            if ($module_key === 'diagnostic-autofix') {
                // Never load diagnostic module on frontend
                return false;
            }
        }
        
        return $load_module;
    }
    
    /**
     * Remove external dependencies
     */
    public static function remove_external_dependencies() {
        // Remove Chart.js CDN dependency
        wp_dequeue_script('chart-js');
        wp_deregister_script('chart-js');
        
        // Remove any other external CDN resources
        global $wp_scripts;
        if (isset($wp_scripts->registered)) {
            foreach ($wp_scripts->registered as $handle => $script) {
                if (isset($script->src) && 
                    strpos($script->src, 'cdnjs.cloudflare.com') !== false) {
                    wp_dequeue_script($handle);
                }
            }
        }
    }
    
    /**
     * Disable debug features
     */
    private static function disable_debug_features() {
        // Remove debug logging
        remove_action('wp_footer', array('Redco_Error_Handler', 'output_debug_info'));
        
        // Disable performance measurement in modules
        add_filter('redco_enable_performance_measurement', '__return_false');
        
        // Disable verbose logging
        add_filter('redco_enable_verbose_logging', '__return_false');
    }
    
    /**
     * AJAX handler for applying fixes
     */
    public static function ajax_apply_fixes() {
        // Verify nonce and permissions
        if (!wp_verify_nonce($_POST['nonce'], 'redco_performance_fixes') || 
            !current_user_can('manage_options')) {
            wp_die('Security check failed');
        }
        
        $fixes_applied = array();
        
        // 1. Disable frontend monitoring
        $options = get_option('redco_optimizer_options', array());
        $options['disable_frontend_monitoring'] = true;
        update_option('redco_optimizer_options', $options);
        $fixes_applied[] = 'Disabled frontend performance monitoring';
        
        // 2. Optimize security settings
        $security_config = get_option('redco_optimizer_security_config', array());
        $security_config['enable_request_filtering'] = false;
        $security_config['enable_admin_protection'] = false;
        update_option('redco_optimizer_security_config', $security_config);
        $fixes_applied[] = 'Optimized security settings for performance';
        
        // 3. Disable heavy modules on frontend
        $module_settings = get_option('redco_optimizer_module_settings', array());
        $module_settings['disable_heavy_modules_frontend'] = true;
        update_option('redco_optimizer_module_settings', $module_settings);
        $fixes_applied[] = 'Optimized module loading for frontend';
        
        // 4. Clear all caches
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }
        
        // Clear plugin caches
        delete_transient('redco_enabled_modules_frontend');
        delete_transient('redco_asset_versions');
        $fixes_applied[] = 'Cleared all caches';
        
        wp_send_json_success(array(
            'message' => 'Performance fixes applied successfully',
            'fixes_applied' => $fixes_applied,
            'total_fixes' => count($fixes_applied)
        ));
    }
    
    /**
     * Get performance optimization recommendations
     */
    public static function get_recommendations() {
        return array(
            array(
                'title' => 'Disable Frontend Monitoring',
                'description' => 'Remove performance tracking overhead from frontend pages',
                'impact' => 'High',
                'action' => 'disable_frontend_monitoring'
            ),
            array(
                'title' => 'Optimize Security Settings',
                'description' => 'Reduce security filtering overhead for better performance',
                'impact' => 'Medium',
                'action' => 'optimize_security'
            ),
            array(
                'title' => 'Remove External Dependencies',
                'description' => 'Eliminate Chart.js CDN and other external resources',
                'impact' => 'Medium',
                'action' => 'remove_external_deps'
            ),
            array(
                'title' => 'Optimize Module Loading',
                'description' => 'Load only essential modules on frontend',
                'impact' => 'High',
                'action' => 'optimize_modules'
            )
        );
    }
    
    /**
     * Check if performance fixes are needed
     */
    public static function needs_performance_fixes() {
        // Check for common performance issues
        $issues = 0;
        
        // Check if frontend monitoring is enabled
        $options = get_option('redco_optimizer_options', array());
        if (empty($options['disable_frontend_monitoring'])) {
            $issues++;
        }
        
        // Check security settings
        $security_config = get_option('redco_optimizer_security_config', array());
        if (!empty($security_config['enable_request_filtering'])) {
            $issues++;
        }
        
        // Check for external dependencies
        global $wp_scripts;
        if (isset($wp_scripts->registered['chart-js'])) {
            $issues++;
        }
        
        return $issues > 0;
    }
    
    /**
     * Get current performance status
     */
    public static function get_performance_status() {
        $status = array(
            'frontend_monitoring' => 'enabled',
            'security_filtering' => 'enabled',
            'external_dependencies' => 'present',
            'module_optimization' => 'disabled'
        );
        
        $options = get_option('redco_optimizer_options', array());
        if (!empty($options['disable_frontend_monitoring'])) {
            $status['frontend_monitoring'] = 'disabled';
        }
        
        $security_config = get_option('redco_optimizer_security_config', array());
        if (empty($security_config['enable_request_filtering'])) {
            $status['security_filtering'] = 'optimized';
        }
        
        return $status;
    }
}

// Initialize the performance fixer
Redco_Performance_Fixer::init();
